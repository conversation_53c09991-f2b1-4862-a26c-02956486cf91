{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-button/tn-button.vue?9475", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-button/tn-button.vue?a7e4", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-button/tn-button.vue?25b3", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-button/tn-button.vue?3c2f", "uni-app:///tuniao-ui/components/tn-button/tn-button.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-button/tn-button.vue?4658", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-button/tn-button.vue?dffe"], "names": ["mixins", "name", "behaviors", "props", "index", "type", "default", "shape", "shadow", "width", "height", "size", "fontBold", "padding", "margin", "plain", "border", "borderBold", "disabled", "loading", "formType", "openType", "blockRepeatClick", "scene", "blockTime", "computed", "buttonClass", "clazz", "buttonStyle", "style", "data", "watch", "handler", "mounted", "methods", "initScene", "debounceClick", "throttleClick", "emitClick", "handleClick", "handleGetUserInfo", "detail", "handleContact", "handleGetPhoneNumber", "handleError"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAqnB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACazoB;AACA;;;;;;;;;;;;;;AAIA;AAAA,eACA;EACAA;EACAC;EACA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;EACA;EACAmB;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;UACAC;UACA;MAAA;;MAGA;MACA;QACA;UACA;UACAA;QACA;UACAA;QACA;MACA;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;QACAA;QACA;UACAA;UACA;YACAA;UACA;UACA;YACA;YACAA;UACA;QACA;MACA;MAEA;IACA;IACA;IACAC;MACA;MACA;QACA;UACAC;UACAA;UACAA;UACA;QACA;UACAA;UACAA;UACAA;UACA;QACA;UACAA;UACAA;UACAA;MAAA;;MAGA;MACA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;MACAA;MACAA;MAEA;QACAA;MACA;MAEA;QACA;UACAA;QACA;UACAA;QACA;MACA;;MAEA;MACA;QACA;UACAA;QACA,+FACA;UACAA;QACA;MAEA;MAEA;IACA;EACA;EACAC;IACA,QAEA;EACA;EACAC;IACA;IACA;IACAP;MACAQ;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACAlC;MACA;MACA;MACA;QACAA;MACA;IACA;IACA;IACAmC;MACA;QACA;MACA;MACA;MACA;QACA;QACA;MACA;MACA;MACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAC,gDAEA;MAAA;QAAA,mBADAC;QAAAA;MAEA;IACA;IACAC,wCAEA;MAAA;QAAA,qBADAD;QAAAA;MAEA;IACA;IACAE,sDAEA;MAAA;QAAA,qBADAF;QAAAA;MAEA;IACA;IACAG,oCAEA;MAAA;QAAA,qBADAH;QAAAA;MAEA;IACA;EAGA;AACA;AAAA,2B;;;;;;;;;;;;AC9TA;AAAA;AAAA;AAAA;AAAosC,CAAgB,0nCAAG,EAAC,C;;;;;;;;;;;ACAxtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-button/tn-button.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-button.vue?vue&type=template&id=17fe1570&scoped=true&\"\nvar renderjs\nimport script from \"./tn-button.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-button.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-button.vue?vue&type=style&index=0&id=17fe1570&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"17fe1570\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-button/tn-button.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-button.vue?vue&type=template&id=17fe1570&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.buttonStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-button.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-button.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<button class=\"tn-btn-class tn-btn\" :class=\"[\r\n      buttonClass,\r\n      backgroundColorClass,\r\n      fontColorClass\r\n    ]\" :style=\"[buttonStyle]\" hover-class=\"tn-hover\" :loading=\"loading\" :disabled=\"disabled\" :form-type=\"formType\"\r\n\t\t:open-type=\"openType\" @getuserinfo=\"handleGetUserInfo\" @getphonenumber=\"handleGetPhoneNumber\"\r\n\t\t@contact=\"handleContact\" @error=\"handleError\" @tap=\"handleClick\">\r\n\t\t<slot></slot>\r\n\t</button>\r\n</template>\r\n\r\n<script>\r\n\timport componentsColorMixin from '../../libs/mixin/components_color.js'\r\n\timport {\r\n\t\tdebounceFun,\r\n\t\tthrottleFun\r\n\t} from '../../libs/function/applyEven.js'\r\n\tlet spanTime = 200;\r\n\texport default {\r\n\t\tmixins: [componentsColorMixin],\r\n\t\tname: \"tn-button\",\r\n\t\t// 解决再微信小程序种，自定义按钮无法触发bindsubmit\r\n\t\tbehaviors: ['wx://form-field-button'],\r\n\t\tprops: {\r\n\t\t\t// 按钮索引，用于区分多个按钮\r\n\t\t\tindex: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t// 按钮形状 default 默认 round 圆角 icon 图标按钮\r\n\t\t\tshape: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'default'\r\n\t\t\t},\r\n\t\t\t// 是否加阴影\r\n\t\t\tshadow: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 宽度 rpx或%\r\n\t\t\twidth: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'auto'\r\n\t\t\t},\r\n\t\t\t// 高度 rpx或%\r\n\t\t\theight: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 按钮的尺寸 sm lg\r\n\t\t\tsize: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 字体是否加粗\r\n\t\t\tfontBold: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tpadding: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '0 30rpx'\r\n\t\t\t},\r\n\t\t\t// 外边距 与css的margin参数用法相同\r\n\t\t\tmargin: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 是否镂空\r\n\t\t\tplain: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 当plain=true时，是否显示边框\r\n\t\t\tborder: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 当plain=true时，是否加粗显示边框\r\n\t\t\tborderBold: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 是否禁用\r\n\t\t\tdisabled: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 是否显示加载图标\r\n\t\t\tloading: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 触发form表单的事件类型\r\n\t\t\tformType: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 开放能力\r\n\t\t\topenType: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 是否阻止重复点击(默认间隔是200ms)\r\n\t\t\tblockRepeatClick: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t//场景：（如果开启blockRepeatClick，这里无效）none ： 不开启防抖节流模式，debounce ：防抖模式 throttle：节流模式\r\n\t\t\tscene:{\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'none'\r\n\t\t\t},\r\n\t\t\t// 防抖节流间隔时间（毫秒）\r\n\t\t\tblockTime:{\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 200\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 根据不同的参数动态生成class\r\n\t\t\tbuttonClass() {\r\n\t\t\t\tlet clazz = ''\r\n\t\t\t\t// 按钮形状\r\n\t\t\t\tswitch (this.shape) {\r\n\t\t\t\t\tcase 'icon':\r\n\t\t\t\t\tcase 'round':\r\n\t\t\t\t\t\tclazz += ' tn-round'\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 阴影\r\n\t\t\t\tif (this.shadow) {\r\n\t\t\t\t\tif (this.backgroundColorClass !== '' && this.backgroundColorClass.indexOf('tn-bg') != -1) {\r\n\t\t\t\t\t\tconst color = this.backgroundColor.slice(this.backgroundColor.lastIndexOf('-') + 1)\r\n\t\t\t\t\t\tclazz += ` tn-shadow-${color}`\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tclazz += ' tn-shadow-blur'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 字体加粗\r\n\t\t\t\tif (this.fontBold) {\r\n\t\t\t\t\tclazz += ' tn-text-bold'\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 设置为镂空并且设置镂空便可才进行设置\r\n\t\t\t\tif (this.plain) {\r\n\t\t\t\t\tclazz += ' tn-btn--plain'\r\n\t\t\t\t\tif (this.border) {\r\n\t\t\t\t\t\tclazz += ' tn-border-solid'\r\n\t\t\t\t\t\tif (this.borderBold) {\r\n\t\t\t\t\t\t\tclazz += ' tn-bold-border'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (this.backgroundColor !== '' && this.backgroundColor.includes('tn-bg')) {\r\n\t\t\t\t\t\t\tconst color = this.backgroundColor.slice(this.backgroundColor.lastIndexOf('-') + 1)\r\n\t\t\t\t\t\t\tclazz += ` tn-border-${color}`\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn clazz\r\n\t\t\t},\r\n\t\t\t// 按钮的样式\r\n\t\t\tbuttonStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\tswitch (this.size) {\r\n\t\t\t\t\tcase 'sm':\r\n\t\t\t\t\t\tstyle.padding = '0 20rpx'\r\n\t\t\t\t\t\tstyle.fontSize = '22rpx'\r\n\t\t\t\t\t\tstyle.height = this.height || '48rpx'\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tcase 'lg':\r\n\t\t\t\t\t\tstyle.padding = '0 40rpx'\r\n\t\t\t\t\t\tstyle.fontSize = '32rpx'\r\n\t\t\t\t\t\tstyle.height = this.height || '80rpx'\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\tstyle.padding = '0 30rpx'\r\n\t\t\t\t\t\tstyle.fontSize = '28rpx'\r\n\t\t\t\t\t\tstyle.height = this.height || '64rpx'\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 是否手动设置了内边距\r\n\t\t\t\tif (this.padding) {\r\n\t\t\t\t\tstyle.padding = this.padding\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 是否手动设置外边距\r\n\t\t\t\tif (this.margin) {\r\n\t\t\t\t\tstyle.margin = this.margin\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 是否手动设置了字体大小\r\n\t\t\t\tif (this.fontSize) {\r\n\t\t\t\t\tstyle.fontSize = this.fontSize + this.fontUnit\r\n\t\t\t\t}\r\n\t\t\t\tstyle.width = this.shape === 'icon' ? style.height : this.width\r\n\t\t\t\tstyle.padding = this.shape === 'icon' ? '0' : style.padding\r\n\r\n\t\t\t\tif (this.fontColorStyle) {\r\n\t\t\t\t\tstyle.color = this.fontColorStyle\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (!this.backgroundColorClass) {\r\n\t\t\t\t\tif (this.plain) {\r\n\t\t\t\t\t\tstyle.borderColor = this.backgroundColorStyle || '#080808'\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tstyle.backgroundColor = this.backgroundColorStyle || '#FFFFFF'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 设置阴影\r\n\t\t\t\tif (this.shadow && !this.backgroundColorClass) {\r\n\t\t\t\t\tif (this.backgroundColorStyle.indexOf('#') != -1) {\r\n\t\t\t\t\t\tstyle.boxShadow = `6rpx 6rpx 8rpx ${(this.backgroundColorStyle || '#000000')}10`\r\n\t\t\t\t\t} else if (this.backgroundColorStyle.indexOf('rgb') != -1 || this.backgroundColorStyle.indexOf(\r\n\t\t\t\t\t\t'rgba') != -1 || !this.backgroundColorStyle) {\r\n\t\t\t\t\t\tstyle.boxShadow = `6rpx 6rpx 8rpx ${(this.backgroundColorStyle || 'rgba(0, 0, 0, 0.1)')}`\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch:{\r\n\t\t\t//支持动态修改时间，但是这里是没有做撤销上一次的方法，毕竟这种场景非常少\r\n\t\t\t//这里只是防止用户使用时复用了组件，有场景时长要求二次变动，而做的优化\r\n\t\t\tblockTime:{\r\n\t\t\t\thandler(newVal,oldVal){\r\n\t\t\t\t    this.initScene();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.initScene()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinitScene(){\r\n\t\t\t\t// 动态传入blockTime，需要重新初始化,参数\r\n\t\t\t\t//防抖模式\r\n\t\t\t\tthis.debounceClick=debounceFun(function() {\r\n\t\t\t\t\tthis.emitClick();\r\n\t\t\t\t}, this.blockTime);\r\n\t\t\t\t//节流模式\r\n\t\t\t\tthis.throttleClick=throttleFun(function() {\r\n\t\t\t\t\tthis.emitClick();\r\n\t\t\t\t}, this.blockTime);\t\r\n\t\t\t},\r\n\t\t\t//防抖模式\r\n\t\t\tdebounceClick:debounceFun(function() {\r\n\t\t\t\tthis.emitClick();\r\n\t\t\t}, spanTime),\r\n\t\t\t//节流模式\r\n\t\t\tthrottleClick:throttleFun(function() {\r\n\t\t\t\tthis.emitClick();\r\n\t\t\t}, spanTime),\r\n\t\t\temitClick() {\r\n\t\t\t\t//触发事件\r\n\t\t\t\tthis.$emit('click', {\r\n\t\t\t\t\tindex: Number(this.index)\r\n\t\t\t\t})\r\n\t\t\t\t// 兼容tap事件\r\n\t\t\t\tthis.$emit('tap', {\r\n\t\t\t\t\tindex: Number(this.index)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 按钮点击事件\r\n\t\t\thandleClick() {\r\n\t\t\t\tif (this.disabled) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t//兼容旧的\r\n\t\t\t\tif (this.blockRepeatClick) {\r\n\t\t\t\t\tthis.throttleClick();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t//普通模式，触发多少次就回调多少次\r\n\t\t\t\tif(this.scene === 'none'){\r\n\t\t\t\t\tthis.emitClick();\r\n\t\t\t\t}else if(this.scene == 'debounce'){\r\n\t\t\t\t\t//防抖模式\r\n\t\t\t\t\tthis.debounceClick();\r\n\t\t\t\t}else{\r\n\t\t\t\t\t//节流模式\r\n\t\t\t\t\tthis.throttleClick();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thandleGetUserInfo({\r\n\t\t\t\tdetail = {}\r\n\t\t\t} = {}) {\r\n\t\t\t\tthis.$emit('getuserinfo', detail);\r\n\t\t\t},\r\n\t\t\thandleContact({\r\n\t\t\t\tdetail = {}\r\n\t\t\t} = {}) {\r\n\t\t\t\tthis.$emit('contact', detail);\r\n\t\t\t},\r\n\t\t\thandleGetPhoneNumber({\r\n\t\t\t\tdetail = {}\r\n\t\t\t} = {}) {\r\n\t\t\t\tthis.$emit('getphonenumber', detail);\r\n\t\t\t},\r\n\t\t\thandleError({\r\n\t\t\t\tdetail = {}\r\n\t\t\t} = {}) {\r\n\t\t\t\tthis.$emit('error', detail);\r\n\t\t\t},\r\n\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.tn-btn {\r\n\t\tposition: relative;\r\n\t\tdisplay: inline-flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbox-sizing: border-box;\r\n\t\tline-height: 1;\r\n\t\ttext-align: center;\r\n\t\ttext-decoration: none;\r\n\t\toverflow: visible;\r\n\t\ttransform: translate(0rpx, 0rpx);\r\n\t\t// background-color: $tn-mai\r\n\t\tborder-radius: 12rpx;\r\n\t\t// color: $tn-font-color;\r\n\t\tmargin: 0;\r\n\r\n\t\t&--plain {\r\n\t\t\tbackground-color: transparent !important;\r\n\t\t\tbackground-image: none;\r\n\r\n\t\t\t&.tn-round {\r\n\t\t\t\tborder-radius: 1000rpx !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-button.vue?vue&type=style&index=0&id=17fe1570&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-button.vue?vue&type=style&index=0&id=17fe1570&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980406077\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}