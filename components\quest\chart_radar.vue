<template>
	<view class="charts_warp tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-between">
		<view class="top tn-flex tn-flex-row-between tn-flex-col-center">
			<view class="cwt_left tn-flex tn-flex-col-center">
				<view class="dot_word tn-text-bold">
					各版块正确率
				</view>
			</view>
			<view class="cwt_right tn-flex tn-flex-col-center">
				<view>
					<image src="../../static/icon/mine_tips.png" mode="widthFix"
						style="width: 22rpx;margin-right: 5rpx;">
					</image> 我的
				</view>
				<view style="margin-left: 30rpx;">
					<image src="../../static/icon/avg_tips.png" mode="widthFix"
						style="width: 22rpx;margin-right: 5rpx;">
					</image> 平均
				</view>
			</view>
		</view>
		<view class="charts-box">
			<qiun-data-charts tooltipFormat="tooltipDemo2" :canvas2d="true" type="radar" :opts="opts"
				:chartData="chartData" />
		</view>
	</view>
	</view>
</template>

<script>
	export default {
		name: "chart_radar",
		props: {
			chartData: {
				type: Object,
				default: () => {}
			},
			item: {
				type: Object,
				default: () => {}
			},
			name: {
				type: String,
				default: ""
			},
		},
		data() {
			return {
				opts: {
					color: ["#5552FF", "#FF000A"],
					padding: [5, 5, 5, 5],
					dataLabel: false,
					enableScroll: false,
					legend: {
						show: false,
					},
					extra: {
						radar: {
							gridType: "radar",
							gridColor: "#D8D8D8",
							opacity: 0.2,
							max: 100,
							gridCount: 5,
							labelShow: true,
							labelColor: "#999999",
							border: false,
							radius: this.getredius(),
						},
						tooltip: {
							showBox: true,
							borderRadius: 4,
							bgOpacity: 1,
							bgColor: "#DEFAFF",
							fontColor: "#222222",
						}
					},
				}
			}
		},
		methods: {
			getredius() {
				let arr = [1181, 1183, 1256, 1257, 1258, 1260, 1302, 1178, 1258, 1260]
				let scene = uni.getEnterOptionsSync().scene
				console.log("dddddd", scene, arr.indexOf(scene));
				if (arr.indexOf(scene) >= 0) {
					return 100
				} else {
					return 250
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.chart_bottom {
		width: 100%;
		padding: 0rpx 40rpx;
	}

	.cb_w {
		font-size: 24rpx;
		color: #333333;
	}

	.top {
		width: 100%;
		box-sizing: border-box;
		padding-right: 24rpx;
		margin-bottom: 20rpx;
	}

	.dot {
		width: 16rpx;
		height: 16rpx;
		border-radius: 0rpx 8rpx 8rpx 0rpx;
		background-color: #FF000A;
		margin-right: 6rpx;
	}

	.dot_word {
		font-size: 30rpx;
		color: #333333;
	}

	.cwt_right {
		font-size: 26rpx;
		color: #9E9E9E;
	}

	.charts_warp {
		width: 690rpx;
	}

	/* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
	.charts-box {
		width: 690rpx;
		height: 500rpx;
	}
</style>