{"version": 3, "sources": ["webpack:///D:/project/shuati_new/components/zui-progress-bar.vue?3274", "webpack:///D:/project/shuati_new/components/zui-progress-bar.vue?2e03", "webpack:///D:/project/shuati_new/components/zui-progress-bar.vue?a8a9", "webpack:///D:/project/shuati_new/components/zui-progress-bar.vue?cc7d", "uni-app:///components/zui-progress-bar.vue", "webpack:///D:/project/shuati_new/components/zui-progress-bar.vue?97bb", "webpack:///D:/project/shuati_new/components/zui-progress-bar.vue?5e2d"], "names": ["query", "select", "fields", "size", "exec", "resolve", "name", "props", "width", "height", "type", "default", "value", "disable<PERSON><PERSON><PERSON>", "unit", "validator", "rounded", "color", "invertColor", "texture", "preset", "data", "valuePosFix", "computed", "valueFixed", "textureLib", "style", "map", "join", "wrapperStyle", "fixedPos", "barStyle", "barMode", "valueStr", "watch", "mounted", "methods", "fixValuePosition", "Promise", "_getWidth"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACsC;;;AAGrG;AACgK;AAChK,gBAAgB,6KAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8lB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkBlnB;AACA;EACA;IACAA,MACAC,iBACAC;MAAAC;IAAA,GACAC;MACA;MACA;MACA;MACAC;IACA;EACA;AACA;AAAA,eAGA;EACAC;EAEAC;IACA;AACA;AACA;AACA;AACA;IACAC;IAEAC;MACAC;MACAC;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC;IAEA;AACA;AACA;IACAC;IAEAC;MACAJ;MACAC;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAD;MACAA;MACAC;MACAI;QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAC;MACAN;MACAC;IACA;IAEA;AACA;AACA;IACAM;MACAP;MACAC;IACA;IAEA;AACA;AACA;IACAO;IAEA;AACA;AACA;IACAC;MACAT;MACAC;MACAI;QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAK;EACA;EAEAC;IACA;MACAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;QACAN;MACA;QACAA;MACA;QACAA;MACA;MACA;IACA;IAEAO;MACA;MACA;QACAA,cACA;MACA;MACA,0BACAC;QAAA;MAAA,GACAC;IACA;IAEAC;MACA;QACApB;MACA;MAEA;MACAiB;MACAA;MAEAA;MACA;MACA;QACAI;MACA;QACA;QACA;UACAA;QACA;MACA;MACAJ;MACAA;MACAA;MACAA,8DACA,yBACA;MAEAA,wDACA,eACA;MAEA,0BACAC;QAAA;MAAA,GACAC;IACA;IAEAG;MACA;QACAtB,QACA;MACA;MAEA,0BACAkB;QAAA;MAAA,GACAC;IACA;IAEAI;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;EACA;EAEAC;IACAtB;MAAA;MACA;QACA;MACA;IACA;EACA;EAEAuB;IACA;EACA;EAEAC;IACA;AACA;AACA;IACAC;MAAA;MACA;MAGA;MACAC,aACAC,2CACAA,4CACA;QACA;QACA;MACA;IAYA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9PA;AAAA;AAAA;AAAA;AAAqpC,CAAgB,ioCAAG,EAAC,C;;;;;;;;;;;ACAzqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/zui-progress-bar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./zui-progress-bar.vue?vue&type=template&id=beacc0e6&scoped=true&\"\nvar renderjs\nimport script from \"./zui-progress-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./zui-progress-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./zui-progress-bar.vue?vue&type=style&index=0&id=beacc0e6&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"beacc0e6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/zui-progress-bar.vue\"\nexport default component.exports", "export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zui-progress-bar.vue?vue&type=template&id=beacc0e6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zui-progress-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zui-progress-bar.vue?vue&type=script&lang=js&\"", "<template>\n  <view ref=\"elePBar\" class=\"zui-progress-bar\" :style=\"style\">\n    <view\n      :class=\"barMode\"\n      :style=\"wrapperStyle\"\n    >\n      <view class=\"zui-progress-bar-bar\" :style=\"barStyle\">\n        <view class=\"zui-progress-bar-inside\"></view>\n      </view>\n      <view v-if=\"!disableValue\" class=\"zui-progress-bar-value\">{{\n        valueStr\n      }}</view>\n    </view>\n  </view>\n</template>\n\n<script>\n// #ifndef H5\n// 获取元素宽\nconst _getWidth = (query, selector) => {\n  return new Promise((resolve, reject) => {\n    query\n      .select(selector)\n      .fields({ size: true })\n      .exec((rst) => {\n        if (!rst) return resolve(0);\n        if (!rst[0]) return resolve(0);\n        const size = rst.pop();\n        resolve(size.width);\n      });\n  });\n};\n// #endif\n\nexport default {\n  name: \"zui-progress-bar\",\n\n  props: {\n    /**\n     * 宽度\n     *\n     * 默认：100%\n     */\n    width: [String, Number],\n\n    height: {\n      type: Number,\n      default: 12,\n    },\n\n    /**\n     * 进度\n     *\n     * 默认：0\n     */\n    value: Number,\n\n    /**\n     * 不显示值\n     */\n    disableValue: Boolean,\n\n    unit: {\n      type: String,\n      default: \"%\",\n    },\n\n    /**\n     * 样式\n     *\n     * left, inside-left\n     * center, inside-center\n     * right, inside-right\n     * outside-left\n     * outside-right\n     * follow-left\n     * follow-right\n     */\n    type: {\n      type: String,\n      default: \"right\",\n      validator: () => true,\n    },\n\n    /**\n     * 圆角\n     */\n    rounded: {\n      type: Boolean,\n      default: true,\n    },\n\n    /**\n     * 文字颜色\n     */\n    color: {\n      type: [String, Array],\n      default: \"#333\",\n    },\n\n    /**\n     * 开启反色\n     */\n    invertColor: Boolean,\n\n    /**\n     * 颜色纹理\n     */\n    texture: {\n      type: [String, Array],\n      default: undefined,\n      validator: () => true,\n    },\n\n    /**\n     * 预设模式\n     */\n    preset: String,\n  },\n\n  data() {\n    return {\n      valuePosFix: 0,\n    };\n  },\n\n  computed: {\n    valueFixed() {\n      if (this.value < -1) return -1\n      if (this.value > 1) return 1\n      return this.value\n    },\n\n    textureLib() {\n      let texture;\n      if (typeof this.texture === \"string\") {\n        texture = [this.texture, \"transparent\"];\n      } else if (this.texture) {\n        texture = [...this.texture];\n      } else {\n        texture = [\"#4cd964\", \"#FAFAFA\"];\n      }\n      return texture;\n    },\n\n    style() {\n      const style = {};\n      if (this.width) {\n        style.width =\n          typeof this.width === \"string\" ? this.width : `${this.width}px`;\n      }\n      return Object.keys(style)\n        .map((key) => `${key}:${style[key]}`)\n        .join(\";\");\n    },\n\n    wrapperStyle() {\n      const style = {\n        height: `${this.height}px`,\n      };\n\n      const fontSize = this.height > 14 ? this.height - 4 : 10;\n      style[\"font-size\"] = `${fontSize}px`;\n      style[\"--zui-progress-bar-color\"] = this.color;\n\n      style[\"--zui-progress-bar-value\"] = `${this.valueFixed * 100}%`;\n      let fixedPos = this.valueFixed * 100;\n      if (this.type === \"follow-left\" && this.valuePosFix > fixedPos) {\n        fixedPos = this.valuePosFix;\n      } else if (this.type === \"follow-right\") {\n        const pos = 100 - this.valuePosFix;\n        if (fixedPos > pos) {\n          fixedPos = pos;\n        }\n      }\n      style[\"--zui-progress-bar-value-fixed\"] = `${fixedPos}%`;\n      style[\"--zui-progress-bar-fg\"] = this.textureLib[0];\n      style[\"--zui-progress-bar-bg\"] = this.textureLib[1];\n      style[\"--zui-progress-bar-radius\"] = this.rounded\n        ? `${this.height / 2}px`\n        : \"0\";\n\n      style[\"--zui-progress-bar-invert\"] = this.invertColor\n        ? \"difference\"\n        : \"none\";\n\n      return Object.keys(style)\n        .map((key) => `${key}:${style[key]}`)\n        .join(\";\");\n    },\n\n    barStyle() {\n      const style = {\n        height:\n          typeof this.height === \"string\" ? this.height : `${this.height}px`,\n      };\n\n      return Object.keys(style)\n        .map((key) => `${key}:${style[key]}`)\n        .join(\";\");\n    },\n\n    barMode() {\n      const type = this.type.replace(/inside-/, \"\");\n      return ['zui-progress-bar-wrapper', type, this.rounded && \"rounded\"].join(\" \");\n    },\n\n    valueStr() {\n      const val = Math.floor((this.valueFixed || 0) * 1000) / 10;\n      return `${val}${this.unit}`;\n    },\n  },\n\n  watch: {\n    value() {\n      this.$nextTick(() => {\n        this.fixValuePosition();\n      });\n    },\n  },\n\n  mounted() {\n    this.fixValuePosition();\n  },\n\n  methods: {\n    /**\n     * 修复 follow- 模式下文字的位置\n     */\n    fixValuePosition() {\n      if (this.disableValue) return;\n\n      // #ifndef H5\n      const query = uni.createSelectorQuery().in(this);\n      Promise.all([\n        _getWidth(query, \".zui-progress-bar-bar\"),\n        _getWidth(query, \".zui-progress-bar-value\"),\n      ]).then((resp) => {\n        if (!resp[0] || !resp[1]) return;\n        this.valuePosFix = ((resp[1] + 8) / resp[0]) * 100;\n      });\n      // #endif\n\n      // #ifdef H5\n      const dom = this.$refs.elePBar.$el;\n      if (!dom) return;\n      const bar = dom.querySelector(\".zui-progress-bar-bar\");\n      const val = dom.querySelector(\".zui-progress-bar-value\");\n      const barWidthPx = bar.getBoundingClientRect().width;\n      const valueWidthPx = val.getBoundingClientRect().width;\n      this.valuePosFix = (valueWidthPx / barWidthPx) * 100;\n      // #endif\n    },\n  },\n};\n</script>\n\n<style scoped lang=\"scss\">\n.zui-progress-bar {\n  position: relative;\n  width: 100%;\n  margin: 4px 0;\n}\n.zui-progress-bar-wrapper {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.zui-progress-bar-bar {\n  width: 100%;\r\n  background-color: #ffffff;\n  padding: 3rpx 2rpx 2rpx 2rpx;\r\n  box-sizing: border-box;\n}\n.zui-progress-bar-inside {\n  height: 100%;\n  width: var(--zui-progress-bar-value, 0);\n  background: var(--zui-progress-bar-fg, --zui-progress-bar-fg-def);\n}\n\n.zui-progress-bar-bar,\n.zui-progress-bar-inside,\n.zui-progress-bar-value {\n  transition: all 0.1s ease-in-out;\n  background-size: auto 100%;\n}\n.zui-progress-bar-value {\n  color: var(--zui-progress-bar-color);\n  mix-blend-mode: var(--zui-progress-bar-invert, none);\n}\n\n.rounded {\n  .zui-progress-bar-bar,\n  .zui-progress-bar-inside {\n    border-radius: var(--zui-progress-bar-radius, 0);\n    overflow: hidden;\n  }\n}\n\n.left,\n.inside-left {\n  .zui-progress-bar-value {\n    position: absolute;\n    padding: 0 4px;\n    left: 0;\n    top: 50%;\n    transform: translateY(-50%);\n  }\n}\n.center,\n.inside-center {\n  .zui-progress-bar-value {\n    position: absolute;\n    left: 50%;\n    top: 50%;\n    transform: translate(-50%, -50%);\n  }\n}\n.right,\n.inside-right {\n  .zui-progress-bar-value {\n    position: absolute;\n    padding: 0 4px;\n    right: 0;\n    top: 50%;\n    transform: translateY(-50%);\n  }\n}\n.outside-left {\n  flex-direction: row-reverse;\n  .zui-progress-bar-value {\n    margin-right: 4px;\n  }\n}\n.outside-right {\n  .zui-progress-bar-value {\n    margin-left: 4px;\n  }\n}\n\n.follow-left {\n  .zui-progress-bar-value {\n    position: absolute;\n    top: 50%;\n    left: var(--zui-progress-bar-value-fixed);\n    transform: translate(-100%, -50%);\n    padding: 0 4px;\n  }\n}\n.follow-right {\n  .zui-progress-bar-value {\n    position: absolute;\n    top: 50%;\n    left: var(--zui-progress-bar-value-fixed);\n    transform: translate(0, -50%);\n    padding: 0 4px;\n  }\n}\n</style>\n", "import mod from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zui-progress-bar.vue?vue&type=style&index=0&id=beacc0e6&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./zui-progress-bar.vue?vue&type=style&index=0&id=beacc0e6&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980404680\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}