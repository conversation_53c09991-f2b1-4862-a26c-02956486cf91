@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.selTitle.data-v-c646f9e8 {
  font-size: 36rpx !important;
  font-weight: bold;
}
.submit.data-v-c646f9e8 {
  width: 630rpx;
  height: 96rpx;
  border-radius: 48rpx;
  background: #5552FF;
  font-size: 32rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 96rpx;
}
.q_pop_left.data-v-c646f9e8 {
  width: 550rpx;
}
.scroll_warp.data-v-c646f9e8 {
  height: 450rpx;
  padding: 0rpx 40rpx 40rpx 40rpx;
  box-sizing: border-box;
}
.scroll_warp2.data-v-c646f9e8 {
  height: 300rpx;
  padding: 0rpx 40rpx 40rpx 40rpx;
  box-sizing: border-box;
}
.quest_footer.data-v-c646f9e8 {
  width: 100%;
  height: 170rpx;
  border-radius: 20rpx;
  background-color: #FFFFFF;
}
.chart_warp.data-v-c646f9e8 {
  width: 100%;
  background-color: #FFFFFF;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-sizing: border-box;
}
page.data-v-c646f9e8 {
  background-color: #f5f5f5 !important;
}
.m_title.data-v-c646f9e8 {
  min-width: 170rpx;
  display: flex;
  align-items: center;
  color: #333333;
  font-size: 33rpx;
}
.m_titleicon.data-v-c646f9e8 {
  margin-left: 5rpx;
  width: 28rpx;
  height: 28rpx;
}
.model_warp.data-v-c646f9e8 {
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
}
.top.data-v-c646f9e8 {
  width: 100%;
  padding: 10rpx 0rpx 30rpx 0rpx;
  background: url(data:image/png;base64,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) no-repeat;
  background-size: 100% 100%;
}
.t_word.data-v-c646f9e8 {
  width: 540rpx;
}
.t_tips.data-v-c646f9e8 {
  width: 137.5rpx;
  height: 50rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAM0AAABLCAMAAADki/OeAAAAclBMVEUAAABVVf9VUv9VUv9VUf9MQP9VUv9VUv9UUv9UUf9UUf9VUf9VUv9VUP9VVf9VUv9VUv9VUv9UUf9UUf9TUP9QUP9VUv9VUv9VUv9VUv9UU/9VUf9VUf9VUv9UUv9UUf9VUv9VUf9VUv9VUv9VSv9VUv8frpQ8AAAAJXRSTlMADNr7vwTyxmajiEgrIhj3u6ldWEAQ6tG4nE884cuXdY5sVDYYGRm49wAAAX9JREFUaN7d2wmOwjAQRNFyjGOH7PtA2KHvf8URQTCJxByg69/gKbLlSN1YZtrdJopFS+VhkzQPfC+trCjM779Ypo1obUyxzm1FcxeHReZHdHcsFphItBd9OE77l3nmDV7pPjPvEsx1wlGKZ3qv5nWRAbAXlgKASljyQK/yOfO9DI3wdEYiPNU0N9qzEfofNXOlP52bFnp+zv5vOHUGc6K+sXEAODRDAMCiqXvQaGwAaDTxHjwaO4FIcwORZgsijXdEGnsHkWYHIo3NmTQ1mDQpk2ZwTJoaTJorlWai0hRUGjBpLJWmpNJIT6XJqTQdlSZQaRIqTUz16pSOSnOh0sQ9k0bOVJq4YNLIjkojLZUmzpg0ciiYNBLlTBoZMqxSPi9QBixTP8tR5XjFMTlYnv48DDNQtgoPzLHMp/ltSDNDNjuo/+B8OlLN3F4pbrXlPDQm4ejGtEdQM+14jIZp/yYn2o3yBdHeWmKw7q73oo46on3P1uGd5l1cezgmIcOiX8gyCp4PwyglAAAAAElFTkSuQmCC) no-repeat;
  background-size: 100% 100%;
  color: #FFFFFF;
  font-size: 24rpx;
  text-align: center;
  line-height: 50rpx;
}
.top_tips.data-v-c646f9e8 {
  width: 750rpx;
  height: 70rpx;
  background-color: #FFFFFF;
  padding: 0rpx 36rpx;
  box-sizing: border-box;
}

