<!-- 
真题题库
 -->
<template>
	<view class="model_inner">
		<view class="model_inner_top tn-flex tn-flex-col-center tn-flex-row-between">
			<view class="mit_left tn-height-full tn-flex tn-flex-col-center">
				<view class="mitl_word tn-flex tn-flex-col-center">
					<view v-for="(item,index) in bookList" :key="index" style="margin-right: 100rpx;"
						:class="item.id==selCateId?'tn-text-bold':''" @click.stop="selCate(item)">
						{{item.title}}
					</view>
				</view>
			</view>
			<view class="mit_right" @click.stop="openSelBook()">
				更多<text class="tn-icon-right"></text>
			</view>
		</view>
		<tn-tabs :list="list_type" bold :isScroll="false" activeColor="#333333" inactiveColor="#9E9E9E"
			:current="current" name="template_name" @change="change"></tn-tabs>
		<view class="pro_model tn-flex tn-flex-direction-column tn-flex-row-between"
			v-if="Object.keys(bookDetail).length>0">
			<view class="pm_top tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
				<view class="pmt_left tn-color-white">
					<view class="pmtl_title tn-text-bold tn-text-ellipsis">
						{{bookDetail.cover_title||''}}
					</view>
					<view class="pmtl_sub tn-text-sm tn-text-ellipsis">
						当前已作答{{bookDetail.read||0}}题
					</view>
				</view>
				<view class="pmt_right">
					<!-- <tn-circle-progress :percent="bookDetail.correct_ratio" activeColor="#FF707E" :borderWidth="12"
						:width="150">
						<view class="tn-flex tn-flex-direction-column tn-flex-col-center">
							<view class="tn-color-white" style="font-size: 20rpx;">
								{{bookDetail.correct_ratio?bookDetail.correct_ratio+"%":'0%'}}
							</view>
							<view class="tn-color-white" style="font-size: 20rpx;">正确率</view>
						</view>
					</tn-circle-progress> -->

					<chatPro :opts="getOpt(bookDetail)" :chartData="getRatio(bookDetail)"></chatPro>
				</view>
			</view>
			<view class="pro_model_foot tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
				<view class="pro_model_btn tn-flex tn-flex-col-center tn-flex-row-center" @click.stop="toError()">
					<image src="../../static/icon/error_book02.png" mode="widthFix"
						style="width: 40rpx;height: 40rpx;margin-right: 5rpx;">
					</image>
					<view class="">
						错题本
					</view>
				</view>
				<view class="pro_model_btn2" @click.stop="toExercise()">
					立即刷题
				</view>
			</view>
		</view>
		<view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center" style="margin-top: 30rpx;">
			<view class="model_btn tn-flex tn-flex-row-center tn-flex-col-center" @click.stop="toNote()">
				<image src="../../static/icon/note.png" mode="widthFix" style="width: 62rpx;"></image>
				<view style="color: #333333;font-size: 28rpx;">
					笔记
				</view>
			</view>
			<view class="model_btn tn-flex tn-flex-row-center tn-flex-col-center" @click.stop="toHistory()">
				<image src="../../static/icon/history.png" mode="widthFix" style="width: 62rpx;"></image>
				<view style="color: #333333;font-size: 28rpx;">
					答题记录
				</view>
			</view>
			<view class="model_btn tn-flex tn-flex-row-center tn-flex-col-center" @click.stop="toColl()">
				<image src="../../static/icon/collect.png" mode="widthFix" style="width: 62rpx;"></image>
				<view style="color: #333333;font-size: 28rpx;">
					我的收藏
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import zuiProgressBar from "@/components/zui-progress-bar.vue"
	import chatPro from "@/components/chat_pro.vue"
	export default {
		name: "quest_model_tw",
		components: {
			zuiProgressBar,
			chatPro
		},
		data() {
			return {
				list_type: [],
				current: 0,
				selCateId: null,
				selCateItem: {},
				selTemId: null,
				bookDetail: {},
				bookList: []
			};
		},
		methods: {
			getOpt(bookDetail) {
				let ab = bookDetail.correct_ratio ? bookDetail.correct_ratio + "%" : "0%"
				let a = {
					title: {
						name: ab,
						fontSize: 10,
						color: "#ffffff",
						offsetX: 0,
						offsetY: 0
					},
					subtitle: {
						name: "正确率",
						fontSize: 8,
						color: "#ffffff",
						offsetX: 0,
						offsetY: 0
					},
					extra: {
						arcbar: {
							type: "circle",
							width: 8,
							backgroundColor: "#ffffff",
							startAngle: 1.5,
							endAngle: 1.5,
							gap: 2,
							direction: "cw",
							lineCap: "butt",
							centerX: 0,
							centerY: 0,
							linearType: "none"
						}
					}
				}
				return a
			},
			getRatio(bookDetail) {
				let a = bookDetail.correct_ratio ? Number(bookDetail.correct_ratio / 100).toFixed(2) : 0
				return {
					series: [{
						color: "#FF707E",
						data: a
					}]
				}
			},

			toError() {
				this.$emit('toError', {
					book_id: this.bookDetail.book_id,
					template_id: 0,
					name: this.bookDetail.cover_title,
					isselect: 0, // 是否选择分类
				})
			},
			toColl() {
				this.$emit("toColl", this.bookDetail)
			},
			toNote() {
				this.$emit("toNote", this.bookDetail)
			},
			toHistory() {
				this.$emit("toHistory", this.bookDetail)
			},
			toExercise() {
				this.$emit('toExercise', {
					book_id: this.bookDetail.book_id,
					template_id: "0",
					name: this.bookDetail.cover_title
				})
			},
			openSelBook() { //回调选择书籍
				this.$emit("openSelBook")
			},
			selCate(item) {
				this.selCateId = item.id
				this.selCateItem = item
				this.getBookDetail()
			},
			changeBook(item, bookList) { //接收传递数据
				uni.setStorage({
					key: "category_two",
					data: item.id
				})
				this.selCateId = item.id
				this.selCateItem = item
				this.bookList = bookList
				this.getBookDetail()
			},
			getBookDetail() { //获取书籍详情
				this.$http.post(this.$api.bookDetail, {
					template_id: 0,
					exam_cate_id: this.selCateId,
					cate_id: 2,
				}).then(res => {
					if (res.code == 200) {
						this.bookDetail = res.data
						this.completePro = res.data.complete / 100
						this.$emit('changeTem', {
							book_id: this.bookDetail.book_id,
							template_id: 0
						})
					}
				})
			},
			change(index) { //切换模板
				if (this.current != index) {
					this.current = index;
					this.selTemId = this.list_type[this.current].template_id
					this.getBookDetail()
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.model_btn {
		width: 200rpx;
		height: 96rpx;
		border-radius: 20rpx;
		background-color: #FFF6F0;
	}

	.mit_right {
		width: 92rpx;
		height: 34rpx;
		border-radius: 17rpx;
		background: linear-gradient(124deg, #FF9C4C 30%, #FFB200 90%);
		font-size: 24rpx;
		color: #FFFFFF;
		text-align: center;
		padding-left: 10rpx;
		box-sizing: border-box;
		line-height: 34rpx;
	}

	.mit_left {
		font-size: 30rpx;
		color: #FF000A;

		.mitl_word {
			max-width: 430rpx;
		}
	}

	.pro_model_foot {
		margin-top: 30rpx;
		margin-bottom: 10rpx;
	}

	.pm_bottom {
		margin-top: 32rpx;
		margin-bottom: 15rpx;
	}

	.pro_model_btn {
		width: 255rpx;
		height: 96rpx;
		border-radius: 48rpx;
		box-sizing: border-box;
		border: 4rpx solid #FFFFFF;
		background-color: #FF8793;
		color: #ffffff;
		font-size: 28rpx;
		line-height: 96rpx;
	}

	.pro_model_btn2 {
		width: 255rpx;
		height: 96rpx;
		border-radius: 48rpx;
		box-sizing: border-box;
		background-color: #ffffff;
		border: 4rpx solid #ffffff;
		text-align: center;
		line-height: 92rpx;
		color: #FF707E;
		font-size: 28rpx;
	}

	.pmb_pro {
		margin-bottom: 18rpx;
	}

	.pmt_left {
		width: 410rpx;
	}

	.pmtl_title {
		font-size: 32rpx;
	}

	.pmtl_sub {
		margin-top: 16rpx;
	}

	.pro_model {
		width: 650rpx;
		border-radius: 20rpx;
		background-color: #FF707E;
		padding: 30rpx;
		box-sizing: border-box;
		margin-top: 20rpx;
	}

	.model_inner_top {
		width: 650rpx;
		height: 66rpx;
		border-radius: 10rpx;
		background-color: #FDEEE9;
		padding: 0rpx 20rpx;
		box-sizing: border-box;
	}

	.model_inner {
		width: 690rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		margin-top: 36rpx;
		padding: 20rpx;
		box-sizing: border-box;
	}
</style>