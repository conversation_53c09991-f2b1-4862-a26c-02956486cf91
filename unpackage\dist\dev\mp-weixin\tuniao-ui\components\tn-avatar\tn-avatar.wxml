<view data-event-opts="{{[['tap',[['click',['$event']]]]]}}" class="{{['tn-avatar-class','tn-avatar','data-v-27426dd6',backgroundColorClass,fontColorClass,avatarClass]}}" style="{{$root.s0}}" bindtap="__e"><block wx:if="{{showImg}}"><image class="{{['tn-avatar__img','data-v-27426dd6',imgClass]}}" src="{{src}}" mode="{{imgMode||'aspectFill'}}" data-event-opts="{{[['error',[['loadImageError',['$event']]]]]}}" binderror="__e"></image></block><block wx:else><view class="tn-avatar__text data-v-27426dd6"><block wx:if="{{text}}"><view class="data-v-27426dd6">{{text}}</view></block><block wx:else><view class="{{['data-v-27426dd6','tn-icon-'+icon]}}"></view></block></view></block><block wx:if="{{badge&&(badgeIcon||badgeText)}}"><tn-badge vue-id="7de86420-1" radius="{{badgeSize}}" backgroundColor="{{badgeBgColor}}" fontColor="{{badgeColor}}" fontSize="{{badgeSize-8}}" absolute="{{true}}" top="{{badgePosition[0]}}" right="{{badgePosition[1]}}" class="data-v-27426dd6" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{badgeIcon&&badgeText===''}}"><view class="data-v-27426dd6"><view class="{{['data-v-27426dd6','tn-icon-'+badgeIcon]}}"></view></view></block><block wx:else><view class="data-v-27426dd6">{{''+badgeText+''}}</view></block></tn-badge></block></view>