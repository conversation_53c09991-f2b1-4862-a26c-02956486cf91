@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.data-v-bbcc50f6 .uni-input-input:disabled {
  color: #3D3D3D;
}
.ef_sub.data-v-bbcc50f6 {
  width: 690rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #5552FF;
  color: #FFFFFF;
  font-size: 32rpx;
  text-align: center;
  line-height: 80rpx;
}
.efl_top.data-v-bbcc50f6 {
  color: #D0D0D0;
  font-size: 24rpx;
}
.efl_bottom.data-v-bbcc50f6 {
  font-size: 28rpx;
  color: #999999;
}
.error_footer.data-v-bbcc50f6 {
  position: fixed;
  bottom: 0rpx;
  left: 0rpx;
  right: 0rpx;
  width: 750rpx;
  height: 212rpx;
  background-color: #FFFFFF;
  border-top: 1rpx solid #dfdfdf;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}
.submit.data-v-bbcc50f6 {
  width: 630rpx;
  height: 96rpx;
  border-radius: 48rpx;
  background: #5552FF;
  font-size: 16px;
  color: #FFFFFF;
  text-align: center;
  line-height: 96rpx;
}
.q_pop_left.data-v-bbcc50f6 {
  width: 550rpx;
}
.scroll_warp.data-v-bbcc50f6 {
  height: 450rpx;
  padding: 0rpx 40rpx 40rpx 40rpx;
  box-sizing: border-box;
}
.scroll_warp2.data-v-bbcc50f6 {
  height: 300rpx;
  padding: 0rpx 40rpx 40rpx 40rpx;
  box-sizing: border-box;
}
.body.data-v-bbcc50f6 {
  width: 750rpx;
  padding: 0rpx 30rpx;
  padding-bottom: 250rpx;
}
.export_btn.data-v-bbcc50f6 {
  width: 132rpx;
  height: 54rpx;
  border-radius: 27rpx;
  background: linear-gradient(127deg, #FF9C4C 29%, #FFB200 90%);
  font-size: 24rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 54rpx;
}
.tips_warp.data-v-bbcc50f6 {
  width: 750rpx;
  height: 100rpx;
  border-radius: 0rpx 0rpx 20rpx 20rpx;
  background-color: #FFFFFF;
  padding: 0rpx 40rpx 0rpx 10rpx;
  box-sizing: border-box;
}
.top.data-v-bbcc50f6 {
  width: 750rpx;
  height: 100rpx;
  background-color: #FFFFFF;
  padding: 0rpx 40rpx;
  box-sizing: border-box;
}
.top .top_left.data-v-bbcc50f6 {
  color: #222222;
  font-size: 30rpx;
  width: 350rpx;
}
.top .top_right.data-v-bbcc50f6 {
  color: #666666;
  font-size: 28rpx;
}
.top .top_right .tr_word.data-v-bbcc50f6 {
  width: 250rpx;
}

