@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.rib_item.data-v-64e7aaf2 {
  width: 316rpx;
  height: 90rpx;
  border-radius: 20rpx;
  background-color: #FFFFFF;
  margin-top: 20rpx;
  padding: 12rpx 20rpx;
  box-sizing: border-box;
}
.rib_item .ribi_word.data-v-64e7aaf2 {
  width: 195rpx;
  color: #333333;
  font-size: 12px;
  text-align: center;
}
.rember_item_body.data-v-64e7aaf2 {
  padding: 0rpx 20rpx 0rpx 20rpx;
  box-sizing: border-box;
}
.red_line.data-v-64e7aaf2 {
  background-color: #FF585F;
}
.blue_line.data-v-64e7aaf2 {
  background-color: #5552FF;
}
.green_line.data-v-64e7aaf2 {
  background-color: #00864E;
}
.purple_line.data-v-64e7aaf2 {
  background-color: #E552FF;
}
.red.data-v-64e7aaf2 {
  background-color: #FFEAEA;
}
.blue.data-v-64e7aaf2 {
  background-color: #F1F0FF;
}
.green.data-v-64e7aaf2 {
  background-color: #E5F2ED;
}
.purple.data-v-64e7aaf2 {
  background-color: #FAE9FD;
}
.rember_item_top_right.data-v-64e7aaf2 {
  font-size: 24rpx;
  color: #555555;
}
.rember_item_top_left.data-v-64e7aaf2 {
  width: 550rpx;
}
.rember_item.data-v-64e7aaf2 {
  width: 690rpx;
  border-radius: 20rpx;
  padding: 20rpx 0rpx 30rpx 0rpx;
  box-sizing: border-box;
}
.rember_item .rember_item_top.data-v-64e7aaf2 {
  padding-right: 20rpx;
  box-sizing: border-box;
}
.rember_item .rember_item_top .rember_item_line.data-v-64e7aaf2 {
  width: 10rpx;
  height: 68rpx;
  border-radius: 0rpx 10rpx 10rpx 0rpx;
  margin-right: 20rpx;
}
.rember_item .rember_item_top .rember_item_title.data-v-64e7aaf2 {
  width: 510rpx;
  font-size: 36rpx;
  color: #333333;
}

