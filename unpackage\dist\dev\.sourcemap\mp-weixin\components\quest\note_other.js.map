{"version": 3, "sources": ["webpack:///D:/project/shuati_new/components/quest/note_other.vue?1b15", "webpack:///D:/project/shuati_new/components/quest/note_other.vue?4770", "webpack:///D:/project/shuati_new/components/quest/note_other.vue?a0a6", "webpack:///D:/project/shuati_new/components/quest/note_other.vue?d6e4", "uni-app:///components/quest/note_other.vue", "webpack:///D:/project/shuati_new/components/quest/note_other.vue?4977", "webpack:///D:/project/shuati_new/components/quest/note_other.vue?8f22"], "names": ["name", "props", "item", "type", "default", "data", "methods", "toCollect"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAumB,CAAgB,onBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCgC3nB;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA,QAEA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AClDA;AAAA;AAAA;AAAA;AAA0qC,CAAgB,2nCAAG,EAAC,C;;;;;;;;;;;ACA9rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/quest/note_other.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./note_other.vue?vue&type=template&id=6f0b75ea&scoped=true&\"\nvar renderjs\nimport script from \"./note_other.vue?vue&type=script&lang=js&\"\nexport * from \"./note_other.vue?vue&type=script&lang=js&\"\nimport style0 from \"./note_other.vue?vue&type=style&index=0&id=6f0b75ea&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6f0b75ea\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/quest/note_other.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./note_other.vue?vue&type=template&id=6f0b75ea&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./note_other.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./note_other.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"note_model tn-width-full\">\r\n\t\t<view class=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center\">\r\n\t\t\t<view class=\"name\">\r\n\t\t\t\t学员：{{item.user_nick}}\r\n\t\t\t</view>\r\n\t\t\t<view class=\"time\">\r\n\t\t\t\t{{item.create_time}}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"tn-width-full note_inner\">\r\n\t\t\t{{item.content}}\r\n\t\t</view>\r\n\t\t<view class=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center\">\r\n\t\t\t<view class=\"note_f_l tn-flex tn-flex-col-center\" v-if=\"item.type==0\">\r\n\t\t\t\t<view class=\"r_dot\"></view> 题笔记\r\n\t\t\t</view>\r\n\t\t\t<view class=\"note_f_l tn-flex tn-flex-col-center\" style=\"color: #5552FF;\" v-if=\"item.type==1\">\r\n\t\t\t\t<view class=\"r_dot\" style=\"background-color: #5552FF;\"></view> 章笔记\r\n\t\t\t</view>\r\n\t\t\t<view class=\"note_f_r tn-flex tn-flex-col-center\" @click.stop=\"toCollect()\">\r\n\t\t\t\t<image src=\"../../static/icon/no_coll.png\" mode=\"widthFix\" style=\"width: 32rpx;margin-right: 10rpx;\">\r\n\t\t\t\t</image>\r\n\t\t\t\t<view style=\"color: #666666;font-size: 28rpx;\">\r\n\t\t\t\t\t采纳<text v-if=\"item.accept_num>0\">-{{item.accept_num}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: \"note_other\",\r\n\t\tprops: {\r\n\t\t\titem: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {}\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttoCollect() {\r\n\t\t\t\tthis.$emit(\"toCollect\", this.item)\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.note_f_l {\r\n\t\tcolor: #5552FF;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.b_dot {\r\n\t\twidth: 14rpx;\r\n\t\theight: 14rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground-color: #5552FF;\r\n\t\tmargin-right: 8rpx;\r\n\t}\r\n\r\n\t.note_inner {\r\n\t\tmargin-top: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.name {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.time {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.note_model {\r\n\t\tpadding: 30rpx 0rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-bottom: 2rpx solid #F7F7F7;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./note_other.vue?vue&type=style&index=0&id=6f0b75ea&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./note_other.vue?vue&type=style&index=0&id=6f0b75ea&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980404513\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}