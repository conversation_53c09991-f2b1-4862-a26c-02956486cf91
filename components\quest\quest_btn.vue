<template>
	<view class="tn-height-full tn-width-full tn-flex tn-flex-row-between tn-flex-col-center"
		style="padding: 0rpx 50rpx;">
		<view class="tn-flex tn-flex-direction-column tn-flex-col-center" style="min-width: 100rpx;"
			@click.stop="toNote">
			<image src="../../static/icon/note.png" mode="widthFix" style="width: 62rpx;"></image>
			<view style="color: #333333;font-size: 28rpx;">
				笔记
			</view>
		</view>
		<view class="tn-flex tn-flex-direction-column tn-flex-col-center" style="min-width: 100rpx;"
			@click.stop="toColl()">
			<image src="../../static/icon/collect.png" mode="widthFix" style="width: 62rpx;"></image>
			<view style="color: #333333;font-size: 28rpx;">
				我的收藏
			</view>
		</view>
		<view class="tn-flex tn-flex-direction-column tn-flex-col-center" style="min-width: 100rpx;" v-if="type!=3"
			@click.stop="toCheck()">
			<image src="../../static/icon/answer.png" mode="widthFix" style="width: 62rpx;"></image>
			<view style="color: #333333;font-size: 28rpx;">
				答案速查
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "quest_btn",
		props: {
			type: {
				type: Number,
				default: 1
			},
		},
		data() {
			return {

			};
		},
		methods: {
			toColl() {
				this.$emit("toColl")
			},
			toNote() {
				this.$emit("toNote")
			},
			toCheck() {
				this.$emit("toCheck")
			},
		}
	}
</script>

<style lang="scss">

</style>