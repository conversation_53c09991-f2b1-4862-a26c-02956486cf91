<view class="data-v-8c52024e"><tn-tabs vue-id="fded4566-1" list="{{temList}}" isScroll="{{false}}" activeColor="#222222" inactiveColor="#666666" current="{{current}}" name="template_name" data-event-opts="{{[['^change',[['change']]]]}}" bind:change="__e" class="data-v-8c52024e" bind:__l="__l"></tn-tabs><view class="body data-v-8c52024e"><block wx:for="{{reciteChapterList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="body_item data-v-8c52024e"><rember-dot vue-id="{{'fded4566-2-'+index}}" item="{{item}}" index="{{index}}" selIndex="{{selIndex}}" data-event-opts="{{[['^showFold',[['showFold']]],['^noShowFold',[['noShowFold']]],['^toRember',[['toRember']]]]}}" bind:showFold="__e" bind:noShowFold="__e" bind:toRember="__e" class="data-v-8c52024e" bind:__l="__l"></rember-dot></view></block></view></view>