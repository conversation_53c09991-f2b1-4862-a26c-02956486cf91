<script>
	export default {
		onLaunch: function() {
			// uni.setStorage({
			// 	key: "TOKEN",
			// 	data: 'oyU6A7dAoFs_n-1vRh7wRHqa94eI'
			// 	// data: '123456789'
			// })
			// #ifdef APP-PLUS
			// this.getversion()
			// #endif

			// #ifdef MP-WEIXIN
			this.updateManagerfun();
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
		},
		onShow: function() {
			console.log('App Show')
			// #ifdef MP-WEIXIN
			uni.login({
				provider: 'weixin',
				success: (suc) => {
					this.$http.post(this.$api.login, {
						code: suc.code
					}).then(res => {
						if (res.code == 200) {
							uni.setStorage({
								key: "TOKEN",
								data: res.data
								// data: "oyU6A7UHYmTsNOMmVB95qwXgArks"
							})
						}
					})
				}
			});
			// #endif
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			updateManagerfun() { // 更新小程序
				const updateManager = uni.getUpdateManager();
				updateManager.onCheckForUpdate(res => {
					if (res.hasUpdate) {
						uni.showModal({
							content: '新版本已经准备好，是否重启应用？',
							showCancel: false,
							confirmText: '确定',
							success: res => {
								if (res.confirm) {
									updateManager.onUpdateReady(function(res) {
										updateManager.applyUpdate();
									});
									updateManager.onUpdateFailed(res => { // 新版本下载失败的回调
										// 新版本下载失败，提示用户删除后通过冷启动重新打开
										uni.showModal({
											content: '下载失败，请删除当前小程序后重新打开',
											showCancel: false,
											confirmText: '知道了'
										})
									})
								}
							}
						})
					}
				})
			},
			//更新app
			getversion() {
				plus.runtime.getProperty(plus.runtime.appid, (wgtinfo) => {
					this.$http.post(this.$api.version).then(res => {
						if (res.code == 1) {
							if (wgtinfo.version == res.data.newversion) {} else {
								uni.reLaunch({
									url: "/pages/fullscreen/fullscreen"
								})
							}
						}
					})
				})
			},
		}
	}
</script>
<style lang="scss">
	/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
	@import './tuniao-ui/index.scss';
	@import './tuniao-ui/iconfont.css';

	image {
		@include cssimg;
	}
</style>
<style>
	/*每个页面公共css */
	page {
		background-color: #f7f7f7;
	}
</style>