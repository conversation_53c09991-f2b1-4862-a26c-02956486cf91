<view class="model_inner data-v-1298888f"><view class="model_inner_top tn-flex tn-flex-col-center tn-flex-row-between data-v-1298888f"><view data-event-opts="{{[['tap',[['openSelBook',['$event']]]]]}}" class="mit_left tn-height-full tn-flex tn-flex-col-center tn-text-bold data-v-1298888f" catchtap="__e"><view class="mitl_word tn-text-ellipsis data-v-1298888f">{{''+(selBookItem.title||'')+''}}</view><image style="height:16rpx;width:28rpx;" src="../../static/icon/down.png" mode="heightFix" class="data-v-1298888f"></image></view></view><block wx:if="{{$root.g0>0}}"><view class="pro_model tn-flex tn-flex-direction-column tn-flex-row-between data-v-1298888f"><view class="pm_top tn-width-full tn-flex tn-flex-row-between tn-flex-col-center data-v-1298888f"><view class="pmt_left tn-color-white data-v-1298888f"><view class="pmtl_title tn-text-bold tn-text-ellipsis data-v-1298888f">{{''+(bookDetail.cover_title||'')+''}}</view><view class="pmtl_sub tn-text-sm tn-text-ellipsis data-v-1298888f">{{''+(bookDetail.study_title?'正在学习：'+bookDetail.study_title:'')+''}}</view></view><view class="pmt_right data-v-1298888f"><chat-pro vue-id="40790a57-1" opts="{{$root.m0}}" chartData="{{$root.m1}}" class="data-v-1298888f" bind:__l="__l"></chat-pro></view></view><view class="pm_bottom tn-width-full data-v-1298888f"><view class="pmb_pro data-v-1298888f"><zui-progress-bar vue-id="40790a57-2" height="{{16}}" texture="linear-gradient(180deg, #E3E3FF 2%, #5552FF 100%)" disableValue="{{true}}" value="{{completePro}}" class="data-v-1298888f" bind:__l="__l"></zui-progress-bar></view><view class="pmb_word tn-flex tn-flex-col-center tn-flex-row-between data-v-1298888f"><view class="tn-color-white tn-text-sm data-v-1298888f">{{'已刷/总题：'+(bookDetail.read||0)+"/"+(bookDetail.total||0)+''}}</view><view class="tn-color-white tn-text-sm data-v-1298888f">{{'已完成'+(bookDetail.complete||0)+'%'}}</view></view></view></view></block><block wx:if="{{$root.g1>0}}"><view class="pro_model_foot tn-width-full tn-flex tn-flex-row-between tn-flex-col-center data-v-1298888f"><view data-event-opts="{{[['tap',[['toError']]]]}}" class="pro_model_btn tn-flex tn-flex-col-center tn-flex-row-center data-v-1298888f" catchtap="__e"><image style="width:40rpx;height:40rpx;" src="../../static/icon/error_book.png" mode="widthFix" class="data-v-1298888f"></image>错题本</view><view data-event-opts="{{[['tap',[['toExercise']]]]}}" class="pro_model_btn2 data-v-1298888f" catchtap="__e">立即刷题</view></view></block></view>