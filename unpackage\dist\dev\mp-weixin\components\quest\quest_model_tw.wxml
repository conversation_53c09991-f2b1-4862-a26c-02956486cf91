<view class="model_inner data-v-136be91e"><view class="model_inner_top tn-flex tn-flex-col-center tn-flex-row-between data-v-136be91e"><view class="mit_left tn-height-full tn-flex tn-flex-col-center data-v-136be91e"><view class="mitl_word tn-flex tn-flex-col-center data-v-136be91e"><block wx:for="{{bookList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selCate',['$0'],[[['bookList','',index]]]]]]]}}" class="{{['data-v-136be91e',item.id==selCateId?'tn-text-bold':'']}}" style="margin-right:100rpx;" catchtap="__e">{{''+item.title+''}}</view></block></view></view><view data-event-opts="{{[['tap',[['openSelBook']]]]}}" class="mit_right data-v-136be91e" catchtap="__e">更多<text class="tn-icon-right data-v-136be91e"></text></view></view><tn-tabs vue-id="5a11bba8-1" list="{{list_type}}" bold="{{true}}" isScroll="{{false}}" activeColor="#333333" inactiveColor="#9E9E9E" current="{{current}}" name="template_name" data-event-opts="{{[['^change',[['change']]]]}}" bind:change="__e" class="data-v-136be91e" bind:__l="__l"></tn-tabs><block wx:if="{{$root.g0>0}}"><view class="pro_model tn-flex tn-flex-direction-column tn-flex-row-between data-v-136be91e"><view class="pm_top tn-width-full tn-flex tn-flex-row-between tn-flex-col-center data-v-136be91e"><view class="pmt_left tn-color-white data-v-136be91e"><view class="pmtl_title tn-text-bold tn-text-ellipsis data-v-136be91e">{{''+(bookDetail.cover_title||'')+''}}</view><view class="pmtl_sub tn-text-sm tn-text-ellipsis data-v-136be91e">{{'当前已作答'+(bookDetail.read||0)+'题'}}</view></view><view class="pmt_right data-v-136be91e"><chat-pro vue-id="5a11bba8-2" opts="{{$root.m0}}" chartData="{{$root.m1}}" class="data-v-136be91e" bind:__l="__l"></chat-pro></view></view><view class="pro_model_foot tn-width-full tn-flex tn-flex-row-between tn-flex-col-center data-v-136be91e"><view data-event-opts="{{[['tap',[['toError']]]]}}" class="pro_model_btn tn-flex tn-flex-col-center tn-flex-row-center data-v-136be91e" catchtap="__e"><image style="width:40rpx;height:40rpx;margin-right:5rpx;" src="../../static/icon/error_book02.png" mode="widthFix" class="data-v-136be91e"></image><view class="data-v-136be91e">错题本</view></view><view data-event-opts="{{[['tap',[['toExercise']]]]}}" class="pro_model_btn2 data-v-136be91e" catchtap="__e">立即刷题</view></view></view></block><view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center data-v-136be91e" style="margin-top:30rpx;"><view data-event-opts="{{[['tap',[['toNote']]]]}}" class="model_btn tn-flex tn-flex-row-center tn-flex-col-center data-v-136be91e" catchtap="__e"><image style="width:62rpx;" src="../../static/icon/note.png" mode="widthFix" class="data-v-136be91e"></image><view style="color:#333333;font-size:28rpx;" class="data-v-136be91e">笔记</view></view><view data-event-opts="{{[['tap',[['toHistory']]]]}}" class="model_btn tn-flex tn-flex-row-center tn-flex-col-center data-v-136be91e" catchtap="__e"><image style="width:62rpx;" src="../../static/icon/history.png" mode="widthFix" class="data-v-136be91e"></image><view style="color:#333333;font-size:28rpx;" class="data-v-136be91e">答题记录</view></view><view data-event-opts="{{[['tap',[['toColl']]]]}}" class="model_btn tn-flex tn-flex-row-center tn-flex-col-center data-v-136be91e" catchtap="__e"><image style="width:62rpx;" src="../../static/icon/collect.png" mode="widthFix" class="data-v-136be91e"></image><view style="color:#333333;font-size:28rpx;" class="data-v-136be91e">我的收藏</view></view></view></view>