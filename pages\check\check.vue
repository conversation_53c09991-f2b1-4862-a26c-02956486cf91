<template>
	<view class="content">
		<tn-swiper :list="list" mode="none" :height="160" :radius="20"></tn-swiper>
		<view class="body tn-flex tn-flex-row-between tn-flex-wrap">
			<view v-for="(item,index) in goodsList" :key="index" style="margin-bottom: 20rpx;">
				<product-item :item="item" @toDetail="toDetail"></product-item>
			</view>
		</view>
		<tn-popup v-model="showKefu" mode="bottom" :borderRadius="40">
			<view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				客服帮助</view>
			<view class="tn-width-full tn-flex tn-flex-direction-column tn-flex-col-center"
				style="padding-bottom: 40rpx;">
				<view class="tips">
					解锁后务必添加
				</view>
				<view class="tips_word">
					<view class="tn-flex">
						<view class="">
							1 开通确认
						</view>
						<view class="" style="width: 60rpx;"></view>
						<view class="">
							2 上新通知
						</view>
					</view>
					<view class="tn-flex" style="margin-top: 20rpx;">
						<view class="">
							3 最新干货
						</view>
						<view class="" style="width: 60rpx;"></view>
						<view class="">
							4 考研答疑
						</view>
					</view>
				</view>
				<view class="img_warp">
					<image style="width: 100%;height: 100%;" :src="baseUrl+service_img" mode="" show-menu-by-longpress>
					</image>
				</view>
				<view class="" style="color: #5552FF;font-size: 24rpx;">
					长按识别
				</view>
			</view>
		</tn-popup>
		<image src="../../static/icon/zixun.png" mode="widthFix" class="fab_btn" @click.stop="showKefu=true"></image>
	</view>
</template>

<script>
	import productItem from "@/components/product_item.vue"
	import shareMixin from "@/mixins/shareMixin.js"
	export default {
		mixins: [shareMixin],
		components: {
			productItem
		},
		data() {
			return {
				showKefu: false,
				baseUrl: this.$config.baseUrl,
				// list: [{
				// 		image: 'https://resource.tuniaokj.com/images/swiper/spring.jpg',
				// 		title: '春天'
				// 	},
				// 	{
				// 		image: 'https://resource.tuniaokj.com/images/swiper/summer.jpg',
				// 		title: '夏天'
				// 	},
				// 	{
				// 		image: 'https://resource.tuniaokj.com/images/swiper/autumn.jpg',
				// 		title: '秋天'
				// 	},
				// 	{
				// 		image: 'https://resource.tuniaokj.com/images/swiper/winter.jpg',
				// 		title: '冬天'
				// 	},
				// ],
				list: [],
				goodsList: [],
				service_img: ""
			}
		},
		onLoad() {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			this.getGoodsList()
			this.getsysconfig()
		},

		methods: {
			toDetail(item) {
				if (item.link) {
					wx.navigateToMiniProgram({
						shortLink: item.link,
						fail: (err) => {
							uni.showToast({
								title: err,
								icon: "none"
							})
						}
					})
				}
			},
			getsysconfig() { // 获取系统配置
				this.$http.post(this.$api.systemData, {}).then(res => {
					if (res.code == 200) {
						let datas = res.data;
						this.service_img = datas.config.service_img;
						let bannerlist = [];
						datas.config.banner_list.forEach((item) => {
							bannerlist.push({
								image: this.baseUrl + item
							})
						});
						this.list = bannerlist;
						uni.setStorageSync('systemData', datas)
					}
				})
			},
			getGoodsList() {
				this.$http.post(this.$api.goodsList).then(res => {
					if (res.code == 200) {
						this.goodsList = res.data
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.img_warp {
		margin: 60rpx 0rpx 40rpx 0rpx;
		width: 300rpx;
		height: 300rpx;
		background-color: #333333;
	}

	.tips_word {
		margin: 24rpx 0rpx;
		font-size: 24rpx;
		color: #333333;
	}

	.tips {
		width: 256rpx;
		height: 64rpx;
		border-radius: 16rpx;
		background: linear-gradient(270deg, #FE593B 0%, #E14B56 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		color: #ffffff;
		font-size: 32rpx;
	}

	.fab_btn {
		width: 154rpx;
		position: fixed;
		right: 0rpx;
		bottom: 150rpx;
	}

	.content {
		width: 100%;
		padding: 30rpx;
		box-sizing: border-box;
	}

	.body {
		margin-top: 20rpx;
	}
</style>