<template>
	<view style="padding-bottom: 140rpx;">
		<view class="quest_primary tn-bg-white">
			<view class="tn-width-full tn-flex" style="margin-bottom: 30rpx;">
				<view style="width: 600rpx;">
					<tn-line-progress :percent="percent" :height="20" activeColor="#5552FF"
						inactiveColor="#F5F5F5"></tn-line-progress>
				</view>
				<text style="margin-left: 40rpx; font-size: 28rpx;color: #999999;"> <text
						style="color: #FF000A;">{{questIndex+1}}</text> <text style="margin: 0rpx 5rpx;">/</text>
					{{questList.length}} </text>
			</view>
			<view class="qp_top tn-flex tn-flex-row-between tn-flex-col-center">
				<view class="qpt_left tn-flex tn-flex-col-center">
					<view class="type_tips">
						<text v-if="questItem.type==0">单选题</text>
						<text v-if="questItem.type==1">多选题</text>
						<text v-if="questItem.type==2">不定项</text>
					</view>
					<view style="font-size: 28rpx;color: #999999;" class="tn-flex tn-flex-col-center">
						<view class="tn-icon-clock-fill" style="font-size: 36rpx;"></view>
						<view style="margin-left: 5rpx;">
							<bing-countup ref="countUp" :show-hour="showHour" @change="onChangeTime" />
						</view>
					</view>
				</view>
				<view class="tn-flex tn-flex-col-center">
					<view style="font-size: 28rpx;color: #A1A1A1;" v-if="questItem.is_collect==1"
						@click.stop="toColl()">
						<text class="tn-icon-star-fill" style="color: #FFBD23;"></text> 已收藏
					</view>
					<view style="font-size: 28rpx;color: #A1A1A1;" v-if="questItem.is_collect==0"
						@click.stop="toColl()">
						<text class="tn-icon-star-fill" style="color: #A1A1A1;"></text> 收藏
					</view>
					<view class="correction" @click.stop="showError=true">
						<text class="tn-icon-help-fill" style="margin-right: 4rpx;"></text> 纠错
					</view>
				</view>
			</view>
			<view class="quest_warp">
				<view class="quest_title">
					{{questItem.title_id+"、"}}{{questItem.title||''}}
				</view>
				<view class="tn-width-full" v-if="questItem.title_img&&questItem.title_img.length>0"
					style="margin-top: 20rpx;margin-bottom: 20rpx;">
					<view v-for="(item,index) in questItem.title_img" :key="index" class="tn-width-full">
						<image :src="baseUrl+item" mode="widthFix" style="width: 100%;"></image>
					</view>
				</view>
				<view class="tn-width-full" v-if="questItem.type==0">
					<answer-com v-if="questItem.type==0" ref="exeAnsCom" @selAnswer="nextQuest"></answer-com>
				</view>
				<view class="tn-width-full" v-if="questItem.type==1||questItem.type==2">
					<answer-com-tum ref="exeAnsComTum" @selAnswer="nextQuestTum"></answer-com-tum>
				</view>
			</view>
		</view>
		<quest-fixed :end="questList.length>0&&questIndex==questList.length-1?true:false" @nextQuest="nextQuestBefore"
			@lastQuest="lastQuestBefore" @showCard="showCard=true"></quest-fixed>
		<tn-popup v-model="showCard" mode="bottom" :borderRadius="40" safeAreaInsetBottom>
			<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				<text class="tn-text-bold">
					答题卡
				</text>
				<view class="tn-flex tn-flex-col-center tn-flex-row-between" style="width: 300rpx;">
					<view class="tn-flex tn-flex-col-center">
						<view class="cri_red">
						</view>
						<view style="font-size: 24rpx;color: #333333;">
							已交
						</view>
					</view>
					<view class="tn-flex tn-flex-col-center">
						<view class="cri_blue">
						</view>
						<view style="font-size: 24rpx;color: #333333;">
							当前
						</view>
					</view>
					<view class="tn-flex tn-flex-col-center">
						<view class="cri_gary">
						</view>
						<view style="font-size: 24rpx;color: #333333;">
							未答
						</view>
					</view>
				</view>
			</view>
			<view class="scroll_warp">
				<scroll-view scroll-y="true" style="width: 100%;height: 100%;">
					<view class="tn-flex tn-flex-wrap tn-flex-row-between">
						<view v-for="(item,index) in questList" :key="index"
							:class="questIndex!=index?answerAll[item.id]!==null&&answerAll[item.id].length>0?'card_sub':'card_no':'card_now'"
							@click.stop="jumpQuest(index)">
							{{item.title_id}}
						</view>
						<view v-for="(item,index) in 5-questList.length%5" :key="'a'+index"
							style="width: 100rpx;margin-bottom: 40rpx;margin-left: 20rpx;">
						</view>
					</view>
				</scroll-view>
			</view>
		</tn-popup>
		<tn-popup v-model="showError" mode="bottom" :borderRadius="40" safeAreaInsetBottom>
			<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				<text class="tn-text-bold">
					反馈
				</text>
			</view>
			<view class="tn-width-full tn-flex tn-flex-direction-column"
				style="padding:0rpx 30rpx 30rpx 30rpx;box-sizing: border-box;">
				<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between">
					<view :class="selError=='有错别字'?'sel_is':'sel_no'" @click.stop="selError='有错别字'">
						有错别字
					</view>
					<view :class="selError=='题干有误'?'sel_is':'sel_no'" @click.stop="selError='题干有误'">
						题干有误
					</view>
					<view :class="selError=='答案有误'?'sel_is':'sel_no'" @click.stop="selError='答案有误'">
						答案有误
					</view>
				</view>
				<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between" style="margin-top: 30rpx;">
					<view :class="selError=='解析有误'?'sel_is':'sel_no'" @click.stop="selError='解析有误'">
						解析有误
					</view>
					<view :class="selError=='解析缺失'?'sel_is':'sel_no'" @click.stop="selError='解析缺失'">
						解析缺失
					</view>
					<view :class="selError=='选择有误'?'sel_is':'sel_no'" @click.stop="selError='选择有误'">
						选择有误
					</view>
				</view>
			</view>
			<view class="scroll_warp2 tn-width-full">
				<tn-input v-model="err_value" placeholder="开始输入..." :clearable="false" type="textarea" :border="false"
					:height="324" :autoHeight="false" />
			</view>
			<view class="tn-flex tn-flex-col-top tn-flex-row-center" style="height: 150rpx;">
				<view class="submit" @click.stop="submitErrorQuest()">立即反馈</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	import answerCom from "@/components/exercise/answer_com.vue"
	import answerComTum from "@/components/exercise/answer_com_tum.vue"
	import questFixed from "@/components/exercise/quest_fixed.vue"
	import BingCountup from "@/components/bing-countup.vue"
	import shareMixin from "@/mixins/shareMixin.js"
	export default {
		mixins: [shareMixin],
		components: {
			answerCom,
			questFixed,
			answerComTum,
			BingCountup
		},
		data() {
			return {
				exerId: null,
				questList: [],
				questItem: {},
				questIndex: -1,
				cardIndex: null,
				answerAll: {},
				percent: 0,
				showCard: false,
				showError: false,
				baseUrl: this.$config.baseUrl,
				maxTitleId: null,
				resDetail: [],
				showHour: false,
				userTime: 0,
				selError: '',
				err_value: '',
				isEnd: 1
			};
		},
		onLoad(options) {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			if (options.id) {
				this.exerId = options.id
				this.maxTitleId = options.maxTitleId
				this.toQuest()

			}
		},

		onUnload() {
			if (this.isEnd == 1) {
				this.submitBefore2()
			}
		},

		onBackPress() {
			let pages = getCurrentPages();
			let prevPage = pages[pages.length - 2];
			prevPage.$vm.getChapterList();
		},
		methods: {
			submitErrorQuest() {
				if (!this.selError) {
					uni.showToast({
						title: "请选择反馈类目",
						icon: "none"
					})
					return false
				}
				if (!this.err_value) {
					uni.showToast({
						title: "请输入反馈内容",
						icon: "none"
					})
					return false
				}
				let data = {
					title: this.selError,
					content: this.err_value,
					topic_id: this.questItem.id
				}
				this.$http.post(this.$api.errorCorrection, data).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: "提交成功",
							icon: "success"
						})
						this.selError = ''
						this.err_value = ''
						this.showError = false
					}
				})
			},
			onChangeTime(e) {
				if (e.hour > 0) {
					this.showHour = true
				}
				this.userTime = e.seconds
			},
			getRes() {
				this.$http.post(this.$api.answerRes, {
					chapter_id: this.exerId
				}).then(res => {
					if (res.code == 200) {
						this.resDetail = res.data
						if (res.data.is_over != 1) {
							for (let i = 0; i < res.data.topic_record.length; i++) {
								this.answerAll[res.data.topic_record[i].topic_id] = res.data.topic_record[i].answer
									.map(
										it => {
											return Number(it)
										})
							}
						}
					}
				})
			},
			toColl() {
				this.$http.post(this.$api.addColl, {
					topic_id: this.questItem.id
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: "操作成功",
							icon: "none"
						})
						if (this.questItem.is_collect == 1) {
							this.questItem.is_collect = 0
						} else {
							this.questItem.is_collect = 1
						}
					}
				})
			},
			submitBefore() {
				let data = []
				for (let key in this.answerAll) {
					data.push({
						topic_id: key,
						answer: this.answerAll[key]
					})
				}
				this.$http.post(this.$api.submitAnswer, {
					chapter_id: this.exerId,
					answers: data,
					use_time: this.userTime
				}).then(res => {
					if (res.code == 200) {
						this.isEnd = 2
						this.$publicjs.toUrl("/pages/exercise/exercise_end?chapter_id=" + this.exerId)
					}
				})
			},
			submitBefore2() {
				let data = []
				for (let key in this.answerAll) {
					data.push({
						topic_id: key,
						answer: this.answerAll[key]
					})
				}
				let arr = data.filter(item => {
					return item.answer.length > 0
				})
				if (arr.length > 0) {
					this.$http.post(this.$api.submitAnswer, {
						chapter_id: this.exerId,
						answers: arr,
						use_time: this.userTime
					}).then(res => {
						if (res.code == 200) {
							let pages = getCurrentPages();
							let prevPage = pages[pages.length - 1];
							prevPage.$vm.getChapterList();
						}
					})
				}
			},
			jumpQuest(index) {
				if (index <= this.cardIndex) {
					this.questIndex = index
					this.questItem = this.questList[this.questIndex]
					this.changeQuest()
				} else {
					uni.showToast({
						title: "当前题目未完成",
						icon: "none"
					})
				}
				this.showCard = false
			},
			nextQuestTum(item, index) {
				this.answerAll[item.id] = index
			},
			nextQuestBefore() {
				if (this.answerAll[this.questItem.id].length <= 0) {
					uni.showToast({
						title: "当前题目未完成",
						icon: "none"
					})
				} else {
					if (this.questList.length - 1 == this.questIndex) {
						this.submitBefore()
					} else {
						let arr=Object.keys(this.answerAll)
						let index=arr.indexOf(String(this.questItem.id))+1
						let item=arr[index]
						if (this.answerAll[item].length>0) {
							this.nextQuest(this.questItem, this.answerAll[this.questItem.id], false)
						}else{
							this.nextQuest(this.questItem, this.answerAll[this.questItem.id], true)
						}
					}
				}
			},
			lastQuestBefore() {
				if (this.questIndex == 0) {
					uni.showToast({
						title: "当前已是第一题",
						icon: "none"
					})
				} else {
					this.questIndex -= 1
					this.questItem = this.questList[this.questIndex]
					this.changeQuest()
				}
			},
			nextQuest(item, index, isPer = true) {
				this.answerAll[item.id] = index
				this.isEnd = 1
				if (this.questIndex == 0) {
					if (this.questList.length > 1) {
						this.questIndex += 1
						this.cardIndex = this.questIndex
						this.questItem = this.questList[this.questIndex]
						this.changeQuest()
						if (isPer) {
							this.percent = Number(Number(Number(this.questIndex + 1) / Number(this.questList.length) * 100)
								.toFixed(
									2))
						}
					}
				} else if (this.questIndex > 0 && this.questIndex < this.questList.length - 1) {
					this.questIndex += 1
					this.cardIndex = this.questIndex
					this.questItem = this.questList[this.questIndex]
					this.changeQuest()
					if (isPer) {
						this.percent = Number(Number(Number(this.questIndex + 1) / Number(this.questList.length) * 100)
							.toFixed(
								2))
						console.log("ddd");
					}
				} else if (this.questIndex == this.questList.length - 1) {}
			},
			changeQuest() {
				this.$nextTick(() => {
					if (this.questItem.type == 0) {
						this.$refs.exeAnsCom.changeData(this.questItem, this.answerAll)
					}
					if (this.questItem.type == 1 || this.questItem.type == 2) {
						this.$refs.exeAnsComTum.changeData(this.questItem, this.answerAll)
					}
				})
			},
			toQuest() {
				this.$http.post(this.$api.questList, {
					chapter_id: this.exerId
				}).then(res => {
					if (res.code == 200) {
						this.$refs.countUp.start();
						if (this.maxTitleId > 0) {
							this.questList = res.data
							for (let i = 0; i < res.data.length; i++) {
								if (res.data[i].title_id == this.maxTitleId) {
									if (i != res.data.length - 1) {
										this.questIndex = i + 1
										this.questItem = res.data[i + 1]
									} else {
										this.questIndex = 0
										this.questItem = res.data[0]
									}
								}
							}
							this.cardIndex = this.questIndex
							this.changeQuest()
							res.data.forEach(item => {
								this.answerAll[item.id] = []
							})
							this.percent = Number(Number(Number(this.questIndex + 1) / Number(this
									.questList
									.length) *
								100).toFixed(
								2))
						} else {
							this.questList = res.data
							this.questItem = res.data[0]
							this.questIndex = 0
							this.cardIndex = this.questIndex
							this.changeQuest()
							res.data.forEach(item => {
								this.answerAll[item.id] = []
							})
							this.percent = Number(Number(Number(this.questIndex + 1) / Number(this
									.questList
									.length) *
								100).toFixed(
								2))
						}
						this.$nextTick(() => {
							if (this.maxTitleId != 0) {
								this.getRes()
							}
						})
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.submit {
		width: 630rpx;
		height: 96rpx;
		border-radius: 48rpx;
		background-color: #5552FF;
		font-size: 16px;
		color: #FFFFFF;
		text-align: center;
		line-height: 96rpx;
	}

	.scroll_warp2 {
		width: 690rpx;
		height: 384rpx;
		border-radius: 20rpx;
		background-color: #F8F8F8;
		margin: 0rpx auto;
		margin-bottom: 26rpx;
		padding: 30rpx;
		box-sizing: border-box;
	}

	.sel_no {
		width: 200rpx;
		height: 80rpx;
		border-radius: 55rpx;
		background-color: #F1F1F1;
		text-align: center;
		line-height: 80rpx;
		color: #666666;
		font-size: 28rpx;
	}

	.sel_is {
		width: 200rpx;
		height: 80rpx;
		border-radius: 55rpx;
		background-color: #5552FF;
		text-align: center;
		line-height: 80rpx;
		color: #FFFFFF;
		font-size: 28rpx;
	}

	.cri_red {
		width: 14rpx;
		height: 14rpx;
		box-sizing: border-box;
		border-radius: 50%;
		border: 2rpx solid #FF585F;
		margin-right: 10rpx;
	}

	.cri_blue {
		width: 14rpx;
		height: 14rpx;
		box-sizing: border-box;
		border-radius: 50%;
		border: 2rpx solid #5552FF;
		margin-right: 10rpx;
	}

	.cri_gary {
		width: 14rpx;
		height: 14rpx;
		box-sizing: border-box;
		border-radius: 50%;
		border: 2rpx solid #C8C8C8;
		margin-right: 10rpx;
	}

	.card_no {
		width: 100rpx;
		height: 100rpx;
		border-radius: 55rpx;
		background-color: #C8C8C8;
		text-align: center;
		line-height: 100rpx;
		font-size: 32rpx;
		color: #FFFFFF;
		margin-bottom: 40rpx;
		margin-left: 20rpx;
	}

	.card_now {
		width: 100rpx;
		height: 100rpx;
		border-radius: 55rpx;
		text-align: center;
		line-height: 100rpx;
		font-size: 32rpx;
		color: #5552FF;
		margin-bottom: 40rpx;
		margin-left: 20rpx;
		border: 2rpx solid #5552FF;
	}

	.card_sub {
		width: 100rpx;
		height: 100rpx;
		border-radius: 55rpx;
		text-align: center;
		line-height: 100rpx;
		font-size: 32rpx;
		color: #FF585F;
		margin-bottom: 40rpx;
		margin-left: 20rpx;
		border: 2rpx solid #FF585F;
	}

	.scroll_warp {
		height: 450rpx;
		padding: 0rpx 40rpx 40rpx 40rpx;
		box-sizing: border-box;
	}

	.quest_title {
		font-size: 30rpx;
		color: #333333;
		margin-bottom: 50rpx;
	}

	.quest_warp {
		margin-top: 50rpx;
	}

	.correction {
		width: 122rpx;
		height: 44rpx;
		border-radius: 10rpx;
		background-color: #EEEEEE;
		text-align: center;
		line-height: 44rpx;
		color: #A1A1A1;
		font-size: 26rpx;
		margin-left: 48rpx;
	}

	.type_tips {
		width: 114rpx;
		height: 52rpx;
		border-radius: 26rpx 0rpx 26rpx 0rpx;
		background-color: #5552FF;
		text-align: center;
		line-height: 52rpx;
		color: #ffffff;
		font-size: 28rpx;
		margin-right: 30rpx;
	}

	.quest_primary {
		width: 750rpx;
		padding: 20rpx 30rpx;
		box-sizing: border-box;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		overflow: hidden;
	}
</style>