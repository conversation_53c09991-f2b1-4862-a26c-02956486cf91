{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/project/shuati_new/pages/exercise/exercise_res.vue?d376", "webpack:///D:/project/shuati_new/pages/exercise/exercise_res.vue?b5ee", "webpack:///D:/project/shuati_new/pages/exercise/exercise_res.vue?3405", "webpack:///D:/project/shuati_new/pages/exercise/exercise_res.vue?ece0", "uni-app:///pages/exercise/exercise_res.vue", "webpack:///D:/project/shuati_new/pages/exercise/exercise_res.vue?d1c7", "webpack:///D:/project/shuati_new/pages/exercise/exercise_res.vue?9cdb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "resId", "resDetail", "showAdd", "note_value", "onLoad", "withShareTicket", "menus", "methods", "back", "uni", "toAnalysis", "title", "icon", "submitNote", "chapter_id", "type", "content", "getRes"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,kRAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAAymB,CAAgB,snBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCyH7nB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEAV;MAAA;MACAW;MACAC;IACA;IAEA;MACA;MACA;IACA;EACA;EAEAC;IACAC;MACAC;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;QACA;UACA;QACA;UACAD;YACAE;YACAC;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACAC;QACAC;QACAC;MACA;QACA;UACAP;YACAG;YACAD;UACA;UACA;QACA;MACA;IACA;IACAM;MAAA;MACA;QACAH;MACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/LA;AAAA;AAAA;AAAA;AAA4qC,CAAgB,6nCAAG,EAAC,C;;;;;;;;;;;ACAhsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/exercise/exercise_res.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/exercise/exercise_res.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./exercise_res.vue?vue&type=template&id=1b27d1e7&scoped=true&\"\nvar renderjs\nimport script from \"./exercise_res.vue?vue&type=script&lang=js&\"\nexport * from \"./exercise_res.vue?vue&type=script&lang=js&\"\nimport style0 from \"./exercise_res.vue?vue&type=style&index=0&id=1b27d1e7&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1b27d1e7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/exercise/exercise_res.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exercise_res.vue?vue&type=template&id=1b27d1e7&scoped=true&\"", "var components\ntry {\n  components = {\n    tnCircleProgress: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-circle-progress/tn-circle-progress\" */ \"@/tuniao-ui/components/tn-circle-progress/tn-circle-progress.vue\"\n      )\n    },\n    tnPopup: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-popup/tn-popup\" */ \"@/tuniao-ui/components/tn-popup/tn-popup.vue\"\n      )\n    },\n    tnInput: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-input/tn-input\" */ \"@/tuniao-ui/components/tn-input/tn-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.resDetail.use_time > 0\n      ? _vm.$publicjs.timeMin(_vm.resDetail.use_time)\n      : null\n  var g1 =\n    _vm.resDetail &&\n    _vm.resDetail.topic_record &&\n    _vm.resDetail.topic_record.length > 0\n  var g2 = g1 ? _vm.resDetail.topic_record.length : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.showAdd = true\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exercise_res.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exercise_res.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"padding-bottom: 150rpx;\">\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"header_top tn-flex tn-flex-direction-column tn-flex-row-between\">\r\n\t\t\t\t<view class=\"tn-width-full\">\r\n\t\t\t\t\t<view class=\"tn-width-full\" style=\"font-size: 26rpx;color: #666666;\">{{resDetail.template_name}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tn-width-full tn-text-bold\"\r\n\t\t\t\t\t\tstyle=\"margin-top: 10rpx; font-size: 30rpx;color: #333333;\">{{resDetail.title}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-width-full tn-flex tn-flex-row-between\">\r\n\t\t\t\t\t<view class=\"tn-flex tn-flex-direction-column tn-flex-col-top\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t用时(min)\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"num\">\r\n\t\t\t\t\t\t\t{{resDetail.use_time>0?$publicjs.timeMin(resDetail.use_time):0}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tn-flex tn-flex-direction-column tn-flex-col-top\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t正确率\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"num\">\r\n\t\t\t\t\t\t\t{{resDetail.correct_ratio||0}}%\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tn-flex tn-flex-direction-column tn-flex-col-top\">\r\n\t\t\t\t\t\t<view class=\"title\">\r\n\t\t\t\t\t\t\t平均正确率\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"num\">\r\n\t\t\t\t\t\t\t{{resDetail.avg_correct_ratio||0}}%\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"header_bottom tn-flex tn-flex-direction-column tn-flex-row-between\">\r\n\t\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-flex-row-between\">\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<text style=\"color: #666666;font-size: 26rpx;\">正确率超过 <text\r\n\t\t\t\t\t\t\t\tstyle=\"color: #FF000A;font-weight: bold;\">{{resDetail.than_other||0}}</text>\r\n\t\t\t\t\t\t\t%的研友</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<tn-circle-progress :percent=\"resDetail.than_other||0\" :width=\"128\" :borderWidth=\"12\"\r\n\t\t\t\t\t\tactiveColor=\"#5552FF\" inactiveColor=\"#D8D8D8\">\r\n\t\t\t\t\t\t<view style=\"color: #222222;font-size: 20rpx;\">{{resDetail.than_other||0}}%</view>\r\n\t\t\t\t\t</tn-circle-progress>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-flex-row-around\">\r\n\t\t\t\t\t<view class=\"h_btn1\" @click.stop=\"toAnalysis('1')\">\r\n\t\t\t\t\t\t全部解析\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"h_btn2\" @click.stop=\"toAnalysis('2')\">\r\n\t\t\t\t\t\t错题研究\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"card_warp\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-flex-row-between\">\r\n\t\t\t\t<view class=\"tn-flex tn-flex-col-center\">\r\n\t\t\t\t\t<text class=\"tn-text-bold\" style=\"font-size: 30rpx;color: #333333;\">答题卡</text>\r\n\t\t\t\t\t<!-- <view style=\"margin-left: 24rpx;font-size: 24rpx;color: #999999;\"\r\n\t\t\t\t\t\tclass=\"tn-flex tn-flex-col-center\"> <text class=\"tn-icon-star-fill\"\r\n\t\t\t\t\t\t\tstyle=\"color: #FFBD23;font-size: 32rpx;\"></text> 为重点题</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-flex tn-flex-col-center\" style=\"font-size: 24rpx;color: #333333;\">\r\n\t\t\t\t\t<view class=\"tn-flex tn-flex-col-center\" style=\"margin-right: 55rpx;\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tstyle=\"margin-right: 8rpx; width: 14rpx;height: 14rpx;border-radius: 50%; border: 2rpx solid #5552FF;\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t正确\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tn-flex tn-flex-col-center\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tstyle=\"margin-right: 8rpx; width: 14rpx;height: 14rpx;border-radius: 50%; border: 2rpx solid #FF585F;\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t错误\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-wrap tn-flex-row-between\"\r\n\t\t\t\tv-if=\"resDetail&&resDetail.topic_record&&resDetail.topic_record.length>0\">\r\n\t\t\t\t<view v-for=\"(item,index) in resDetail.topic_record\" :key=\"index\"\r\n\t\t\t\t\t:class=\"item.result==1?'quest_suc':'quest_err'\" class=\"quest_or\"\r\n\t\t\t\t\************=\"toAnalysis('1',item.topic_id)\">\r\n\t\t\t\t\t{{item.title_id}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-for=\"(item,index) in 5-resDetail.topic_record.length%5\"\r\n\t\t\t\t\tstyle=\"width: 100rpx;margin-left: 10rpx;margin-right: 10rpx;\" :key=\"'00'+index\">\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"footer_model tn-flex tn-flex-col-center tn-flex-row-between\">\r\n\t\t\t<view class=\"f_btn1\" @click.stop=\"back()\">\r\n\t\t\t\t返回列表\r\n\t\t\t</view>\r\n\t\t\t<view class=\"f_btn2\" @click.stop=\"showAdd=true\">\r\n\t\t\t\t章笔记\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<tn-popup v-model=\"showAdd\" mode=\"bottom\" :borderRadius=\"40\" safeAreaInsetBottom>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t<view style=\"font-size: 34rpx;\">\r\n\t\t\t\t\t章笔记\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"scroll_warp2 tn-width-full\">\r\n\t\t\t\t<tn-input v-model=\"note_value\" placeholder=\"开始输入...\" :clearable=\"false\" type=\"textarea\" :border=\"false\"\r\n\t\t\t\t\t:height=\"324\" :autoHeight=\"false\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-flex tn-flex-col-top tn-flex-row-center\" style=\"height: 150rpx;\">\r\n\t\t\t\t<view class=\"submit\" @click.stop=\"submitNote()\">保存</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tresId: null,\r\n\t\t\t\tresDetail: {},\r\n\t\t\t\tshowAdd: false,\r\n\t\t\t\tnote_value: \"\"\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\tif (options.chapter_id) {\r\n\t\t\t\tthis.resId = options.chapter_id\r\n\t\t\t\tthis.getRes()\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\tback() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\r\n\t\t\ttoAnalysis(type, num) {\r\n\t\t\t\tif (type == 1) {\r\n\t\t\t\t\tif (!num) {\r\n\t\t\t\t\t\tthis.$publicjs.toUrl(\"/pages/analysis/index?id=\" + this.resId)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$publicjs.toUrl(\"/pages/analysis/index?id=\" + this.resId + \"&topicId=\" + num)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (type == 2) {\r\n\t\t\t\t\tif (this.resDetail.correct_ratio != 100) {\r\n\t\t\t\t\t\tthis.$publicjs.toUrl(\"/pages/analysis/error?id=\" + this.resId)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"暂无错题\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsubmitNote() {\r\n\t\t\t\tthis.$http.post(this.$api.addNote, {\r\n\t\t\t\t\tchapter_id: this.resId,\r\n\t\t\t\t\ttype: 1,\r\n\t\t\t\t\tcontent: this.note_value\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: \"success\",\r\n\t\t\t\t\t\t\ttitle: \"添加成功\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.showAdd = false\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetRes() {\r\n\t\t\t\tthis.$http.post(this.$api.answerRes, {\r\n\t\t\t\t\tchapter_id: this.resId\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.resDetail = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.scroll_warp2 {\r\n\t\twidth: 690rpx;\r\n\t\theight: 384rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #F8F8F8;\r\n\t\tmargin: 0rpx auto;\r\n\t\tmargin-bottom: 26rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.submit {\r\n\t\twidth: 630rpx;\r\n\t\theight: 96rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t\tbackground-color: #7270FF;\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #FFFFFF;\r\n\t\ttext-align: center;\r\n\t\tline-height: 96rpx;\r\n\t}\r\n\r\n\t.quest_suc {\r\n\t\tbackground-color: #5552FF;\r\n\t}\r\n\r\n\t.quest_err {\r\n\t\tbackground-color: #FF585F;\r\n\t}\r\n\r\n\t.f_btn1 {\r\n\t\twidth: 330rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 55rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder: 2rpx solid #3775F6;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #5552FF;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.f_btn2 {\r\n\t\twidth: 330rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 55rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder: 2rpx solid #5552FF;\r\n\t\tbackground-color: #5552FF;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.footer_model {\r\n\t\twidth: 750rpx;\r\n\t\theight: 120rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0rpx;\r\n\t\tleft: 0rpx;\r\n\t\tright: 0rpx;\r\n\t\tpadding: 0rpx 30rpx;\r\n\t}\r\n\r\n\t.quest_or {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 32rpx;\r\n\t\tline-height: 100rpx;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 40rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.h_btn1 {\r\n\t\twidth: 286rpx;\r\n\t\theight: 96rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbackground-color: #F1F0FF;\r\n\t\ttext-align: center;\r\n\t\tline-height: 96rpx;\r\n\t\tcolor: #7270FF;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.h_btn2 {\r\n\t\twidth: 286rpx;\r\n\t\theight: 96rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbackground-color: #7270FF;\r\n\t\ttext-align: center;\r\n\t\tline-height: 96rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.num {\r\n\t\tcolor: #333333;\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-top: 25rpx;\r\n\t}\r\n\r\n\t.card_warp {\r\n\t\twidth: 750rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.header_bottom {\r\n\t\twidth: 690rpx;\r\n\t\theight: 320rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tmargin-top: 4rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.header_top {\r\n\t\twidth: 690rpx;\r\n\t\theight: 320rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.header {\r\n\t\twidth: 750rpx;\r\n\t\tbackground: linear-gradient(to bottom, #FFE8E4, #FFE8E4) no-repeat;\r\n\t\tbackground-size: 100% 274rpx;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exercise_res.vue?vue&type=style&index=0&id=1b27d1e7&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./exercise_res.vue?vue&type=style&index=0&id=1b27d1e7&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980619877\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}