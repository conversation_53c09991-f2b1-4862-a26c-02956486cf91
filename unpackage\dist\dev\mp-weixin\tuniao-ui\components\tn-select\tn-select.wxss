@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.tn-select__content.data-v-a65b9b48 {
  position: relative;
}
.tn-select__content__header.data-v-a65b9b48 {
  position: relative;
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 90rpx;
  padding: 0 40rpx;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  font-size: 30rpx;
  background-color: #FFFFFF;
}
.tn-select__content__header__btn.data-v-a65b9b48 {
  padding: 16rpx;
  box-sizing: border-box;
  text-align: center;
  text-decoration: none;
}
.tn-select__content__header__title.data-v-a65b9b48 {
  color: #080808;
}
.tn-select__content__header--cancel.data-v-a65b9b48 {
  color: #AAAAAA;
}
.tn-select__content__header--confirm.data-v-a65b9b48 {
  background-color: #07C160;
  color: #FFFFFF;
  padding: 10rpx 25rpx;
  border-radius: 10rpx;
}
.tn-select__content__body.data-v-a65b9b48 {
  width: 100%;
  height: 500rpx;
  overflow: hidden;
  background-color: #FFFFFF;
}
.tn-select__content__body__search.data-v-a65b9b48 {
  z-index: 5;
  align-items: center;
  border-radius: 19px;
  background: #f8f8f8;
  width: calc(100% - 60rpx);
  margin: 0 auto;
  position: relative;
  top: 15px;
}
.tn-select__content__body__search__input.data-v-a65b9b48 {
  width: 600rpx;
}
.tn-select__content__body__view.data-v-a65b9b48 {
  height: 100%;
  box-sizing: border-box;
}
.tn-select__content__body__item.data-v-a65b9b48 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #080808;
  padding: 0 8rpx;
}

