/**
 * 分享功能快速修复方案
 * 为不方便使用混入的页面提供快速修复方法
 */

import shareHelper from '@/utils/shareHelper.js'

/**
 * 快速修复分享功能
 * 在页面的onLoad中调用此方法，然后使用getShareConfig获取分享配置
 */
export function quickFixShare() {
  const vm = this
  
  // 设置分享菜单
  // #ifdef MP-WEIXIN
  wx.showShareMenu({
    withShareTicket: true,
    menus: ['shareAppMessage', 'shareTimeline']
  })
  // #endif

  // 初始化分享数据
  if (!vm.shareImageUrl) {
    vm.shareImageUrl = ''
  }

  // 预加载分享图片
  shareHelper.getShareImage().then(imageUrl => {
    vm.shareImageUrl = imageUrl
  }).catch(error => {
    console.error('加载分享图片失败:', error)
    vm.shareImageUrl = vm.$config.baseUrl + '/static/222.jpg?t=' + new Date().getTime()
  })
}

/**
 * 获取分享配置对象
 * @param {Object} options 分享选项
 * @param {string} options.title 分享标题
 * @param {string} options.path 分享路径
 * @returns {Object} 分享配置对象
 */
export function getShareConfig(options = {}) {
  const vm = this
  const {
    title = '考研政治刷题库，名师题库免费刷，超100w考生都在用！',
    path = '/pages/quest/quest'
  } = options

  return {
    title,
    path,
    imageUrl: vm.shareImageUrl || (vm.$config.baseUrl + '/static/222.jpg?t=' + new Date().getTime())
  }
}

/**
 * 为页面添加分享方法
 * 在页面的methods中调用此方法
 */
export function addShareMethods() {
  return {
    // 微信小程序分享到聊天
    onShareAppMessage() {
      return getShareConfig.call(this)
    },

    // 微信小程序分享到朋友圈
    onShareTimeline() {
      return getShareConfig.call(this)
    }
  }
}
