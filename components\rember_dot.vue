<template>
	<view class="rember_dot">
		<view class="test" v-if="getShow()">
			试用
		</view>
		<view class="rember_dot_title tn-width-full">{{item.title}}</view>
		<view class="rember_dot_center tn-flex tn-flex-col-center tn-flex-row-around">
			<text>共{{item.total}}个考点</text>
			<text>掌握 <text style="color: #5552FF;margin-left: 2rpx;">{{item.grasp}}</text> </text>
			<text>未掌握<text style="color: #FF000A;margin-left: 2rpx;">{{item.no_grasp}}</text></text>
		</view>
		<view class="rember_dot_bottom tn-flex tn-flex-row-between">
			<view class="rdb_l tn-flex tn-flex-col-center tn-flex-row-center"
				v-if="selIndex!=item.id&&item.show_exam==1" @click.stop="changeFold('show')">
				展开考点<text class="tn-icon-down"></text>
			</view>
			<view class="rdb_l tn-flex tn-flex-col-center tn-flex-row-center"
				v-if="selIndex==item.id&&item.show_exam==1" @click.stop="changeFold('noshow')">
				收起考点<text class="tn-icon-up"></text>
			</view>
			<view class="" v-if="item.show_exam!=1"></view>
			<view class="tn-flex">
				<view class="rdb_btn tn-flex tn-flex-row-center" v-if="item.grasp==0&&item.no_grasp==0"
					@click.stop="toRemDetail()">
					{{item.type==0?'立即检测':'立即背诵'}}
				</view>
				<view class="rdb_btn tn-flex tn-flex-row-center"
					v-if="(item.grasp!=0||item.no_grasp!=0)&&item.grasp+item.no_grasp==item.total"
					@click.stop="toRemDetail()">
					{{item.type==0?'再测一次':'再背一次'}}
				</view>
				<view class="rdb_btn tn-flex tn-flex-row-center"
					v-if="(item.grasp!=0||item.no_grasp!=0)&&item.grasp+item.no_grasp!=item.total"
					@click.stop="toRemDetail()">
					{{item.type==0?'继续检测':'继续背诵'}}
				</view>
				<view class="rdb_btn2 tn-flex tn-flex-row-center" v-if="item.no_grasp>0" @click.stop="toRemDetailNo()">
					未掌握
				</view>
			</view>
		</view>
		<view class="rember_dot_footer" v-if="selIndex==item.id&&item.show_exam==1">
			<view v-for="(it,ind) in reciteExamList" :key="ind"
				class="title_model tn-flex tn-flex-wrap tn-flex-row-between">
				<view class="tips" v-if="it.grasp==0">未掌握</view>
				<view class="tips" style="background-color: #E8E7FF;color: #5552FF;" v-if="it.grasp==1">掌握</view>
				<view class="title_width">
					{{it.title_id}}.{{it.title}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "rember_dot",
		props: {
			item: {
				type: Object,
				default: () => {}
			},
			index: {
				type: Number,
				default: 0
			},
			selIndex: {
				type: Number,
				default: -1
			},
		},
		data() {
			return {
				reciteExamList: []
			};
		},
		methods: {
			toWeidian() {
				let url = uni.getStorageSync('systemData').config.recite_jump_link
				wx.navigateToMiniProgram({
					shortLink: url,
					fail: (err) => {
						uni.showToast({
							title: err,
							icon: "none"
						})
					}
				})
			},
			getShow() {
				if (Object.keys(this.item).length > 0) {
					let userinfo = uni.getStorageSync('userinfo')
					if (userinfo.recite_status == 0) {
						if (this.item.is_unlock == 0) {
							return true
						} else {
							return false
						}
					} else {
						return false
					}
				}
			},
			toRemDetailNo() {
				let userinfo = uni.getStorageSync('userinfo')
				if (userinfo.recite_status == 0) {
					if (this.item.is_unlock == 0) {
						uni.navigateTo({
							url: "/pages/rember/norecitation?chapter_id=" + this.item.id + "&type=" + this.item
								.type
						})
					} else {
						// this.$publicjs.toUrl("/pages/mine/code")
						this.toWeidian()
					}
				} else {
					uni.navigateTo({
						url: "/pages/rember/norecitation?chapter_id=" + this.item.id + "&type=" + this.item.type
					})
				}
			},
			toRemDetail() {
				let userinfo = uni.getStorageSync('userinfo')
				if (userinfo.recite_status == 0) {
					if (this.item.is_unlock == 0) {
						uni.navigateTo({
							url: "/pages/rember/recitation?chapter_id=" + this.item.id + "&type=" + this.item.type
						})
					} else {
						// this.$publicjs.toUrl("/pages/mine/code")
						this.toWeidian()
					}
				} else {
					uni.navigateTo({
						url: "/pages/rember/recitation?chapter_id=" + this.item.id + "&type=" + this.item.type
					})
				}
			},
			getExamList() {
				this.reciteExamList = this.item.exam_list
			},
			toRember(it) {
				this.$emit("toRember", it)
			},
			changeFold(type) {
				if (type == 'show') {
					this.$emit("showFold", this.item)
					this.getExamList()
				}
				if (type == 'noshow') {
					this.$emit("noShowFold")
				}
			},
		},
	}
</script>

<style lang="scss" scoped>
	.title_model {
		margin-top: 40rpx;
	}

	.tips {
		width: 68rpx;
		height: 32rpx;
		border-radius: 8rpx;
		background-color: #F7F7F7;
		color: #666666;
		font-size: 20rpx;
		text-align: center;
		line-height: 32rpx;
		margin-right: 10rpx;
	}

	.title_width {
		width: 550rpx;
		font-size: 28rpx;
		color: #333333;
	}

	.rdb_btn2 {
		width: 166rpx;
		height: 56rpx;
		border-radius: 80rpx;
		background-color: #E8E7FF;
		color: #5552FF;
		font-size: 24rpx;
		line-height: 56rpx;
		margin-left: 16rpx;
	}

	.rdb_btn {
		width: 166rpx;
		height: 56rpx;
		border-radius: 80rpx;
		background-color: #5552FF;
		color: #FFFFFF;
		font-size: 24rpx;
		line-height: 56rpx;
	}

	.rdb_l {
		width: 168rpx;
		height: 50rpx;
		border-radius: 10rpx;
		background-color: #F7F7F7;
		font-size: 24rpx;
		color: #666666;
	}

	.rember_dot_center {
		width: 400rpx;
		height: 50rpx;
		border-radius: 10rpx;
		background-color: #F7F7F7;
		margin: 20rpx 0rpx;
		font-size: 24rpx;
		color: #999999;
	}

	.test {
		position: absolute;
		top: 0rpx;
		right: 0rpx;
		width: 90rpx;
		height: 44rpx;
		border-radius: 0rpx 20rpx 0rpx 20rpx;
		background: linear-gradient(180deg, #FF9D4B 0%, #FFB200 100%);
		text-align: center;
		line-height: 44rpx;
		color: #FFFFFF;
		font-size: 22rpx;
	}

	.rember_dot {
		width: 690rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		box-sizing: border-box;
		background-color: #FFFFFF;
		position: relative;

		.rember_dot_title {
			color: #555555;
			font-size: 28rpx;
			font-weight: bold;
		}
	}
</style>