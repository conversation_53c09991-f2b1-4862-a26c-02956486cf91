import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
import {
	http
} from '@/utils/request';
import * as api from './api/api.js'
import tools from '@/utils/common.js'
import publicjs from '@/utils/publicJs.js'
import config from '@/config/index.js'
import '@/utils'

// 引入全局TuniaoUI
import TuniaoUI from 'tuniao-ui'
Vue.use(TuniaoUI)
import store from './store'
// 引入TuniaoUI提供的vuex简写方法
let vuexStore = require('@/store/$tn.mixin.js')
Vue.mixin(vuexStore)


Vue.config.productionTip = false
Vue.prototype.$publicjs = publicjs  // 封装方法
Vue.prototype.$config = config  // 域名
Vue.prototype.$http = http  // get post put delete ...
Vue.api = Vue.prototype.$api = api  // 接口
Vue.prototype.$tools = tools  // toast
App.mpType = 'app'
const app = new Vue({
	store,
  ...App
})
app.$mount()
// #endif
