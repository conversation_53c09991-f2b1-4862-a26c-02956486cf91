{"version": 3, "sources": ["webpack:///D:/project/shuati_new/pages/check/check.vue?bcba", "webpack:///D:/project/shuati_new/pages/check/check.vue?0be4", "webpack:///D:/project/shuati_new/pages/check/check.vue?33f0", "webpack:///D:/project/shuati_new/pages/check/check.vue?18a1", "uni-app:///pages/check/check.vue", "webpack:///D:/project/shuati_new/pages/check/check.vue?4641", "webpack:///D:/project/shuati_new/pages/check/check.vue?5d87", "uni-app:///main.js"], "names": ["mixins", "components", "productItem", "data", "showKefu", "baseUrl", "list", "goodsList", "service_img", "onLoad", "wx", "withShareTicket", "menus", "methods", "toDetail", "shortLink", "fail", "uni", "title", "icon", "getsysconfig", "datas", "bannerlist", "image", "getGoodsList", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACoDtnB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEAC;MAAA;MACAC;MACAC;IACA;IAEA;IACA;EACA;EAEAC;IACAC;MACA;QACAJ;UACAK;UACAC;YACAC;cACAC;cACAC;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;MACA;QACA;UACA;UACA;UACA;UACAC;YACAC;cACAC;YACA;UACA;UACA;UACAN;QACA;MACA;IACA;IACAO;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrIA;AAAA;AAAA;AAAA;AAAqqC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACAzrC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAd,EAAE,CAACe,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C", "file": "pages/check/check.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./check.vue?vue&type=template&id=057b20d8&scoped=true&\"\nvar renderjs\nimport script from \"./check.vue?vue&type=script&lang=js&\"\nexport * from \"./check.vue?vue&type=script&lang=js&\"\nimport style0 from \"./check.vue?vue&type=style&index=0&id=057b20d8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"057b20d8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/check/check.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./check.vue?vue&type=template&id=057b20d8&scoped=true&\"", "var components\ntry {\n  components = {\n    tnSwiper: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-swiper/tn-swiper\" */ \"@/tuniao-ui/components/tn-swiper/tn-swiper.vue\"\n      )\n    },\n    tnPopup: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-popup/tn-popup\" */ \"@/tuniao-ui/components/tn-popup/tn-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.showKefu = true\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./check.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./check.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<tn-swiper :list=\"list\" mode=\"none\" :height=\"160\" :radius=\"20\"></tn-swiper>\r\n\t\t<view class=\"body tn-flex tn-flex-row-between tn-flex-wrap\">\r\n\t\t\t<view v-for=\"(item,index) in goodsList\" :key=\"index\" style=\"margin-bottom: 20rpx;\">\r\n\t\t\t\t<product-item :item=\"item\" @toDetail=\"toDetail\"></product-item>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<tn-popup v-model=\"showKefu\" mode=\"bottom\" :borderRadius=\"40\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t客服帮助</view>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-direction-column tn-flex-col-center\"\r\n\t\t\t\tstyle=\"padding-bottom: 40rpx;\">\r\n\t\t\t\t<view class=\"tips\">\r\n\t\t\t\t\t解锁后务必添加\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tips_word\">\r\n\t\t\t\t\t<view class=\"tn-flex\">\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t1 开通确认\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\" style=\"width: 60rpx;\"></view>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t2 上新通知\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tn-flex\" style=\"margin-top: 20rpx;\">\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t3 最新干货\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\" style=\"width: 60rpx;\"></view>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t4 考研答疑\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"img_warp\">\r\n\t\t\t\t\t<image style=\"width: 100%;height: 100%;\" :src=\"baseUrl+service_img\" mode=\"\" show-menu-by-longpress>\r\n\t\t\t\t\t</image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"\" style=\"color: #5552FF;font-size: 24rpx;\">\r\n\t\t\t\t\t长按识别\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t\t<image src=\"../../static/icon/zixun.png\" mode=\"widthFix\" class=\"fab_btn\" @click.stop=\"showKefu=true\"></image>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport productItem from \"@/components/product_item.vue\"\r\n\timport shareMixin from \"@/mixins/shareMixin.js\"\r\n\texport default {\r\n\t\tmixins: [shareMixin],\r\n\t\tcomponents: {\r\n\t\t\tproductItem\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowKefu: false,\r\n\t\t\t\tbaseUrl: this.$config.baseUrl,\r\n\t\t\t\t// list: [{\r\n\t\t\t\t// \t\timage: 'https://resource.tuniaokj.com/images/swiper/spring.jpg',\r\n\t\t\t\t// \t\ttitle: '春天'\r\n\t\t\t\t// \t},\r\n\t\t\t\t// \t{\r\n\t\t\t\t// \t\timage: 'https://resource.tuniaokj.com/images/swiper/summer.jpg',\r\n\t\t\t\t// \t\ttitle: '夏天'\r\n\t\t\t\t// \t},\r\n\t\t\t\t// \t{\r\n\t\t\t\t// \t\timage: 'https://resource.tuniaokj.com/images/swiper/autumn.jpg',\r\n\t\t\t\t// \t\ttitle: '秋天'\r\n\t\t\t\t// \t},\r\n\t\t\t\t// \t{\r\n\t\t\t\t// \t\timage: 'https://resource.tuniaokj.com/images/swiper/winter.jpg',\r\n\t\t\t\t// \t\ttitle: '冬天'\r\n\t\t\t\t// \t},\r\n\t\t\t\t// ],\r\n\t\t\t\tlist: [],\r\n\t\t\t\tgoodsList: [],\r\n\t\t\t\tservice_img: \"\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\tthis.getGoodsList()\r\n\t\t\tthis.getsysconfig()\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\ttoDetail(item) {\r\n\t\t\t\tif (item.link) {\r\n\t\t\t\t\twx.navigateToMiniProgram({\r\n\t\t\t\t\t\tshortLink: item.link,\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: err,\r\n\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetsysconfig() { // 获取系统配置\r\n\t\t\t\tthis.$http.post(this.$api.systemData, {}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tlet datas = res.data;\r\n\t\t\t\t\t\tthis.service_img = datas.config.service_img;\r\n\t\t\t\t\t\tlet bannerlist = [];\r\n\t\t\t\t\t\tdatas.config.banner_list.forEach((item) => {\r\n\t\t\t\t\t\t\tbannerlist.push({\r\n\t\t\t\t\t\t\t\timage: this.baseUrl + item\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.list = bannerlist;\r\n\t\t\t\t\t\tuni.setStorageSync('systemData', datas)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetGoodsList() {\r\n\t\t\t\tthis.$http.post(this.$api.goodsList).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.goodsList = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.img_warp {\r\n\t\tmargin: 60rpx 0rpx 40rpx 0rpx;\r\n\t\twidth: 300rpx;\r\n\t\theight: 300rpx;\r\n\t\tbackground-color: #333333;\r\n\t}\r\n\r\n\t.tips_word {\r\n\t\tmargin: 24rpx 0rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.tips {\r\n\t\twidth: 256rpx;\r\n\t\theight: 64rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbackground: linear-gradient(270deg, #FE593B 0%, #E14B56 100%);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.fab_btn {\r\n\t\twidth: 154rpx;\r\n\t\tposition: fixed;\r\n\t\tright: 0rpx;\r\n\t\tbottom: 150rpx;\r\n\t}\r\n\r\n\t.content {\r\n\t\twidth: 100%;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.body {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./check.vue?vue&type=style&index=0&id=057b20d8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./check.vue?vue&type=style&index=0&id=057b20d8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980402470\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/check/check.vue'\ncreatePage(Page)"], "sourceRoot": ""}