{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/project/shuati_new/pages/rember/rember.vue?67d7", "webpack:///D:/project/shuati_new/pages/rember/rember.vue?c2f8", "webpack:///D:/project/shuati_new/pages/rember/rember.vue?7e19", "uni-app:///pages/rember/rember.vue", "webpack:///D:/project/shuati_new/pages/rember/rember.vue?53bc", "webpack:///D:/project/shuati_new/pages/rember/rember.vue?9368"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "components", "remberItem", "data", "baseUrl", "selIndex", "showKefu", "reciteList", "service_img", "userInfo", "onLoad", "withShareTicket", "menus", "onShow", "methods", "to<PERSON>eidian", "shortLink", "fail", "uni", "title", "icon", "getsysconfig", "toRember", "getReciteList"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAmmB,CAAgB,gnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACiDvnB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEAf;MAAA;MACAgB;MACAC;IACA;EAEA;EAEAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;MACApB;QACAqB;QACAC;UACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;MACA;QACA;UACA;UACA;UACAH;QACA;MACA;IACA;IACAI;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChHA;AAAA;AAAA;AAAA;AAAsqC,CAAgB,unCAAG,EAAC,C;;;;;;;;;;;ACA1rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/rember/rember.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/rember/rember.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./rember.vue?vue&type=template&id=43034fb4&scoped=true&\"\nvar renderjs\nimport script from \"./rember.vue?vue&type=script&lang=js&\"\nexport * from \"./rember.vue?vue&type=script&lang=js&\"\nimport style0 from \"./rember.vue?vue&type=style&index=0&id=43034fb4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"43034fb4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/rember/rember.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./rember.vue?vue&type=template&id=43034fb4&scoped=true&\"", "var components\ntry {\n  components = {\n    tnPopup: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-popup/tn-popup\" */ \"@/tuniao-ui/components/tn-popup/tn-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, index) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        index = _temp2.index\n      var _temp, _temp2\n      _vm.selIndex = index\n    }\n    _vm.e1 = function ($event) {\n      _vm.selIndex = -1\n    }\n    _vm.e2 = function ($event) {\n      $event.stopPropagation()\n      _vm.showKefu = true\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./rember.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./rember.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"vip_warp tn-flex tn-flex-col-center tn-flex-row-between\" @click.stop=\"toWeidian()\"\r\n\t\t\tv-if=\"userInfo.recite_status != 1\">\r\n\t\t\t<view class=\"vw_left tn-height-full tn-flex tn-flex-direction-column tn-flex-row-between\">\r\n\t\t\t\t<view class=\"vw_top tn-flex tn-flex-col-center\">\r\n\t\t\t\t\t<view class=\"vwt_word\">\r\n\t\t\t\t\t\t背诵会员\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image src=\"../../static/icon/vip.png\" mode=\"widthFix\" style=\"width: 32rpx;height: 32rpx;\"></image>\r\n\t\t\t\t\t<view class=\"vwt_tip\">\r\n\t\t\t\t\t\t认证会员\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"vw_bottom\">\r\n\t\t\t\t\t终极冲刺“速记＋检测”，时间越紧越有用\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"vw_right\">\r\n\t\t\t\t认证会员\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"list_body\">\r\n\t\t\t<view v-for=\"(item,index) in reciteList\" :key=\"index\" style=\"margin-bottom: 20rpx;\">\r\n\t\t\t\t<rember-item :item=\"item\" :index=\"index\" :selIndex=\"selIndex\" @showFold=\"selIndex=index\"\r\n\t\t\t\t\t@noShowFold=\"selIndex=-1\" @toRember=\"toRember\"></rember-item>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<image src=\"../../static/icon/zixun.png\" mode=\"widthFix\" class=\"fab_btn\" @click.stop=\"showKefu=true\"></image>\r\n\r\n\t\t<tn-popup v-model=\"showKefu\" mode=\"bottom\" :borderRadius=\"40\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t客服帮助</view>\r\n\t\t\t<view class=\"tn-width-full\" style=\"\">\r\n\t\t\t\t<view class=\"tn-flex tn-flex-row-center\"\r\n\t\t\t\t\tstyle=\"padding: 30rpx 46rpx 66rpx 46rpx;box-sizing: border-box;\">\r\n\t\t\t\t\t<view style=\"width: 400rpx;height: 400rpx;\">\r\n\t\t\t\t\t\t<image show-menu-by-longpress style=\"width: 400rpx;height: 400rpx;\" :src=\"baseUrl+service_img\"\r\n\t\t\t\t\t\t\tmode=\"\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport remberItem from \"@/components/rember_item.vue\"\r\n\timport shareMixin from \"@/mixins/shareMixin.js\"\r\n\texport default {\r\n\t\tmixins: [shareMixin],\r\n\t\tcomponents: {\r\n\t\t\tremberItem\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbaseUrl: this.$config.baseUrl,\r\n\t\t\t\tselIndex: -1,\r\n\t\t\t\tshowKefu: false,\r\n\t\t\t\treciteList: [],\r\n\t\t\t\tservice_img: \"\",\r\n\t\t\t\tuserInfo: {}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t},\r\n\r\n\t\tonShow() {\r\n\t\t\tthis.getReciteList()\r\n\t\t\tthis.getsysconfig()\r\n\t\t\tthis.userInfo = uni.getStorageSync('userinfo');\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttoWeidian() {\r\n\t\t\t\tlet url = uni.getStorageSync('systemData').config.user_jump_link\r\n\t\t\t\twx.navigateToMiniProgram({\r\n\t\t\t\t\tshortLink: url,\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: err,\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetsysconfig() { // 获取系统配置\r\n\t\t\t\tthis.$http.post(this.$api.systemData, {}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tlet datas = res.data;\r\n\t\t\t\t\t\tthis.service_img = datas.config.service_img\r\n\t\t\t\t\t\tuni.setStorageSync('systemData', datas)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoRember(it) {\r\n\t\t\t\tthis.$publicjs.toUrl('/pages/rember/remberItem?book_id=' + it.id)\r\n\t\t\t},\r\n\t\t\tgetReciteList() {\r\n\t\t\t\tthis.$http.post(this.$api.reciteList).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.reciteList = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.fab_btn {\r\n\t\twidth: 154rpx;\r\n\t\tposition: fixed;\r\n\t\tright: 0rpx;\r\n\t\tbottom: 150rpx;\r\n\t}\r\n\r\n\t.list_body {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.vw_right {\r\n\t\twidth: 150rpx;\r\n\t\theight: 56rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbackground-color: #FFF6F0;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tcolor: #A45505;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.vw_bottom {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #C4690D;\r\n\t}\r\n\r\n\t.vwt_tip {\r\n\t\twidth: 110rpx;\r\n\t\theight: 34rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tbackground: linear-gradient(180deg, #FF9D4B 0%, #FFB200 100%);\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\ttext-align: center;\r\n\t\tline-height: 36rpx;\r\n\t\tmargin-left: 12rpx;\r\n\t}\r\n\r\n\t.vwt_word {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #A45505;\r\n\t\tmargin-right: 4rpx;\r\n\t}\r\n\r\n\t.vip_warp {\r\n\t\twidth: 690rpx;\r\n\t\theight: 130rpx;\r\n\t\tbackground: url(\"../../static/rember_vip_bg.png\") no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t\tpadding: 20rpx 48rpx 28rpx 34rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.content {\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 30rpx 30rpx 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./rember.vue?vue&type=style&index=0&id=43034fb4&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./rember.vue?vue&type=style&index=0&id=43034fb4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980984013\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}