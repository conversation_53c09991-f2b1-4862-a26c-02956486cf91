<template>
	<view class="model_inner">
		<view class="model_inner_top tn-flex tn-flex-col-center tn-flex-row-between">
			<view class="mit_left tn-height-full tn-flex tn-flex-col-center tn-text-bold" @click.stop="openSelBook">
				<view class="mitl_word tn-text-ellipsis">
					{{selBookItem.title||''}}
				</view>
				<image src="../../static/icon/down.png" mode="heightFix" style="height: 16rpx;width: 28rpx;"></image>
			</view>
			<view class="mit_right" v-if="selBookItem.number==1" @click.stop="setBookNum">
				一刷<text class="tn-icon-right"></text>
			</view>
			<view class="mit_right" v-if="selBookItem.number==2" @click.stop="setBookNum">
				二刷<text class="tn-icon-right"></text>
			</view>
			<view class="mit_right" v-if="selBookItem.number==3" @click.stop="setBookNum">
				三刷<text class="tn-icon-right"></text>
			</view>
		</view>
		<tn-tabs :list="list_type" bold :isScroll="false" activeColor="#333333" inactiveColor="#9E9E9E"
			:current="current" name="template_name" @change="change"></tn-tabs>
		<view class="pro_model tn-flex tn-flex-direction-column tn-flex-row-between"
			v-if="Object.keys(bookDetail).length>0">
			<view class="pm_top tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
				<view class="pmt_left tn-color-white">
					<view class="pmtl_title tn-text-bold tn-text-ellipsis">
						{{bookDetail.cover_title||''}}
					</view>
					<view class="pmtl_sub tn-text-sm tn-text-ellipsis">
						{{bookDetail.study_title?'正在学习：'+bookDetail.study_title:''}}
					</view>
				</view>
				<view class="pmt_right">
					<!-- <tn-circle-progress :percent="bookDetail.correct_ratio" activeColor="#5552FF" :borderWidth="12"
						:width="150">
						<view class="tn-flex tn-flex-direction-column tn-flex-col-center">
							<view class="tn-color-white" style="font-size: 20rpx;">
								{{bookDetail.correct_ratio?bookDetail.correct_ratio+"%":'0%'}}
							</view>
							<view class="tn-color-white" style="font-size: 20rpx;">正确率</view>
						</view>
					</tn-circle-progress> -->
					<chatPro :opts="getOpt(bookDetail)" :chartData="getRatio(bookDetail)"></chatPro>
				</view>
			</view>
			<view class="pm_bottom  tn-width-full ">
				<view class="pmb_pro">
					<zui-progress-bar :height="16" texture="linear-gradient(180deg, #E3E3FF 2%, #5552FF 100%)"
						:disableValue="true" :value="completePro"></zui-progress-bar>
				</view>
				<view class="pmb_word tn-flex tn-flex-col-center tn-flex-row-between">
					<view class="tn-color-white tn-text-sm">
						已刷/总题：{{bookDetail.read||0}}/{{bookDetail.total||0}}
					</view>
					<view class="tn-color-white tn-text-sm">
						已完成{{bookDetail.complete||0}}%
					</view>
				</view>
			</view>
		</view>
		<view class="pro_model_foot tn-width-full tn-flex tn-flex-row-between tn-flex-col-center"
			v-if="Object.keys(bookDetail).length>0">
			<view class="pro_model_btn tn-flex tn-flex-col-center tn-flex-row-center" @click.stop="toError()">
				<image src="../../static/icon/error_book.png" mode="widthFix" style="width: 40rpx;height: 40rpx;">
				</image> 错题本
			</view>
			<view class="pro_model_btn2" @click.stop="toExercise()">
				立即刷题
			</view>
		</view>
	</view>
</template>

<script>
	import zuiProgressBar from "@/components/zui-progress-bar.vue"
	import chatPro from "@/components/chat_pro.vue"
	export default {
		name: "quest_model",
		components: {
			zuiProgressBar,
			chatPro
		},
		data() {
			return {
				list_type: [],
				current: 0,
				selBookId: null,
				selBookItem: {},
				selTemId: null,
				bookDetail: {},
				completePro: 0
			};
		},
		methods: {
			getOpt(bookDetail) {
				let ab = bookDetail.correct_ratio ? bookDetail.correct_ratio + "%" : "0%"
				let a = {
					title: {
						name: ab,
						fontSize: 10,
						color: "#ffffff",
						offsetX: 0,
						offsetY: 0
					},
					subtitle: {
						name: "正确率",
						fontSize: 8,
						color: "#ffffff",
						offsetX: 0,
						offsetY: 0
					},
					extra: {
						arcbar: {
							type: "circle",
							width: 8,
							backgroundColor: "#ffffff",
							startAngle: 1.5,
							endAngle: 1.5,
							gap: 2,
							direction: "cw",
							lineCap: "butt",
							centerX: 0,
							centerY: 0,
							linearType: "none"
						}
					}
				}
				return a
			},
			getRatio(bookDetail) {
				let a = bookDetail.correct_ratio ? Number(bookDetail.correct_ratio / 100).toFixed(2) : 0
				return {
					series: [{
						color: "#5552FF",
						data: a
					}]
				}
			},
			toError() {
				this.$emit('toError', {
					book_id: this.selBookId,
					template_id: this.selTemId,
					name: this.selBookItem.title,
					isselect: 1, // 是否选择分类
				})
			},
			setBookNum() {
				this.$emit('setBookNum')
			},
			toExercise() {
				this.$emit('toExercise', {
					book_id: this.selBookId,
					template_id: this.selTemId,
					name: this.selBookItem.title,
				})
			},
			openSelBook() { //回调选择书籍
				this.$emit("openSelBook")
			},
			changeBook(item) { //接收传递数据
				uni.setStorage({
					key: "category_one",
					data: item.id
				})
				this.selBookId = item.id
				this.selBookItem = item
				this.getBookTem()
			},
			getBookTem() { //获取书籍模板
				this.$http.post(this.$api.bookTem, {
					book_id: this.selBookId
				}).then(res => {
					if (res.code == 200) {
						this.list_type = res.data
						// this.current = 0
						this.selTemId = this.list_type[this.current].template_id
						this.$emit('changeTem', this.selTemId)
						this.getBookDetail()
					}
				})
			},
			getBookDetail() { //获取书籍详情
				this.$http.post(this.$api.bookDetail, {
					book_id: this.selBookId,
					template_id: this.selTemId,
					cate_id: 1,
				}).then(res => {
					if (res.code == 200) {
						this.bookDetail = res.data
						this.completePro = res.data.complete / 100
					}
				})
			},
			change(index) { //切换模板
				if (this.current != index) {
					this.current = index;
					this.selTemId = this.list_type[this.current].template_id
					this.$emit('changeTem', this.selTemId)
					this.getBookDetail()
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.mit_right {
		width: 92rpx;
		height: 34rpx;
		border-radius: 17rpx;
		background: linear-gradient(124deg, #FF9C4C 30%, #FFB200 90%);
		font-size: 24rpx;
		color: #FFFFFF;
		text-align: center;
		padding-left: 10rpx;
		box-sizing: border-box;
		line-height: 34rpx;
	}

	.mit_left {
		font-size: 30rpx;
		color: #FF000A;

		.mitl_word {
			max-width: 430rpx;
		}
	}

	.pro_model_foot {
		margin-top: 30rpx;
		margin-bottom: 10rpx;
	}

	.pm_bottom {
		margin-top: 32rpx;
		margin-bottom: 15rpx;
	}

	.pro_model_btn {
		width: 316rpx;
		height: 96rpx;
		border-radius: 48rpx;
		box-sizing: border-box;
		border: 4rpx solid #7270FF;
		color: #7270FF;
		font-size: 32rpx;
		line-height: 96rpx;
	}

	.pro_model_btn2 {
		width: 316rpx;
		height: 96rpx;
		border-radius: 48rpx;
		box-sizing: border-box;
		background-color: #7270FF;
		border: 4rpx solid #7270FF;
		text-align: center;
		line-height: 92rpx;
		color: #FFFFFF;
		font-size: 32rpx;
	}

	.pmb_pro {
		margin-bottom: 18rpx;
	}

	.pmt_left {
		width: 410rpx;
	}

	.pmtl_title {
		font-size: 32rpx;
	}

	.pmtl_sub {
		margin-top: 16rpx;
	}

	.pro_model {
		width: 650rpx;
		border-radius: 20rpx;
		background-color: #7270FF;
		padding: 30rpx;
		box-sizing: border-box;
		margin-top: 20rpx;
	}

	.model_inner_top {
		width: 650rpx;
		height: 66rpx;
		border-radius: 10rpx;
		background-color: #FDEEE9;
		padding: 0rpx 20rpx;
		box-sizing: border-box;
	}

	.model_inner {
		width: 690rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		margin-top: 36rpx;
		padding: 20rpx;
		box-sizing: border-box;
	}
</style>