{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/project/shuati_new/pages/quest/quest.vue?2057", "webpack:///D:/project/shuati_new/pages/quest/quest.vue?ac29", "webpack:///D:/project/shuati_new/pages/quest/quest.vue?32ad", "webpack:///D:/project/shuati_new/pages/quest/quest.vue?c19e", "uni-app:///pages/quest/quest.vue", "webpack:///D:/project/shuati_new/pages/quest/quest.vue?3b56", "webpack:///D:/project/shuati_new/pages/quest/quest.vue?49ef"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "components", "questModel", "questModelTw", "questModelTh", "questModelFr", "chartLine", "chartRadar", "questBtn", "data", "chart_line", "chart_radar", "showQuest", "showType", "systemData", "list", "category_id", "bookList", "selBookId", "selBookItem", "selTemId", "bookType", "userInfo", "onShow", "onLoad", "withShareTicket", "menus", "uni", "provider", "success", "code", "key", "methods", "toAdd", "url", "toNote2", "id", "title", "toColl2", "toHistory", "toColl", "to<PERSON><PERSON><PERSON>", "toNote", "submitBookNum", "content", "that", "book_id", "number", "changeTem1", "changeTem3", "changeTem2", "chartData", "categories", "series", "name", "type", "color", "legend<PERSON><PERSON><PERSON>", "getBookRateList", "template_id", "getBookTemRateList", "to<PERSON><PERSON><PERSON>", "to<PERSON><PERSON><PERSON><PERSON>", "selBook", "subBook", "changeCate", "getBookList", "getBookTemBefore", "getBookCate", "getSystemData", "getUserInfo", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACuJtnB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EAEAC;IAAA;IAEA7B;MAAA;MACA8B;MACAC;IACA;IAEA;MAEAC;QACAC;QACAC;UACA;YACAC;UACA;YACA;cACAH;gBACAI;gBACA;gBACAtB;gBACA;cACA;;cAEA;cACA;cACA;YACA;UACA;QACA;MACA;IAEA;MACA;MACA;MACA;IACA;EACA;EACAuB;IACAC;MACAN;QACAO;MACA;IACA;IACAC;MACA;QACAC;QACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA,6FACArB;IACA;IACAsB;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACAhB;QACAU;QACAO;QACAf;UACA;YACAgB;cACAC;cACAC;YACA;cACA;gBACAF;gBACAA;gBACAA;cACA;YACA;UACA;YACAA;YACAA;UACA;QACA;MACA;IAEA;IACAG;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;YACAC;cACAC;cACAC;gBACAC;gBACAC;gBACAC;gBACA/C;gBACAgD;cACA;gBACAH;gBACAC;gBACAC;gBACA/C;gBACAgD;cACA;YACA;UACA;QACA;MACA;IAGA;IACAC;MAAA;MACA;QACAZ;QACAa;MACA;QACA;UACA;YACAR;cACAC;cACAC;gBACAC;gBACAC;gBACAC;gBACA/C;gBACAgD;cACA;gBACAH;gBACAC;gBACAC;gBACA/C;gBACAgD;cACA;YACA;UACA;QACA;MACA;IACA;IACAG;MAAA;MACA;QACAd;MACA;QACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACA;UACA;UACA;YACAK;cACAC;cACAC;gBACAC;gBACA7C;cACA;gBACA6C;gBACA7C;cACA;YACA;UACA;QACA;MACA;IACA;IACAoD;MACA,qGACA;MACA;IACA;IACAC;MACA,8FACAH,cACA;IACA;IACAI;MAAA;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;MACA;QACAlD;MACA;QACAW;QACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IACAwC;MAAA;MACA;QACA;UACA;YACA;cACA;YACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;UACA;YACA;YACA;UACA;UACA;QACA;QACA;UACA;YACA;cACA;YACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;UACA;YACA;YACA;UACA;UACA;QACA;QACA;UACA;YACA;cACA;YACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;UACA;YACA;YACA;UACA;UACA;QACA;QACA;UACA;YACA;cACA;YACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;UACA;YACA;YACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;MACA;QACAzC;QACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IACA0C;MAAA;MAAA;MACA;QACA1C;QACA;UACA;UACA;UACAA;YACAI;YACAtB;UACA;QACA;MACA;IACA;IACA6D;MAAA;MACA;QACA;UACA;UACA3C;QACA;MACA;IACA;EACA;EACA4C;IACA;IACA;IACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9hBA;AAAA;AAAA;AAAA;AAAqqC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACAzrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/quest/quest.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/quest/quest.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./quest.vue?vue&type=template&id=c646f9e8&scoped=true&\"\nvar renderjs\nimport script from \"./quest.vue?vue&type=script&lang=js&\"\nexport * from \"./quest.vue?vue&type=script&lang=js&\"\nimport style0 from \"./quest.vue?vue&type=style&index=0&id=c646f9e8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c646f9e8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/quest/quest.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quest.vue?vue&type=template&id=c646f9e8&scoped=true&\"", "var components\ntry {\n  components = {\n    tnNoticeBar: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-notice-bar/tn-notice-bar\" */ \"@/tuniao-ui/components/tn-notice-bar/tn-notice-bar.vue\"\n      )\n    },\n    tnPopup: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-popup/tn-popup\" */ \"@/tuniao-ui/components/tn-popup/tn-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showQuest = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showType = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.showQuest = true\n    }\n    _vm.e3 = function ($event) {\n      _vm.showQuest = true\n    }\n    _vm.e4 = function ($event) {\n      _vm.showQuest = true\n    }\n    _vm.e5 = function ($event) {\n      _vm.showType = true\n    }\n    _vm.e6 = function ($event) {\n      $event.stopPropagation()\n      _vm.bookType = 1\n    }\n    _vm.e7 = function ($event) {\n      $event.stopPropagation()\n      _vm.bookType = 2\n    }\n    _vm.e8 = function ($event) {\n      $event.stopPropagation()\n      _vm.bookType = 3\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quest.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quest.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"padding-bottom: 30rpx;\">\r\n\t\t<view class=\"top\">\r\n\t\t\t<view class=\"top_tips tn-flex tn-flex-wrap tn-flex-col-center tn-flex-row-between\">\r\n\t\t\t\t<view class=\"t_tips\">\r\n\t\t\t\t\t上新推荐\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"t_word\">\r\n\t\t\t\t\t<tn-notice-bar :fontSize=\"26\" fontColor=\"#666666\" :leftIcon=\"false\" padding=\"0rpx 0rpx\" :list=\"list\"\r\n\t\t\t\t\t\tmode=\"horizontal\"></tn-notice-bar>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"model_warp\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center\">\r\n\t\t\t\t<view class=\"m_title\" :class=\"category_id==1?'selTitle':''\" @click.stop=\"changeCate(1)\">\r\n\t\t\t\t\t<text>强化题库</text>\r\n\t\t\t\t\t<image v-if=\"userInfo.exam_status == 0\" class=\"m_titleicon\" src=\"@/static/icon/lock.png\" mode=\"\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <view class=\"m_title\" :class=\"category_id==4?'selTitle':''\" @click.stop=\"changeCate(4)\">\r\n\t\t\t\t\t<text>重点易错题库</text>\r\n\t\t\t\t\t<image v-if=\"userInfo.exam_status == 0\" class=\"m_titleicon\" src=\"@/static/icon/lock.png\" mode=\"\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"m_title\" :class=\"category_id==2?'selTitle':''\" @click.stop=\"changeCate(2)\">\r\n\t\t\t\t\t<text>真题题库</text>\r\n\t\t\t\t\t<image v-if=\"userInfo.exam_status == 0\" class=\"m_titleicon\" src=\"@/static/icon/lock.png\" mode=\"\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"m_title\" :class=\"category_id==3?'selTitle':''\" @click.stop=\"changeCate(3)\">\r\n\t\t\t\t\t<text>名师押题</text>\r\n\t\t\t\t\t<image v-if=\"userInfo.exam_status == 0\" class=\"m_titleicon\" src=\"@/static/icon/lock.png\" mode=\"\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-icon-add tn-text-bold\" style=\"margin-left: 10rpx; font-size: 36rpx;color: #D8D8D8;\"\r\n\t\t\t\t\************=\"toAdd()\"></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-width-full\" v-if=\"category_id==1\">\r\n\t\t\t\t<quest-model ref=\"quest1model\" @changeTem=\"changeTem1\" @openSelBook=\"showQuest=true\"\r\n\t\t\t\t\t@toExercise=\"toExercise\" @setBookNum=\"showType=true\" @toError=\"toError\"></quest-model>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-width-full\" v-if=\"category_id==2\">\r\n\t\t\t\t<quest-model-tw ref=\"quest2model\" @changeTem=\"changeTem2\" @openSelBook=\"showQuest=true\"\r\n\t\t\t\t\t@toExercise=\"toExercise\" @toNote=\"toNote2\" @toColl=\"toColl2\" @toHistory=\"toHistory\"\r\n\t\t\t\t\t@toError=\"toError\"></quest-model-tw>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-width-full\" v-if=\"category_id==3\">\r\n\t\t\t\t<quest-model-th ref=\"quest3model\" @openSelBook=\"showQuest=true\" @toExercise=\"toExercise\"\r\n\t\t\t\t\t@toError=\"toError\" @changeTem=\"changeTem3\"></quest-model-th>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-width-full\" v-if=\"category_id==4\">\r\n\t\t\t\t<quest-model-fr ref=\"quest4model\" @changeTem=\"changeTem1\" @openSelBook=\"showQuest=true\"\r\n\t\t\t\t\t@toExercise=\"toExercise\" @setBookNum=\"showType=true\" @toError=\"toError\"></quest-model-fr>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"chart_warp\">\r\n\t\t\t<view class=\"tn-width-full\">\r\n\t\t\t\t<chart-line :item=\"chart_line\" :chartData=\"chart_line.chartData\"></chart-line>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-width-full\" style=\"margin-top: 50rpx;\" v-if=\"category_id!=2 && category_id!=3\">\r\n\t\t\t\t<chart-radar :item=\"chart_radar\" :chartData=\"chart_radar.chartData\"></chart-radar>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"quest_footer\" v-if=\"category_id!=2\">\r\n\t\t\t<quest-btn :type=\"category_id\" @toNote=\"toNote\" @toColl=\"toColl\" @toCheck=\"toCheck\"></quest-btn>\r\n\t\t</view>\r\n\t\t<tn-popup v-model=\"showType\" mode=\"bottom\" :borderRadius=\"40\" safeAreaInsetBottom>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; position:relative;z-index:999;background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t切换刷题</view>\r\n\t\t\t<view class=\"scroll_warp2 tn-width-full\">\r\n\t\t\t\t<view @click.stop=\"bookType=1\" style=\"margin-bottom: 40rpx;\"\r\n\t\t\t\t\tclass=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center\">\r\n\t\t\t\t\t<view class=\"q_pop_left\">\r\n\t\t\t\t\t\t一刷\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<image src=\"../../static/icon/nosel_icon.png\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\tstyle=\"width: 44rpx;height: 44rpx;\" v-if=\"bookType!=1\"></image>\r\n\t\t\t\t\t\t<image src=\"../../static/icon/sel_icon.png\" mode=\"widthFix\" style=\"width: 44rpx;height: 44rpx;\"\r\n\t\t\t\t\t\t\tv-if=\"bookType==1\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view @click.stop=\"bookType=2\" style=\"margin-bottom: 40rpx;\"\r\n\t\t\t\t\tclass=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center\">\r\n\t\t\t\t\t<view class=\"q_pop_left\">\r\n\t\t\t\t\t\t二刷\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<image src=\"../../static/icon/nosel_icon.png\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\tstyle=\"width: 44rpx;height: 44rpx;\" v-if=\"bookType!=2\"></image>\r\n\t\t\t\t\t\t<image src=\"../../static/icon/sel_icon.png\" mode=\"widthFix\" style=\"width: 44rpx;height: 44rpx;\"\r\n\t\t\t\t\t\t\tv-if=\"bookType==2\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view @click.stop=\"bookType=3\" style=\"margin-bottom: 40rpx;\"\r\n\t\t\t\t\tclass=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center\">\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t三刷\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t<image src=\"../../static/icon/nosel_icon.png\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\tstyle=\"width: 44rpx;height: 44rpx;\" v-if=\"bookType!=3\"></image>\r\n\t\t\t\t\t\t<image src=\"../../static/icon/sel_icon.png\" mode=\"widthFix\" style=\"width: 44rpx;height: 44rpx;\"\r\n\t\t\t\t\t\t\tv-if=\"bookType==3\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-flex tn-flex-col-top tn-flex-row-center\" style=\"height: 150rpx;\">\r\n\t\t\t\t<view class=\"submit\" @click=\"submitBookNum()\">保存设置</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t\t<tn-popup v-model=\"showQuest\" mode=\"bottom\" :borderRadius=\"40\" safeAreaInsetBottom>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box;position:relative;z-index:999; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t选择书籍</view>\r\n\t\t\t<view class=\"scroll_warp tn-width-full\">\r\n\t\t\t\t<scroll-view scroll-y=\"true\" style=\"width: 100%;height: 100%;\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in bookList\" :key=\"index\" style=\"margin-bottom: 40rpx;\"\r\n\t\t\t\t\t\tclass=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center\"\r\n\t\t\t\t\t\************=\"selBook(item)\">\r\n\t\t\t\t\t\t<view class=\"q_pop_left tn-text-ellipsis\">\r\n\t\t\t\t\t\t\t{{item.title}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\" v-if=\"item.id!=selBookId\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/icon/nosel_icon.png\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\t\tstyle=\"width: 44rpx;height: 44rpx;\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\" v-if=\"item.id==selBookId\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/icon/sel_icon.png\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\t\tstyle=\"width: 44rpx;height: 44rpx;\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-flex tn-flex-col-top tn-flex-row-center\" style=\"height: 150rpx;\">\r\n\t\t\t\t<view class=\"submit\" @click=\"subBook()\">保存设置</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport questModel from \"@/components/quest/quest_model.vue\"\r\n\timport questModelTw from \"@/components/quest/quest_model_tw.vue\"\r\n\timport questModelTh from \"@/components/quest/quest_model_th.vue\"\r\n\timport questModelFr from \"@/components/quest/quest_model_fr.vue\"\r\n\timport chartLine from \"@/components/quest/chart_line.vue\"\r\n\timport chartRadar from \"@/components/quest/chart_radar.vue\"\r\n\timport questBtn from \"@/components/quest/quest_btn.vue\"\r\n\timport shareMixin from \"@/mixins/shareMixin.js\"\r\n\texport default {\r\n\t\tmixins: [shareMixin],\r\n\t\tcomponents: {\r\n\t\t\tquestModel,\r\n\t\t\tquestModelTw,\r\n\t\t\tquestModelTh,\r\n\t\t\tquestModelFr,\r\n\t\t\tchartLine,\r\n\t\t\tchartRadar,\r\n\t\t\tquestBtn\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tchart_line: {},\r\n\t\t\t\tchart_radar: {},\r\n\t\t\t\tshowQuest: false,\r\n\t\t\t\tshowType: false,\r\n\t\t\t\t// ========\r\n\t\t\t\tsystemData: {},\r\n\t\t\t\tlist: [],\r\n\t\t\t\tcategory_id: 1,\r\n\t\t\t\tbookList: [],\r\n\t\t\t\tselBookId: null,\r\n\t\t\t\tselBookItem: {},\r\n\t\t\t\tselTemId: null,\r\n\t\t\t\tbookType: null,\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tif (uni.getStorageSync('TOKEN')) {\r\n\t\t\t\tthis.changeCate(this.category_id)\r\n\t\t\t\tthis.getUserInfo();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonLoad() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\tif (!uni.getStorageSync('TOKEN')) {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tuni.login({\r\n\t\t\t\t\tprovider: 'weixin',\r\n\t\t\t\t\tsuccess: (suc) => {\r\n\t\t\t\t\t\tthis.$http.post(this.$api.login, {\r\n\t\t\t\t\t\t\tcode: suc.code\r\n\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\t\t\tuni.setStorage({\r\n\t\t\t\t\t\t\t\t\tkey: \"TOKEN\",\r\n\t\t\t\t\t\t\t\t\t// data:\"2203\"\r\n\t\t\t\t\t\t\t\t\tdata: res.data\r\n\t\t\t\t\t\t\t\t\t// data: \"oyU6A7UHYmTsNOMmVB95qwXgArks\"\r\n\t\t\t\t\t\t\t\t})\r\n\r\n\t\t\t\t\t\t\t\tthis.getSystemData()\r\n\t\t\t\t\t\t\t\tthis.getBookList()\r\n\t\t\t\t\t\t\t\tthis.getUserInfo()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t} else {\r\n\t\t\t\tthis.getSystemData()\r\n\t\t\t\tthis.getBookList()\r\n\t\t\t\tthis.getUserInfo();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttoAdd() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: \"./addQuest\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoNote2(item) {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tid: item.book_id,\r\n\t\t\t\t\ttitle: item.cover_title\r\n\t\t\t\t}\r\n\t\t\t\tthis.$publicjs.toUrl(\"/pages/note/index?item=\" + JSON.stringify(data))\r\n\t\t\t},\r\n\t\t\ttoColl2(item) {\r\n\t\t\t\tthis.$publicjs.toUrl(\"/pages/collect/index?book_id=\" + item.book_id + \"&name=\" + item.cover_title)\r\n\t\t\t},\r\n\t\t\ttoHistory(item) {\r\n\t\t\t\tthis.$publicjs.toUrl(\"/pages/exercise/exercise_res?chapter_id=\" + item.chapter_id)\r\n\t\t\t},\r\n\t\t\ttoColl() {\r\n\t\t\t\tthis.$publicjs.toUrl(\"/pages/collect/index?book_id=\" + this.selBookItem.id + \"&name=\" + this\r\n\t\t\t\t\t.selBookItem.title)\r\n\t\t\t},\r\n\t\t\ttoCheck() {\r\n\t\t\t\tthis.$publicjs.toUrl(\"/pages/exercise/check_index?item=\" + JSON.stringify(this.selBookItem))\r\n\t\t\t},\r\n\t\t\ttoNote() {\r\n\t\t\t\tthis.$publicjs.toUrl(\"/pages/note/index?item=\" + JSON.stringify(this.selBookItem))\r\n\t\t\t},\r\n\t\t\tsubmitBookNum() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: \"提示\",\r\n\t\t\t\t\tcontent: \"切换后“错题本，笔记”数据会清空，如需要请自行导出PDF,是否确认切换？\",\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tthat.$http.post(that.$api.bookNum, {\r\n\t\t\t\t\t\t\t\tbook_id: that.selBookId,\r\n\t\t\t\t\t\t\t\tnumber: that.bookType\r\n\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\t\t\t\tthat.bookType = null\r\n\t\t\t\t\t\t\t\t\tthat.getBookList()\r\n\t\t\t\t\t\t\t\t\tthat.showType = false\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.bookType = null\r\n\t\t\t\t\t\t\tthat.showType = false\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\r\n\t\t\t},\r\n\t\t\tchangeTem1(id) {\r\n\t\t\t\tthis.selTemId = id\r\n\t\t\t\tthis.getBookRateList()\r\n\t\t\t\tthis.getBookTemRateList()\r\n\t\t\t},\r\n\t\t\tchangeTem3(id) {\r\n\t\t\t\tthis.selTemId = id\r\n\t\t\t\tthis.getBookRateList()\r\n\t\t\t},\r\n\t\t\tchangeTem2(item) {\r\n\t\t\t\tthis.$http.post(this.$api.bookRateList, item).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.chart_line = {\r\n\t\t\t\t\t\t\tchartData: {\r\n\t\t\t\t\t\t\t\tcategories: res.data.chapter_list,\r\n\t\t\t\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\t\t\t\tname: \"我的\",\r\n\t\t\t\t\t\t\t\t\ttype: \"line\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"#5552FF\",\r\n\t\t\t\t\t\t\t\t\tdata: res.data.correct_list,\r\n\t\t\t\t\t\t\t\t\tlegendShape: \"diamond\",\r\n\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\tname: \"平均\",\r\n\t\t\t\t\t\t\t\t\ttype: \"line\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"#FF000A\",\r\n\t\t\t\t\t\t\t\t\tdata: res.data.avg_list,\r\n\t\t\t\t\t\t\t\t\tlegendShape: \"diamond\",\r\n\t\t\t\t\t\t\t\t}],\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\r\n\r\n\t\t\t},\r\n\t\t\tgetBookRateList() {\r\n\t\t\t\tthis.$http.post(this.$api.bookRateList, {\r\n\t\t\t\t\tbook_id: this.selBookId,\r\n\t\t\t\t\ttemplate_id: this.selTemId\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.chart_line = {\r\n\t\t\t\t\t\t\tchartData: {\r\n\t\t\t\t\t\t\t\tcategories: res.data.chapter_list,\r\n\t\t\t\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\t\t\t\tname: \"我的\",\r\n\t\t\t\t\t\t\t\t\ttype: \"line\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"#5552FF\",\r\n\t\t\t\t\t\t\t\t\tdata: res.data.correct_list,\r\n\t\t\t\t\t\t\t\t\tlegendShape: \"diamond\",\r\n\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\tname: \"平均\",\r\n\t\t\t\t\t\t\t\t\ttype: \"line\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"#FF000A\",\r\n\t\t\t\t\t\t\t\t\tdata: res.data.avg_list,\r\n\t\t\t\t\t\t\t\t\tlegendShape: \"diamond\",\r\n\t\t\t\t\t\t\t\t}],\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetBookTemRateList() {\r\n\t\t\t\tthis.$http.post(this.$api.bookTemRateList, {\r\n\t\t\t\t\tbook_id: this.selBookId,\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tlet data = res.data.map(item => {\r\n\t\t\t\t\t\t\treturn item.template_name\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tlet avg_data = res.data.map(item => {\r\n\t\t\t\t\t\t\treturn item.avg_list\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tlet mine_data = res.data.map(item => {\r\n\t\t\t\t\t\t\treturn item.correct_list\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.chart_radar = {\r\n\t\t\t\t\t\t\tchartData: {\r\n\t\t\t\t\t\t\t\tcategories: data,\r\n\t\t\t\t\t\t\t\tseries: [{\r\n\t\t\t\t\t\t\t\t\tname: \"我的\",\r\n\t\t\t\t\t\t\t\t\tdata: mine_data,\r\n\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\tname: \"平均\",\r\n\t\t\t\t\t\t\t\t\tdata: avg_data,\r\n\t\t\t\t\t\t\t\t}],\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoError(item) {\r\n\t\t\t\tlet params = 'book_id=' + item.book_id + \"&template_id=\" + item.template_id + \"&name=\" + item.name +\r\n\t\t\t\t\t'&isselect=' + item.isselect\r\n\t\t\t\tthis.$publicjs.toUrl(\"/pages/error_quest/index?\" + params)\r\n\t\t\t},\r\n\t\t\ttoExercise(item) {\r\n\t\t\t\tthis.$publicjs.toUrl(\"/pages/exercise/index?book_id=\" + item.book_id + \"&template_id=\" + item\r\n\t\t\t\t\t.template_id +\r\n\t\t\t\t\t\"&name=\" + item.name)\r\n\t\t\t},\r\n\t\t\tselBook(item) { //切换书籍\r\n\t\t\t\tthis.selBookId = item.id\r\n\t\t\t\tthis.selBookItem = item\r\n\t\t\t},\r\n\t\t\tsubBook() { //提交书籍\r\n\t\t\t\tif (this.category_id == 1) {\r\n\t\t\t\t\tthis.$refs.quest1model.changeBook(this.selBookItem)\r\n\t\t\t\t}\r\n\t\t\t\tif (this.category_id == 2) {\r\n\t\t\t\t\tthis.$refs.quest2model.changeBook(this.selBookItem, this.bookList)\r\n\t\t\t\t}\r\n\t\t\t\tif (this.category_id == 3) {\r\n\t\t\t\t\tthis.$refs.quest3model.changeBook(this.selBookItem)\r\n\t\t\t\t}\r\n\t\t\t\tif (this.category_id == 4) {\r\n\t\t\t\t\tthis.$refs.quest4model.changeBook(this.selBookItem)\r\n\t\t\t\t}\r\n\t\t\t\tthis.showQuest = false\r\n\t\t\t},\r\n\t\t\tchangeCate(num) { //切换类目\r\n\t\t\t\tthis.category_id = num || this.category_id\r\n\t\t\t\tthis.selBookId = null\r\n\t\t\t\tthis.selBookItem = {}\r\n\t\t\t\tthis.bookList = []\r\n\t\t\t\tif (this.category_id != 2) {\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.getBookList()\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.getBookCate()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetBookList() { //书籍列表\r\n\t\t\t\tthis.$http.post(this.$api.bookList, {\r\n\t\t\t\t\tcategory_id: this.category_id\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.bookList = res.data\r\n\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\tthis.getBookTemBefore()\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetBookTemBefore() { //书籍模板获取之前\r\n\t\t\t\tif (this.bookList.length > 0) {\r\n\t\t\t\t\tif (this.category_id == 1) {\r\n\t\t\t\t\t\tif (uni.getStorageSync('category_one')) {\r\n\t\t\t\t\t\t\tlet data = this.bookList.filter(item => {\r\n\t\t\t\t\t\t\t\treturn item.id == uni.getStorageSync('category_one')\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tif (data.length > 0) {\r\n\t\t\t\t\t\t\t\tthis.selBookId = data[0].id\r\n\t\t\t\t\t\t\t\tthis.selBookItem = data[0]\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.selBookId = this.bookList[0].id\r\n\t\t\t\t\t\t\t\tthis.selBookItem = this.bookList[0]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.selBookId = this.bookList[0].id\r\n\t\t\t\t\t\t\tthis.selBookItem = this.bookList[0]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.$refs.quest1model.changeBook(this.selBookItem)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.category_id == 2) {\r\n\t\t\t\t\t\tif (uni.getStorageSync('category_two')) {\r\n\t\t\t\t\t\t\tlet data = this.bookList.filter(item => {\r\n\t\t\t\t\t\t\t\treturn item.id == uni.getStorageSync('category_two')\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tif (data.length > 0) {\r\n\t\t\t\t\t\t\t\tthis.selBookId = data[0].id\r\n\t\t\t\t\t\t\t\tthis.selBookItem = data[0]\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.selBookId = this.bookList[0].id\r\n\t\t\t\t\t\t\t\tthis.selBookItem = this.bookList[0]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.selBookId = this.bookList[0].id\r\n\t\t\t\t\t\t\tthis.selBookItem = this.bookList[0]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.$refs.quest2model.changeBook(this.selBookItem, this.bookList)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.category_id == 3) {\r\n\t\t\t\t\t\tif (uni.getStorageSync('category_three')) {\r\n\t\t\t\t\t\t\tlet data = this.bookList.filter(item => {\r\n\t\t\t\t\t\t\t\treturn item.id == uni.getStorageSync('category_three')\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tif (data.length > 0) {\r\n\t\t\t\t\t\t\t\tthis.selBookId = data[0].id\r\n\t\t\t\t\t\t\t\tthis.selBookItem = data[0]\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.selBookId = this.bookList[0].id\r\n\t\t\t\t\t\t\t\tthis.selBookItem = this.bookList[0]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.selBookId = this.bookList[0].id\r\n\t\t\t\t\t\t\tthis.selBookItem = this.bookList[0]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.$refs.quest3model.changeBook(this.selBookItem)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.category_id == 4) {\r\n\t\t\t\t\t\tif (uni.getStorageSync('category_four')) {\r\n\t\t\t\t\t\t\tlet data = this.bookList.filter(item => {\r\n\t\t\t\t\t\t\t\treturn item.id == uni.getStorageSync('category_four')\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tif (data.length > 0) {\r\n\t\t\t\t\t\t\t\tthis.selBookId = data[0].id\r\n\t\t\t\t\t\t\t\tthis.selBookItem = data[0]\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.selBookId = this.bookList[0].id\r\n\t\t\t\t\t\t\t\tthis.selBookItem = this.bookList[0]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.selBookId = this.bookList[0].id\r\n\t\t\t\t\t\t\tthis.selBookItem = this.bookList[0]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.$refs.quest4model.changeBook(this.selBookItem)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetBookCate() { //真题题库分类\r\n\t\t\t\tthis.$http.post(this.$api.bookCate).then(res => {\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.bookList = res.data\r\n\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\tthis.getBookTemBefore()\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetSystemData() { //系统数据\r\n\t\t\t\tthis.$http.post(this.$api.systemData).then(res => {\r\n\t\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.systemData = res.data\r\n\t\t\t\t\t\tthis.list = [res.data.notice.title]\r\n\t\t\t\t\t\tuni.setStorage({\r\n\t\t\t\t\t\t\tkey: \"systemData\",\r\n\t\t\t\t\t\t\tdata: JSON.stringify(res.data)\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetUserInfo() {\r\n\t\t\t\tthis.$http.post(this.$api.getUserInfo, {}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.userInfo = res.data;\r\n\t\t\t\t\t\tuni.setStorageSync('userinfo', res.data)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\tthis.getSystemData();\r\n\t\t\tthis.getBookList();\r\n\t\t\tthis.changeCate(this.category_id);\r\n\t\t\tthis.getUserInfo();\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.selTitle {\r\n\t\tfont-size: 36rpx !important;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.submit {\r\n\t\twidth: 630rpx;\r\n\t\theight: 96rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t\tbackground: #5552FF;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\ttext-align: center;\r\n\t\tline-height: 96rpx;\r\n\t}\r\n\r\n\t.q_pop_left {\r\n\t\twidth: 550rpx;\r\n\t}\r\n\r\n\t.scroll_warp {\r\n\t\theight: 450rpx;\r\n\t\tpadding: 0rpx 40rpx 40rpx 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.scroll_warp2 {\r\n\t\theight: 300rpx;\r\n\t\tpadding: 0rpx 40rpx 40rpx 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.quest_footer {\r\n\t\twidth: 100%;\r\n\t\theight: 170rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t}\r\n\r\n\t.chart_warp {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\tpage {\r\n\t\tbackground-color: #f5f5f5 !important;\r\n\t}\r\n\r\n\t.m_title {\r\n\t\tmin-width: 170rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tcolor: #333333;\r\n\t\tfont-size: 33rpx;\r\n\t}\r\n\r\n\t.m_titleicon {\r\n\t\tmargin-left: 5rpx;\r\n\t\twidth: 28rpx;\r\n\t\theight: 28rpx;\r\n\t}\r\n\r\n\t.model_warp {\r\n\t\twidth: 100%;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.top {\r\n\t\twidth: 100%;\r\n\t\tpadding: 10rpx 0rpx 30rpx 0rpx;\r\n\t\tbackground: url(\"../../static/top_bg02.png\") no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t}\r\n\r\n\t.t_word {\r\n\t\twidth: 540rpx;\r\n\t}\r\n\r\n\t.t_tips {\r\n\t\twidth: 137.5rpx;\r\n\t\theight: 50rpx;\r\n\t\tbackground: url(\"../../static/tips_bg.png\") no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 24rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 50rpx;\r\n\t}\r\n\r\n\t.top_tips {\r\n\t\twidth: 750rpx;\r\n\t\theight: 70rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tpadding: 0rpx 36rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quest.vue?vue&type=style&index=0&id=c646f9e8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quest.vue?vue&type=style&index=0&id=c646f9e8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980402776\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}