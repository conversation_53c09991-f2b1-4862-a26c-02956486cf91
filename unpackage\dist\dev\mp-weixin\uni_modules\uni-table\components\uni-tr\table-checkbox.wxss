@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-table-checkbox {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: relative;
  margin: 5px 0;
  cursor: pointer;
}
.uni-table-checkbox .checkbox__inner {
  flex-shrink: 0;
  box-sizing: border-box;
  position: relative;
  width: 16px;
  height: 16px;
  border: 1px solid #DCDFE6;
  border-radius: 2px;
  background-color: #fff;
  z-index: 1;
}
.uni-table-checkbox .checkbox__inner .checkbox__inner-icon {
  position: absolute;
  top: 2px;
  left: 5px;
  height: 7px;
  width: 3px;
  border: 1px solid #fff;
  border-left: 0;
  border-top: 0;
  opacity: 0;
  -webkit-transform-origin: center;
          transform-origin: center;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  box-sizing: content-box;
}
.uni-table-checkbox .checkbox__inner.checkbox--indeterminate {
  border-color: #007aff;
  background-color: #007aff;
}
.uni-table-checkbox .checkbox__inner.checkbox--indeterminate .checkbox__inner-icon {
  position: absolute;
  opacity: 1;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  height: 2px;
  top: 0;
  bottom: 0;
  margin: auto;
  left: 0px;
  right: 0px;
  bottom: 0;
  width: auto;
  border: none;
  border-radius: 2px;
  -webkit-transform: scale(0.5);
          transform: scale(0.5);
  background-color: #fff;
}
.uni-table-checkbox .checkbox__inner:hover {
  border-color: #007aff;
}
.uni-table-checkbox .checkbox__inner.is-disable {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-table-checkbox .checkbox__inner.is-checked {
  border-color: #007aff;
  background-color: #007aff;
}
.uni-table-checkbox .checkbox__inner.is-checked .checkbox__inner-icon {
  opacity: 1;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.uni-table-checkbox .checkbox__inner.is-checked.is-disable {
  opacity: 0.4;
}

