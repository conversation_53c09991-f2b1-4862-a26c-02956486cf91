<block wx:if="{{value}}"><view class="tn-select-class tn-select data-v-a65b9b48"><tn-popup vue-id="2516cbac-1" mode="bottom" popup="{{false}}" length="auto" safeAreaInsetBottom="{{safeAreaInsetBottom}}" maskCloseable="{{maskCloseable}}" zIndex="{{elZIndex}}" value="{{value}}" data-event-opts="{{[['^close',[['close']]],['^input',[['__set_model',['','value','$event',[]]]]]]}}" bind:close="__e" bind:input="__e" class="data-v-a65b9b48" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-select__content data-v-a65b9b48"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="tn-select__content__header data-v-a65b9b48" catchtouchmove="__e"><view class="tn-select__content__header__btn tn-select__content__header--cancel data-v-a65b9b48" style="{{'color:'+(cancelColor)+';'}}" hover-class="tn-hover-class" hover-stay-time="150" data-event-opts="{{[['tap',[['getResult',['cancel']]]]]}}" bindtap="__e">{{cancelText}}</view><view class="tn-select__content__header__title data-v-a65b9b48">{{title}}</view><view class="tn-select__content__header__btn tn-select__content__header--confirm data-v-a65b9b48" style="{{'color:'+(confirmColor)+';'}}" hover-class="tn-hover-class" hover-stay-time="150" data-event-opts="{{[['tap',[['getResult',['confirm']]]]]}}" bindtap="__e">{{confirmText}}</view></view><view class="tn-select__content__body data-v-a65b9b48"><block wx:if="{{searchShow&&mode==='single'}}"><view class="data-v-a65b9b48"><view class="tn-flex tn-select__content__body__search data-v-a65b9b48"><view class="tn-icon-search tn-padding-sm data-v-a65b9b48"></view><input class="tn-select__content__body__search__input data-v-a65b9b48" placeholder="{{searchPlaceholder}}" maxlength="255" confirm-type="search" data-event-opts="{{[['input',[['searchInput',['$event']]]],['confirm',[['search',['$event']]]]]}}" bindinput="__e" bindconfirm="__e"/></view></view></block><picker-view class="tn-select__content__body__view data-v-a65b9b48" value="{{defaultSelector}}" data-event-opts="{{[['pickstart',[['pickStart',['$event']]]],['pickend',[['pickEnd',['$event']]]],['change',[['columnChange',['$event']]]]]}}" bindpickstart="__e" bindpickend="__e" bindchange="__e"><block wx:for="{{columnData}}" wx:for-item="item" wx:for-index="index" wx:key="index"><picker-view-column class="data-v-a65b9b48"><block wx:for="{{item}}" wx:for-item="sub_item" wx:for-index="sub_index" wx:key="sub_index"><view class="tn-select__content__body__item data-v-a65b9b48"><view class="tn-text-ellipsis data-v-a65b9b48">{{''+sub_item[labelName]+''}}</view></view></block></picker-view-column></block></picker-view></view></view></tn-popup></view></block>