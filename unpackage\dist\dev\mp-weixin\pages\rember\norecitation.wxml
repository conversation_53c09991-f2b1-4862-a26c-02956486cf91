<view class="content data-v-3e028f4c"><view class="tn-flex tn-flex-col-center tn-flex-row-between data-v-3e028f4c"><view style="width:600rpx;" class="data-v-3e028f4c"><tn-line-progress vue-id="fb60498c-1" percent="{{per}}" activeColor="#5552FF" height="{{20}}" inactiveColor="#ffffff" class="data-v-3e028f4c" bind:__l="__l"></tn-line-progress></view><view class="total data-v-3e028f4c"><text style="color:#FF000A;" class="data-v-3e028f4c">{{questIndex+1}}</text>{{'/'+$root.g0}}</view></view><view class="quest_body data-v-3e028f4c"><quest-item vue-id="fb60498c-2" questTitle="{{questTitle}}" data-ref="quertRemItem" data-event-opts="{{[['^changebg',[['changebg']]]]}}" bind:changebg="__e" class="data-v-3e028f4c vue-ref" bind:__l="__l"></quest-item></view><block wx:if="{{type==1}}"><view class="quest_footer tn-flex tn-flex-row-between tn-flex-col-center data-v-3e028f4c"><view data-event-opts="{{[['tap',[['lastQuest']]]]}}" class="qf_left data-v-3e028f4c" catchtap="__e"><text class="tn-icon-left data-v-3e028f4c"></text></view><view data-event-opts="{{[['tap',[['submit',['0']]]]]}}" class="qf_btn data-v-3e028f4c" catchtap="__e">未掌握</view><view data-event-opts="{{[['tap',[['submit',['1']]]]]}}" class="qf_btn2 data-v-3e028f4c" catchtap="__e">掌握</view><view class="qf_right data-v-3e028f4c"><text data-event-opts="{{[['tap',[['nextQuest']]]]}}" class="tn-icon-right data-v-3e028f4c" catchtap="__e"></text></view></view></block><block wx:if="{{type==0}}"><view class="quest_footer tn-flex tn-flex-row-between tn-flex-col-center data-v-3e028f4c"><view data-event-opts="{{[['tap',[['lastQuest']]]]}}" class="qf_left data-v-3e028f4c" catchtap="__e"><text class="tn-icon-left data-v-3e028f4c"></text></view><view data-event-opts="{{[['tap',[['submit',['0']]]]]}}" class="qf_btn data-v-3e028f4c" catchtap="__e">困难</view><view data-event-opts="{{[['tap',[['submit',['1']]]]]}}" class="qf_btn2 data-v-3e028f4c" catchtap="__e">简单</view><view class="qf_right data-v-3e028f4c"><text data-event-opts="{{[['tap',[['nextQuest']]]]}}" class="tn-icon-right data-v-3e028f4c" catchtap="__e"></text></view></view></block></view>