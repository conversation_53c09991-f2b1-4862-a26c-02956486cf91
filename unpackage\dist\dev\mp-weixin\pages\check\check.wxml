<view class="content data-v-057b20d8"><tn-swiper vue-id="ac2d09a4-1" list="{{list}}" mode="none" height="{{160}}" radius="{{20}}" class="data-v-057b20d8" bind:__l="__l"></tn-swiper><view class="body tn-flex tn-flex-row-between tn-flex-wrap data-v-057b20d8"><block wx:for="{{goodsList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view style="margin-bottom:20rpx;" class="data-v-057b20d8"><product-item vue-id="{{'ac2d09a4-2-'+index}}" item="{{item}}" data-event-opts="{{[['^toDetail',[['toDetail']]]]}}" bind:toDetail="__e" class="data-v-057b20d8" bind:__l="__l"></product-item></view></block></view><tn-popup bind:input="__e" vue-id="ac2d09a4-3" mode="bottom" borderRadius="{{40}}" value="{{showKefu}}" data-event-opts="{{[['^input',[['__set_model',['','showKefu','$event',[]]]]]]}}" class="data-v-057b20d8" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold data-v-057b20d8" style="width:750rpx;height:120rpx;padding:30rpx;box-sizing:border-box;background:url('../../static/pop_head.png') no-repeat;background-size:100% 100%;">客服帮助</view><view class="tn-width-full tn-flex tn-flex-direction-column tn-flex-col-center data-v-057b20d8" style="padding-bottom:40rpx;"><view class="tips data-v-057b20d8">解锁后务必添加</view><view class="tips_word data-v-057b20d8"><view class="tn-flex data-v-057b20d8"><view class="data-v-057b20d8">1 开通确认</view><view style="width:60rpx;" class="data-v-057b20d8"></view><view class="data-v-057b20d8">2 上新通知</view></view><view class="tn-flex data-v-057b20d8" style="margin-top:20rpx;"><view class="data-v-057b20d8">3 最新干货</view><view style="width:60rpx;" class="data-v-057b20d8"></view><view class="data-v-057b20d8">4 考研答疑</view></view></view><view class="img_warp data-v-057b20d8"><image style="width:100%;height:100%;" src="{{baseUrl+service_img}}" mode show-menu-by-longpress="{{true}}" class="data-v-057b20d8"></image></view><view style="color:#5552FF;font-size:24rpx;" class="data-v-057b20d8">长按识别</view></view></tn-popup><image class="fab_btn data-v-057b20d8" src="../../static/icon/zixun.png" mode="widthFix" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" catchtap="__e"></image></view>