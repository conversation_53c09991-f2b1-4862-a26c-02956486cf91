<view style="padding-bottom:140rpx;" class="data-v-14ffad94"><view class="quest_primary tn-bg-white data-v-14ffad94"><view class="tn-width-full tn-flex data-v-14ffad94" style="margin-bottom:30rpx;"><view style="width:600rpx;" class="data-v-14ffad94"><tn-line-progress vue-id="ea12f41c-1" percent="{{percent}}" height="{{20}}" activeColor="#5552FF" inactiveColor="#F5F5F5" class="data-v-14ffad94" bind:__l="__l"></tn-line-progress></view><text style="margin-left:40rpx;font-size:28rpx;color:#999999;" class="data-v-14ffad94"><text style="color:#FF000A;" class="data-v-14ffad94">{{questIndex+1}}</text><text style="margin:0rpx 5rpx;" class="data-v-14ffad94">/</text>{{''+$root.g0+''}}</text></view><view class="qp_top tn-flex tn-flex-row-between tn-flex-col-center data-v-14ffad94"><view class="qpt_left tn-flex tn-flex-col-center data-v-14ffad94"><view class="type_tips data-v-14ffad94"><block wx:if="{{questItem.type==0}}"><text class="data-v-14ffad94">单选题</text></block><block wx:if="{{questItem.type==1}}"><text class="data-v-14ffad94">多选题</text></block><block wx:if="{{questItem.type==2}}"><text class="data-v-14ffad94">不定项</text></block></view></view><view class="tn-flex tn-flex-col-center data-v-14ffad94"><block wx:if="{{questItem.is_collect==1}}"><view data-event-opts="{{[['tap',[['toColl']]]]}}" style="font-size:28rpx;color:#A1A1A1;" catchtap="__e" class="data-v-14ffad94"><text class="tn-icon-star-fill data-v-14ffad94" style="color:#FFBD23;"></text>已收藏</view></block><block wx:if="{{questItem.is_collect==0}}"><view data-event-opts="{{[['tap',[['toColl']]]]}}" style="font-size:28rpx;color:#A1A1A1;" catchtap="__e" class="data-v-14ffad94"><text class="tn-icon-star-fill data-v-14ffad94" style="color:#A1A1A1;"></text>收藏</view></block><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="correction data-v-14ffad94" catchtap="__e"><text class="tn-icon-help-fill data-v-14ffad94" style="margin-right:4rpx;"></text>纠错</view></view></view><view class="quest_warp data-v-14ffad94"><view class="quest_title data-v-14ffad94">{{''+(questItem.title_id+"、")+(questItem.title||'')+''}}</view><block wx:if="{{$root.g1}}"><view class="tn-width-full data-v-14ffad94" style="margin-top:20rpx;margin-bottom:20rpx;"><block wx:for="{{questItem.title_img}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tn-width-full data-v-14ffad94"><image style="width:100%;" src="{{baseUrl+item}}" mode="widthFix" class="data-v-14ffad94"></image></view></block></view></block><block wx:if="{{questItem.type==0}}"><view class="tn-width-full data-v-14ffad94"><block wx:if="{{questItem.type==0}}"><answer-com bind:selAnswer="__e" vue-id="ea12f41c-2" data-ref="exeAnsCom" data-event-opts="{{[['^selAnswer',[['nextQuest']]]]}}" class="data-v-14ffad94 vue-ref" bind:__l="__l"></answer-com></block></view></block><block wx:if="{{questItem.type==1||questItem.type==2}}"><view class="tn-width-full data-v-14ffad94"><answer-com-tum bind:selAnswer="__e" vue-id="ea12f41c-3" data-ref="exeAnsComTum" data-event-opts="{{[['^selAnswer',[['nextQuestTum']]]]}}" class="data-v-14ffad94 vue-ref" bind:__l="__l"></answer-com-tum></view></block></view></view><quest-fixed vue-id="ea12f41c-4" end="{{$root.g2?true:false}}" data-event-opts="{{[['^nextQuest',[['nextQuestBefore']]],['^lastQuest',[['lastQuestBefore']]],['^showCard',[['e1']]]]}}" bind:nextQuest="__e" bind:lastQuest="__e" bind:showCard="__e" class="data-v-14ffad94" bind:__l="__l"></quest-fixed><tn-popup bind:input="__e" vue-id="ea12f41c-5" mode="bottom" borderRadius="{{40}}" safeAreaInsetBottom="{{true}}" value="{{showCard}}" data-event-opts="{{[['^input',[['__set_model',['','showCard','$event',[]]]]]]}}" class="data-v-14ffad94" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-14ffad94" style="width:750rpx;height:120rpx;padding:30rpx;box-sizing:border-box;background:url('../../static/pop_head.png') no-repeat;background-size:100% 100%;"><text class="tn-text-bold data-v-14ffad94">答题卡</text><view class="tn-flex tn-flex-col-center tn-flex-row-between data-v-14ffad94" style="width:300rpx;"><view class="tn-flex tn-flex-col-center data-v-14ffad94"><view class="cri_red data-v-14ffad94"></view><view style="font-size:24rpx;color:#333333;" class="data-v-14ffad94">已交</view></view><view class="tn-flex tn-flex-col-center data-v-14ffad94"><view class="cri_blue data-v-14ffad94"></view><view style="font-size:24rpx;color:#333333;" class="data-v-14ffad94">当前</view></view><view class="tn-flex tn-flex-col-center data-v-14ffad94"><view class="cri_gary data-v-14ffad94"></view><view style="font-size:24rpx;color:#333333;" class="data-v-14ffad94">未答</view></view></view></view><view class="scroll_warp data-v-14ffad94"><scroll-view style="width:100%;height:100%;" scroll-y="true" class="data-v-14ffad94"><view class="tn-flex tn-flex-wrap tn-flex-row-between data-v-14ffad94"><block wx:for="{{questList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['jumpQuest',[index]]]]]}}" class="{{['data-v-14ffad94',questIndex!=index?answerAll[item.id]!==null?'card_sub':'card_no':'card_now']}}" catchtap="__e">{{''+item.title_id+''}}</view></block><block wx:for="{{5-$root.g3%5}}" wx:for-item="item" wx:for-index="index"><view style="width:100rpx;margin-bottom:40rpx;margin-left:20rpx;" class="data-v-14ffad94"></view></block></view></scroll-view></view></tn-popup><tn-popup bind:input="__e" vue-id="ea12f41c-6" mode="bottom" borderRadius="{{40}}" safeAreaInsetBottom="{{true}}" value="{{showError}}" data-event-opts="{{[['^input',[['__set_model',['','showError','$event',[]]]]]]}}" class="data-v-14ffad94" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-14ffad94" style="width:750rpx;height:120rpx;padding:30rpx;box-sizing:border-box;background:url('../../static/pop_head.png') no-repeat;background-size:100% 100%;"><text class="tn-text-bold data-v-14ffad94">反馈</text></view><view class="tn-width-full tn-flex tn-flex-direction-column data-v-14ffad94" style="padding:0rpx 30rpx 30rpx 30rpx;box-sizing:border-box;"><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-14ffad94"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="{{['data-v-14ffad94',selError=='有错别字'?'sel_is':'sel_no']}}" catchtap="__e">有错别字</view><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="{{['data-v-14ffad94',selError=='题干有误'?'sel_is':'sel_no']}}" catchtap="__e">题干有误</view><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="{{['data-v-14ffad94',selError=='答案有误'?'sel_is':'sel_no']}}" catchtap="__e">答案有误</view></view><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-14ffad94" style="margin-top:30rpx;"><view data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" class="{{['data-v-14ffad94',selError=='解析有误'?'sel_is':'sel_no']}}" catchtap="__e">解析有误</view><view data-event-opts="{{[['tap',[['e6',['$event']]]]]}}" class="{{['data-v-14ffad94',selError=='解析缺失'?'sel_is':'sel_no']}}" catchtap="__e">解析缺失</view><view data-event-opts="{{[['tap',[['e7',['$event']]]]]}}" class="{{['data-v-14ffad94',selError=='选择有误'?'sel_is':'sel_no']}}" catchtap="__e">选择有误</view></view></view><view class="scroll_warp2 tn-width-full data-v-14ffad94"><tn-input bind:input="__e" vue-id="{{('ea12f41c-7')+','+('ea12f41c-6')}}" placeholder="开始输入..." clearable="{{false}}" type="textarea" border="{{false}}" height="{{324}}" autoHeight="{{false}}" value="{{err_value}}" data-event-opts="{{[['^input',[['__set_model',['','err_value','$event',[]]]]]]}}" class="data-v-14ffad94" bind:__l="__l"></tn-input></view><view class="tn-flex tn-flex-col-top tn-flex-row-center data-v-14ffad94" style="height:150rpx;"><view data-event-opts="{{[['tap',[['submitErrorQuest']]]]}}" class="submit data-v-14ffad94" catchtap="__e">立即反馈</view></view></tn-popup></view>