@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.error_bad.data-v-7d7a8a5a {
  width: 64rpx;
  height: 28rpx;
  border-radius: 17rpx 17rpx 17rpx 0rpx;
  background: linear-gradient(180deg, #FFBD23 0%, #FFBD23 100%);
  position: absolute;
  top: 0rpx;
  right: 10rpx;
}
.eb_order.data-v-7d7a8a5a {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #F0F0F0;
  text-align: center;
  line-height: 100rpx;
  color: #333333;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}
.error_body.data-v-7d7a8a5a {
  width: 690rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
}
.error_body .eb_title.data-v-7d7a8a5a {
  max-width: 420rpx;
  font-size: 30rpx;
  color: #333333;
}
.error_body .eb_fold.data-v-7d7a8a5a {
  width: 165rpx;
  height: 54rpx;
  border-radius: 10rpx;
  background-color: #F7F7F7;
  color: #666666;
  font-size: 24rpx;
  text-align: center;
  line-height: 54rpx;
}

