{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-input/tn-input.vue?7cf4", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-input/tn-input.vue?f2b2", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-input/tn-input.vue?a3f3", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-input/tn-input.vue?6d53", "uni-app:///tuniao-ui/components/tn-input/tn-input.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-input/tn-input.vue?a744", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-input/tn-input.vue?8593"], "names": ["mixins", "name", "props", "value", "type", "default", "inputAlign", "placeholder", "placeholder<PERSON><PERSON><PERSON>", "disabled", "max<PERSON><PERSON><PERSON>", "height", "autoHeight", "confirmType", "customStyle", "fixed", "focus", "passwordIcon", "border", "borderColor", "selectOpen", "clearable", "cursorSpacing", "selectionStart", "selectionEnd", "trim", "showConfirmBar", "showRightIcon", "rightIcon", "showLeftIcon", "leftIcon", "scene", "blockTime", "computed", "inputStyle", "style", "elSelectionStart", "elSelectionEnd", "data", "defaultValue", "inputHeight", "textareaHeight", "validateState", "focused", "showPassword", "lastValue", "watch", "detail", "created", "methods", "leftIconClick", "rightIconClick", "handleInput", "setTimeout", "handleBlur", "onFormItemError", "onFocus", "onConfirm", "onClear", "inputClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,kRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAAonB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACmHxoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IAEA;IACAD;MACAA;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;QACA;MACA;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;IACA;IACAsB;MACAvB;MACAC;IACA;IACA;IACAuB;MACAxB;MACAC;IACA;IACA;IACAwB;MACAzB;MACAC;IACA;IACA;IACAyB;MACA1B;MACAC;IACA;IACA;IACA0B;MACA3B;MACAC;IACA;IACA;IACA2B;MACA5B;MACAC;IACA;EACA;EACA4B;IACA;IACAC;MACA;MACA;MACAC,sDACA;MAEAA;MAEA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA3C;MACA;MACA;MACA;MACA;QACA;UACA4C;YACA5C;UACA;QACA;MACA;IACA;EACA;EACA6C;IACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACA;;QAMA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;;MAEA;MACAD;QACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACAA;QACA;;QAMA;QACA;MACA;IACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACvZA;AAAA;AAAA;AAAA;AAAmsC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACAvtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-input/tn-input.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-input.vue?vue&type=template&id=628dad5c&scoped=true&\"\nvar renderjs\nimport script from \"./tn-input.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-input.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-input.vue?vue&type=style&index=0&id=628dad5c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"628dad5c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-input/tn-input.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-input.vue?vue&type=template&id=628dad5c&scoped=true&\"", "var components\ntry {\n  components = {\n    tnButton: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-button/tn-button\" */ \"@/tuniao-ui/components/tn-button/tn-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.type === \"textarea\" ? _vm.__get_style([_vm.inputStyle]) : null\n  var s1 =\n    !(_vm.type === \"textarea\") && !(_vm.type === \"select\")\n      ? _vm.__get_style([_vm.inputStyle])\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showPassword = !_vm.showPassword\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-input.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-input.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view\r\n    class=\"tn-input-class tn-input\"\r\n    :class=\"{\r\n      'tn-input--border': border,\r\n      'tn-input--error': validateState\r\n    }\"\r\n    :style=\"{\r\n      padding: `0 ${border ? 20 : 0}rpx`,\r\n      borderColor: borderColor,\r\n      textAlign: inputAlign\r\n    }\"\r\n    @tap.stop=\"inputClick\"\r\n  >\r\n    <view\r\n        v-if=\"showLeftIcon\"\r\n        class=\"tn-input__left-icon__item tn-input__left-icon__clear\"\r\n    >\r\n      <tn-button shape=\"icon\" :scene=\"scene\" :block-time=\"blockTime\" @click=\"leftIconClick\"><view class=\"icon\" :class=\"[`tn-icon-${leftIcon}`]\"></view></tn-button>\r\n    </view>\r\n    <textarea\r\n      v-if=\"type === 'textarea'\"\r\n      class=\"tn-input__input tn-input__textarea\"\r\n      :style=\"[inputStyle]\"\r\n      :value=\"defaultValue\"\r\n      :placeholder=\"placeholder\"\r\n      :placeholderStyle=\"placeholderStyle\"\r\n      :disabled=\"disabled || type === 'select'\"\r\n      :maxlength=\"maxLength\"\r\n      :fixed=\"fixed\"\r\n      :focus=\"focus\"\r\n      :autoHeight=\"autoHeight\"\r\n      :selectionStart=\"elSelectionStart\"\r\n      :selectionEnd=\"elSelectionEnd\"\r\n      :cursorSpacing=\"cursorSpacing\"\r\n      :showConfirmBar=\"showConfirmBar\"\r\n      @input=\"handleInput\"\r\n      @blur=\"handleBlur\"\r\n      @focus=\"onFocus\"\r\n      @confirm=\"onConfirm\"\r\n    />\r\n\t<view v-else style=\"width:100%\">\r\n\t\t\r\n\t\t<view\r\n\t\t v-if=\"type === 'select'\"\r\n\t\t class=\"tn-input__text\"\r\n\t\t > \r\n\t\t {{defaultValue}}\r\n\t\t </view>\r\n\t\t \r\n\t\t <input\r\n\t\t   v-else\r\n\t\t   class=\"tn-input__input\"\r\n\t\t   :type=\"type === 'password' ? 'text' : type\"\r\n\t\t   :style=\"[inputStyle]\"\r\n\t\t   :value=\"defaultValue\"\r\n\t\t   :password=\"type === 'password' && !showPassword\"\r\n\t\t   :placeholder=\"placeholder\"\r\n\t\t   :placeholderStyle=\"placeholderStyle\"\r\n\t\t   :disabled=\"disabled || type === 'select'\"\r\n\t\t   :maxlength=\"maxLength\"\r\n\t\t   :focus=\"focus\"\r\n\t\t   :confirmType=\"confirmType\"\r\n\t\t   :selectionStart=\"elSelectionStart\"\r\n\t\t   :selectionEnd=\"elSelectionEnd\"\r\n\t\t   :cursorSpacing=\"cursorSpacing\"\r\n\t\t   :showConfirmBar=\"showConfirmBar\"\r\n\t\t   @input=\"handleInput\"\r\n\t\t   @blur=\"handleBlur\"\r\n\t\t   @focus=\"onFocus\"\r\n\t\t   @confirm=\"onConfirm\"\r\n\t\t />\r\n\t</view>\r\n    \r\n    \r\n    <!-- 右边的icon -->\r\n    <view class=\"tn-input__right-icon tn-flex tn-flex-col-center\">\r\n      <!-- 清除按钮 -->\r\n      <view\r\n        v-if=\"clearable && value !== '' && focused\"\r\n        class=\"tn-input__right-icon__item tn-input__right-icon__clear\"\r\n        @tap=\"onClear\"\r\n      >\r\n        <view class=\"icon tn-icon-close\"></view>\r\n      </view>\r\n      <view\r\n        v-else-if=\"type === 'text' && !focused && showRightIcon && rightIcon !== ''\"\r\n        class=\"tn-input__right-icon__item tn-input__right-icon__clear\"\r\n      >\r\n        <tn-button shape=\"icon\" :scene=\"scene\" :block-time=\"blockTime\" @click=\"rightIconClick\"><view class=\"icon\" :class=\"[`tn-icon-${rightIcon}`]\"></view></tn-button>\r\n      </view>\r\n      <!-- 显示密码按钮 -->\r\n      <view\r\n        v-if=\"passwordIcon && type === 'password'\"\r\n        class=\"tn-input__right-icon__item tn-input__right-icon__clear\"\r\n        @tap=\"showPassword = !showPassword\"\r\n      >\r\n        <view v-if=\"!showPassword\" class=\"tn-icon-eye-hide\"></view>\r\n        <view v-else class=\"icon tn-icon-eye\"></view>\r\n      </view>\r\n      <!-- 可选项箭头 -->\r\n      <view\r\n        v-if=\"type === 'select'\"\r\n        class=\"tn-input__right-icon__item tn-input__right-icon__select\"\r\n        :class=\"{\r\n          'tn-input__right-icon__select--reverse': selectOpen\r\n        }\"\r\n      >\r\n        <view class=\"icon tn-icon-up-triangle\"></view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import Emitter from '../../libs/utils/emitter.js'\r\n\r\n  export default {\r\n    mixins: [Emitter],\r\n    name: 'tn-input',\r\n    props: {\r\n      value: {\r\n        type: [String, Number],\r\n        default: ''\r\n      },\r\n\t  \r\n      // 输入框的类型\r\n      type: {\r\n        type: String,\r\n        default: 'text'\r\n      },\r\n      // 输入框文字对齐方式\r\n      inputAlign: {\r\n        type: String,\r\n        default: 'left'\r\n      },\r\n      // 文本框为空时显示的信息\r\n      placeholder: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      placeholderStyle: {\r\n        type: String,\r\n        default: 'color: #AAAAAA'\r\n      },\r\n      // 是否禁用输入框\r\n      disabled: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 可输入文字的最大长度\r\n      maxLength: {\r\n        type: Number,\r\n        default: 255\r\n      },\r\n      // 输入框高度\r\n      height: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // 根据内容自动调整高度\r\n      autoHeight: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 键盘右下角显示的文字，仅在text时生效\r\n      confirmType: {\r\n        type: String,\r\n        default: 'done'\r\n      },\r\n      // 输入框自定义样式\r\n      customStyle: {\r\n        type: Object,\r\n        default() {\r\n          return {}\r\n        }\r\n      },\r\n      // 是否固定输入框\r\n      fixed: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 是否自动获取焦点\r\n      focus: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 当type为password时，是否显示右侧密码图标\r\n      passwordIcon: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 当type为 input或者textarea时是否显示边框\r\n      border: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 边框的颜色\r\n      borderColor: {\r\n        type: String,\r\n        default: '#dcdfe6'\r\n      },\r\n      // 当type为select时，旋转右侧图标，标记当时select是打开还是关闭\r\n      selectOpen: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 是否可清空\r\n      clearable: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 光标与键盘的距离\r\n      cursorSpacing: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // selectionStart和selectionEnd需要搭配使用，自动聚焦时生效\r\n      // 光标起始位置\r\n      selectionStart: {\r\n        type: Number,\r\n        default: -1\r\n      },\r\n      // 光标结束位置\r\n      selectionEnd: {\r\n        type: Number,\r\n        default: -1\r\n      },\r\n      // 自动去除两端空格\r\n      trim: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 是否显示键盘上方的完成按钮\r\n      showConfirmBar: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 是否在输入框内最右边显示图标\r\n      showRightIcon: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 最右边图标的名称\r\n      rightIcon: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // 是否在输入框内最左边显示图标\r\n      showLeftIcon: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 最左边图标的名称\r\n      leftIcon: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      //场景：debounce ：防抖模式 throttle：节流模式\r\n      scene:{\r\n        type: String,\r\n        default: 'debounce'\r\n      },\r\n      // 防抖节流间隔时间（毫秒）\r\n      blockTime:{\r\n        type: Number,\r\n        default: 500\r\n      }\r\n    },\r\n    computed: {\r\n      // 输入框样式\r\n      inputStyle() {\r\n        let style = {}\r\n        // 如果没有设置高度，根据不同的类型设置一个默认值\r\n        style.minHeight = this.height ? this.height + 'rpx' : \r\n          this.type === 'textarea' ? this.textareaHeight + 'rpx' : this.inputHeight + 'rpx'\r\n        \r\n        style = Object.assign(style, this.customStyle)\r\n        \r\n        return style\r\n      },\r\n      // 光标起始位置\r\n      elSelectionStart() {\r\n        return String(this.selectionStart)\r\n      },\r\n      // 光标结束位置\r\n      elSelectionEnd() {\r\n        return String(this.selectionEnd)\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        // 默认值\r\n        defaultValue: this.value,\r\n        // 输入框高度\r\n        inputHeight: 70,\r\n        // textarea的高度\r\n        textareaHeight: 100,\r\n        // 标记验证的状态\r\n        validateState: false,\r\n        // 标记是否获取到焦点\r\n        focused: false,\r\n        // 是否预览密码\r\n        showPassword: false,\r\n        // 用于头条小程序，判断@input中，前后的值是否发生了变化，因为头条中文下，按下键没有输入内容，也会触发@input事件\r\n        lastValue: '',\r\n      }\r\n    },\r\n    watch: {\r\n      value(newVal, oldVal) {\r\n        this.defaultValue = newVal\r\n        // 当值发生变化时，并且type为select时，不会触发input事件\r\n        // 模拟input事件\r\n        if (newVal !== oldVal && this.type === 'select') {\r\n          this.handleInput({\r\n            detail: {\r\n              value: newVal\r\n            }\r\n          })\r\n        }\r\n      }\r\n    },\r\n    created() {\r\n      // 监听form-item发出的错误事件，将输入框变成红色\r\n      this.$on(\"on-form-item-error\", this.onFormItemError)\r\n    },\r\n    methods: {\r\n      leftIconClick(){\r\n        this.$emit('leftClick', this.defaultValue)\r\n      },\r\n      rightIconClick(){\r\n        this.$emit('rightClick', this.defaultValue)\r\n      },\r\n      /**\r\n       * input事件\r\n       */\r\n      handleInput(event) {\r\n        let value = event.detail.value\r\n        // 是否需要去掉空格\r\n        if (this.trim) value = this.$tn.string.trim(value)\r\n        // 原生事件\r\n        this.$emit('input', value)\r\n        // model赋值\r\n        this.defaultValue = value\r\n        // 过一个生命周期再发送事件给tn-form-item，否则this.$emit('input')更新了父组件的值，但是微信小程序上\r\n        // 尚未更新到tn-form-item，导致获取的值为空，从而校验混论\r\n        // 这里不能延时时间太短，或者使用this.$nextTick，否则在头条上，会造成混乱\r\n        setTimeout(() => {\r\n          // 头条小程序由于自身bug，导致中文下，每按下一个键(尚未完成输入)，都会触发一次@input，导致错误，这里进行判断处理\r\n          // #ifdef MP-TOUTIAO\r\n          if (this.$tn.string.trim(value) === this.lastValue) return\r\n          this.lastValue = value\r\n          // #endif\r\n          \r\n          // 发送当前的值到form-item进行校验\r\n          this.dispatch('tn-form-item','on-form-change', value)\r\n        }, 40)\r\n      },\r\n      /**\r\n       * blur事件\r\n       */\r\n      handleBlur(event) {\r\n        let value = event.detail.value\r\n        \r\n        // 由于点击清除图标也会触发blur事件，导致图标消失从而无法点击\r\n        setTimeout(() => {\r\n          this.focused = false\r\n        }, 100)\r\n        \r\n        // 原生事件\r\n        this.$emit('blur', value)\r\n        // 过一个生命周期再发送事件给tn-form-item，否则this.$emit('blur')更新了父组件的值，但是微信小程序上\r\n        // 尚未更新到tn-form-item，导致获取的值为空，从而校验混论\r\n        // 这里不能延时时间太短，或者使用this.$nextTick，否则在头条上，会造成混乱\r\n        setTimeout(() => {\r\n          // 头条小程序由于自身bug，导致中文下，每按下一个键(尚未完成输入)，都会触发一次@input，导致错误，这里进行判断处理\r\n          // #ifdef MP-TOUTIAO\r\n          if (this.$tn.string.trim(value) === this.lastValue) return\r\n          this.lastValue = value\r\n          // #endif\r\n          \r\n          // 发送当前的值到form-item进行校验\r\n          this.dispatch('tn-form-item','on-form-blur', value)\r\n        }, 40)\r\n      },\r\n      // 处理校验错误\r\n      onFormItemError(status) {\r\n        this.validateState = status\r\n      },\r\n      // 聚焦事件\r\n      onFocus(event) {\r\n        this.focused = true\r\n        this.$emit('focus')\r\n      },\r\n      // 点击确认按钮事件\r\n      onConfirm(event) {\r\n        this.$emit('confirm', event.detail.value)\r\n      },\r\n      // 清除事件\r\n      onClear(event) {\r\n        this.$emit('input', '')\r\n      },\r\n      // 点击事件\r\n      inputClick() {\r\n        this.$emit('click')\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .tn-input {\r\n    display: flex;\r\n    flex-direction: row;\r\n    position: relative;\r\n    flex: 1;\r\n    \r\n    &__input {\r\n      font-size: 28rpx;\r\n      color: $tn-font-color;\r\n      flex: 1;\r\n    }\r\n\t\r\n    &__text {\r\n      font-size: 28rpx;\r\n      color: $tn-font-color;\r\n      flex: 1;\r\n\t  min-width: 296rpx;\r\n\t  max-width: 100%;\r\n\t  text-overflow:clip;\r\n    }\r\n    \r\n    &__textarea {\r\n      width: auto;\r\n      font-size: 28rpx;\r\n      color: $tn-font-color;\r\n      padding: 10rpx 0;\r\n      line-height: normal;\r\n      flex: 1;\r\n    }\r\n    \r\n    &--border {\r\n      border-radius: 6rpx;\r\n      border: 2rpx solid $tn-border-solid-color;\r\n    }\r\n    \r\n    &--error {\r\n      border-color: $tn-color-red !important;\r\n    }\r\n\r\n\r\n    &__right-icon {\r\n      line-height: 1;\r\n      .icon {\r\n        color: $tn-font-sub-color;\r\n      }\r\n      \r\n      &__item {\r\n        margin-left: 10rpx;\r\n      }\r\n      \r\n      &__clear {\r\n        .icon {\r\n          font-size: 32rpx;\r\n        }\r\n      }\r\n      \r\n      &__select {\r\n        transition: transform .4s;\r\n        \r\n        .icon {\r\n          font-size: 26rpx;\r\n        }\r\n        \r\n        &--reverse {\r\n          transform: rotate(-180deg);\r\n        }\r\n      }\r\n    }\r\n\r\n    &__left-icon {\r\n      line-height: 1;\r\n      &__item {\r\n        margin-left: 0rpx;\r\n        margin-top: 4rpx;\r\n      }\r\n\r\n      &__clear {\r\n        .icon {\r\n          font-size: 32rpx;\r\n          color: $tn-font-sub-color;\r\n        }\r\n      }\r\n\r\n      &__select {\r\n        transition: transform .4s;\r\n\r\n        .icon {\r\n          font-size: 26rpx;\r\n        }\r\n\r\n        &--reverse {\r\n          transform: rotate(-180deg);\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-input.vue?vue&type=style&index=0&id=628dad5c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-input.vue?vue&type=style&index=0&id=628dad5c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980406005\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}