Date.prototype.getWeek = function(number) {
  switch (number) {
    case 1:
      return "一";
    case 2:
      return "二";
    case 3:
      return "三";
    case 4:
      return "四";
    case 5:
      return "五";
    case 6:
      return "六";
    case 0:
      return "日";
  }
};

Date.prototype.Format = function(fmt) {
  var o = {
    "M+": this.getMonth() + 1, //月份
    "d+": this.getDate(), //日
    "h+": this.getHours(), //小时
    "m+": this.getMinutes(), //分
    "s+": this.getSeconds(), //秒
    "q+": Math.floor((this.getMonth() + 3) / 3), //季度
    S: this.getMilliseconds(), //毫秒
  };
  if (/(y+)/.test(fmt))
    fmt = fmt.replace(
      RegExp.$1,
      (this.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  for (var k in o)
    if (new RegExp("(" + k + ")").test(fmt))
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
  return fmt;
};

Date.prototype.addDay = function(days, format) {
  if (days > 0) {
    this.setDate(this.getDate() + days); // 获取AddDayCount天后的日期
  } else {
    this.setDate(this.getDate() - Math.abs(days)); // 获取AddDayCount天后的日期
  }
  var y = this.getFullYear();
  var m = this.getMonth() + 1; // 获取当前月份的日期
  var d = this.getDate();
  if (format) {
    return new Date(y + "-" + m + "-" + d).Format(format);
  } else {
    return y + "-" + m + "-" + d;
  }
};
//格式化时间
Date.prototype.formateDate = function(datetime) {
  function addDateZero(num) {
    return num < 10 ? "0" + num : num;
  }
  let d = new Date(datetime);
  let formatdatetime =
    d.getFullYear() +
    "-" +
    addDateZero(d.getMonth() + 1) +
    "-" +
    addDateZero(d.getDate()) +
    " " +
    addDateZero(d.getHours()) +
    ":" +
    addDateZero(d.getMinutes()) +
    ":" +
    addDateZero(d.getSeconds());
  return formatdatetime;
};
//把时间戳转时间
Date.prototype.toDate = function(string) {
  let year = new Date(string).getFullYear();
  let month = new Date(string).getMonth() + 1;
  let date = new Date(string).getDate();
  let hour = new Date(string).getHours();
  let minute = new Date(string).getMinutes();
  let second = new Date(string).getSeconds();
  let time = new Date().formateDate(
    year + "-" + month + "-" + date + " " + hour + ":" + minute + ":" + second
  );
  return time;
};
