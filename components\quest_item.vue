<template>
	<view class="quest_item">
		<view class="quest_item_title">
			{{questTitle}}
		</view>
		<view class="quest_item_body">
			<mp-html :content="questSub" @navigate="richTextClick"></mp-html>
		</view>
	</view>
</template>

<script>
	export default {
		name: "quest_item",
		data() {
			return {
				questTitle: "",
				questSub: ""
			};
		},
		methods: {
			setContent(data) {
				this.questTitle = ""
				this.questSub = ""
				this.questTitle = data.questTitle
				this.questSub = data.questSub
			},
			richTextClick(event) {
				if (event == "http://localhost:8081") {} else {
					let a = event.split("?")[1]
					this.$emit("changebg", a)
				}
			}
		}
	}
</script>
<style lang="scss" scoped>
	.quest_item {
		width: 100%;

		.quest_item_title {
			margin-bottom: 30rpx;
			font-size: 28rpx;
			font-weight: bold;
			color: #222222;
		}

		.quest_item_body {
			width: 690rpx;
			border-radius: 20rpx;
			padding: 24rpx;
			box-sizing: border-box;
			background-color: #FFFFFF;
			color: #222222;
			font-size: 28rpx;
		}
	}
</style>