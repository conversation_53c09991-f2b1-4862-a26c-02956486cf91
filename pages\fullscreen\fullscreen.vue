<template>
	<view class="fullapp">
		<view class="fullModel" v-if="show">
			<view class="title">为了不影响使用，本次为强制升级！</view>
			<progress :percent="progress" show-info stroke-width="15" activeColor="#007AFF" />
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				show: false,
				content: "",
				detail: {},
				progress: 0
			}
		},
		onReady() {
			// #ifdef APP-PLUS
			this.getversion()
			// #endif
		},
		methods: {
			getversion() {
				plus.runtime.getProperty(plus.runtime.appid, (wgtinfo) => {
					this.$http.post(this.$api.version).then(suc => {
						if (suc.code == 1) {
							if (wgtinfo.version == suc.data.newversion) {
								uni.reLaunch({
									url: "../home/<USER>"
								})
							} else {
								this.content = suc.data.content
								this.detail = suc.data
								uni.showModal({
									title: "发现新版本",
									content: "确认下载更新",
									success: (res) => {
										if (res.confirm == true) { //当用户确定更新，执行更新
											this.show = true
											let downloadTask = uni.downloadFile({ //执行下载
												url: suc.data.downloadurl, //下载地址
												success: downloadResult => { //下载成功
													this.show = false
													if (downloadResult
														.statusCode ==
														200
													) {
														uni.showModal({
															title: '',
															content: '更新成功，确定现在重启吗？',
															confirmText: '重启',
															confirmColor: '#EE8F57',
															success: function(
																q
															) {
																if (q
																	.confirm ==
																	true
																) {
																	plus.runtime
																		.install( //安装
																			downloadResult
																			.tempFilePath, {
																				force: true
																			},
																			function(
																				res
																			) {
																				plus.cache
																					.clear()
																				plus.runtime
																					.restart();
																			}
																		)
																} else {
																	plus.runtime
																		.quit(); // 退出应用
																}
															}
														})
													}
												}
											})
											downloadTask.onProgressUpdate((res) => {
												this.progress = res.progress
											})
										} else {
											plus.runtime.quit(); // 退出应用
										}
									}
								})
							}

						}
					})
				})
			},
			cancel() {
				this.closeModal();
				uni.reLaunch({
					url: "../home/<USER>"
				})
			},
			confirm() {},
			closeModal() {
				uni.reLaunch({
					url: "../home/<USER>"
				})
			}
		}
	}
</script>
<style scoped lang="scss">
	.fullModel /deep/ .uni-progress-bar {
		border-radius: 40rpx;
	}

	.fullModel /deep/ .uni-progress-inner-bar {
		border-radius: 40rpx;
	}

	.title {
		font-size: 32rpx;
		font-weight: 700;
		text-align: center;

	}

	.fullModel {
		width: 90vw;
		height: 20vh;
		background-color: #ffffff;
		margin: 0rpx auto;
		border-radius: 20rpx;
		padding: 3vw;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
	}

	.fullapp {
		width: 100vw;
		height: 100vh;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.3);
	}

	// .u-full-content {
	// 	background-color: #00C777;
	// }

	// .u-update-content {
	// 	font-size: 26rpx;
	// 	color: $u-content-color;
	// 	line-height: 1.7;
	// 	padding: 30rpx;
	// }
</style>