<template>
	<view class="ki_content" @click.stop="toDetail()">
		<view class="kip_img">
			<image :src="baseUrl+item.img" mode="aspectFill" style="width: 100%;height: 100%;"></image>
		</view>
		<view class="kip_word">
			<view class="tn-text-ellipsis-2 kip_name">
				{{item.title}}
			</view>
			<view class="price tn-text-bold">
				{{"￥"+item.price}}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "product_item",
		props: {
			item: {
				type: Object,
				default: () => {}
			},
		},
		data() {
			return {
				baseUrl: this.$config.baseUrl
			};
		},
		methods: {
			toDetail() {
				this.$emit("toDetail", this.item)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.price {
		color: #F13F19;
		font-size: 36rpx;
	}

	.kip_word {
		width: 100%;
		height: 158rpx;
		padding: 20rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.kip_name {
		color: #333333;
		font-size: 26rpx;
	}

	.kip_img {
		width: 336rpx;
		height: 336rpx;
		border-radius: 20rpx 20rpx 0rpx 0rpx;
		overflow: hidden;
	}

	.ki_content {
		width: 336rpx;
		height: 494rpx;
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		background-color: #FFFFFF;
		box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(0, 0, 0, 0.08);
	
	}
</style>