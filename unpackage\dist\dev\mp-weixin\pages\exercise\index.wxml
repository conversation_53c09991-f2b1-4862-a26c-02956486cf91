<view class="data-v-d0826dd8"><view class="top_model tn-width-full data-v-d0826dd8"><view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center data-v-d0826dd8"><view class="exercise_info tn-height-full tn-flex tn-flex-direction-column tn-flex-row-between data-v-d0826dd8"><view class="name tn-text-ellipsis-2 data-v-d0826dd8" style="width:380rpx;">{{''+name+''}}</view></view><view class="subsect data-v-d0826dd8"><tn-subsection vue-id="601ada72-1" list="{{list}}" height="{{72}}" inactiveColor="#5552FF" buttonColor="#5552FF" current="{{current}}" data-event-opts="{{[['^change',[['changeSub']]]]}}" bind:change="__e" class="data-v-d0826dd8" bind:__l="__l"></tn-subsection></view></view><view class="tn-width-full data-v-d0826dd8" style="margin-top:30rpx;"><scroll-view class="scroll-view-x data-v-d0826dd8" scroll-x="true"><block wx:for="{{list_type}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeTem',['$0'],[[['list_type','',index]]]]]]]}}" class="{{['scroll-view-item','data-v-d0826dd8',item.template_id==templateId?'sel_tem':'']}}" catchtap="__e">{{''+item.template_name+''}}</view></block></scroll-view></view></view><view class="body data-v-d0826dd8"><block wx:for="{{chapterList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tn-width-full data-v-d0826dd8" style="margin-bottom:20rpx;"><exercise-item vue-id="{{'601ada72-2-'+index}}" item="{{item}}" data-event-opts="{{[['^toQuest',[['toQuest']]],['^toEnd',[['toEnd']]]]}}" bind:toQuest="__e" bind:toEnd="__e" class="data-v-d0826dd8" bind:__l="__l"></exercise-item></view></block></view><tn-popup bind:input="__e" vue-id="601ada72-3" mode="bottom" borderRadius="{{40}}" value="{{showReason}}" data-event-opts="{{[['^input',[['__set_model',['','showReason','$event',[]]]]]]}}" class="data-v-d0826dd8" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold data-v-d0826dd8" style="width:750rpx;height:120rpx;padding:30rpx 30rpx 0rpx 30rpx;box-sizing:border-box;background:url('../../static/pop_head.png') no-repeat;background-size:100% 100%;">提示</view><view class="tn-width-full data-v-d0826dd8"><view class="tn-flex tn-flex-row-center tn-flex-direction-column tn-flex-col-center data-v-d0826dd8" style="padding:30rpx 46rpx 66rpx 46rpx;box-sizing:border-box;"><view class="tn-width-full tn-text-left data-v-d0826dd8">{{''+reason+''}}</view><view data-event-opts="{{[['tap',[['toShare']]]]}}" class="submit data-v-d0826dd8" catchtap="__e">立即分享</view></view></view></tn-popup></view>