<template>
	<view class="">
		<view class="header">
			<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between">
				<view class="tn-flex tn-flex-col-center">
					<tn-avatar size="xl" shape="circle" :src="userForm.avatar"></tn-avatar>
					<view class="name_warp">
						<view class="name tn-text-bold tn-text-ellipsis">
							{{ userForm.nick }}
						</view>
						<view class="tn-flex tn-flex-col-center" style="margin-top: 10rpx;">
							<view class="star_l tn-flex tn-flex-col-center" v-if="userInfo.exam_status==1">
								<image src="../../static/icon/st_vip.png" mode="widthFix" style="width: 130rpx;">
								</image>
							</view>
							<view style="width: 10rpx;"></view>
							<view class="star_r tn-flex tn-flex-col-center" v-if="userInfo.recite_status==1">
								<image src="../../static/icon/bs_vip.png" mode="widthFix" style="width: 130rpx;">
								</image>
							</view>
						</view>
					</view>
				</view>
				<view class="set_btn tn-flex tn-flex-col-center tn-flex-row-center" @click.stop="showUser=true">
					设置个性化头像昵称
				</view>
			</view>
			<view class="vip_warp tn-flex tn-flex-row-between">
				<view class="vip_inner vi_l" @click.stop="toShare()">
					<view class="vi_t tn-flex tn-flex-col-center">
						刷题会员<view class="tips">限时免费</view>
					</view>
					<view class="vi_time">
						<text v-if="userInfo.exam_status != 0">有效期至{{ userInfo.exam_valid_time }}</text>
					</view>
					<view class="vi_btn vib_l">
						免费解锁
					</view>
				</view>
				<view class="vip_inner vi_r" @click.stop="toCode()">
					<view class="vi_t">
						背诵＋刷题会员
					</view>
					<view class="vi_time">
						<text v-if="userInfo.recite_status != 0">有效期至{{ userInfo.recite_valid_time }}</text>
					</view>
					<view class="vi_btn vib_r">
						立即认证
					</view>
				</view>
			</view>
		</view>
		<view class="body">
			<view class="poster tn-flex tn-flex-col-center tn-flex-row-between" @click="$publicjs.goMinifun()">
				<image class="posterbg" :src="baseUrl+share_back" mode=""></image>
				<!-- <view class="" style="position: relative;z-index: 10;">
					<view class="tn-text-lg tn-text-bold">考研政治 刷题＋背诵</view>
					<view class="tn-text-sm">名师串串香--串烧名师，真香！</view>
				</view>
				<view class="poster_btn" @click="" style="position: relative;z-index: 10;">免费刷题</view> -->
			</view>
			<view class="meau tn-flex tn-flex-direction-column tn-flex-row-between">
				<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between" @click.stop="showKefu=true">
					<view class="tn-flex tn-flex-col-center">
						<image src="../../static/icon/kefu.png" mode="widthFix" style="width: 50rpx;height: 50rpx;">
						</image>
						<view class="tn-text-md tn-text-bold" style="color: #222222;margin-left: 20rpx;">
							客服帮助
						</view>
					</view>
					<view class="tn-icon tn-icon-right"></view>
				</view>
				<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between" @click="updateManagerfun">
					<view class="tn-flex tn-flex-col-center">
						<image src="../../static/icon/version.png" mode="widthFix" style="width: 50rpx;height: 50rpx;">
						</image>
						<view class="tn-text-md tn-text-bold" style="color: #222222;margin-left: 20rpx;">
							小程序更新
						</view>
					</view>
					<view class="tn-icon tn-icon-right"></view>
				</view>
			</view>
		</view>
		<tn-popup v-model="showKefu" mode="bottom" :borderRadius="40">
			<view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				客服帮助</view>
			<view class="tn-width-full" style="">
				<view class="tn-flex tn-flex-row-center"
					style="padding: 30rpx 46rpx 66rpx 46rpx;box-sizing: border-box;">
					<view style="width: 400rpx;height: 400rpx;">
						<image show-menu-by-longpress style="width: 400rpx;height: 400rpx;" :src="baseUrl+service_img"
							mode=""></image>
					</view>
				</view>
			</view>
		</tn-popup>
		<tn-popup v-model="showUser" mode="center" width="530" class="userPop">
			<view class="tn-flex tn-flex-direction-column tn-flex-col-center" style="position: relative;">
				<view
					class="tn-width-full tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-between tn-bg-white user_set">
					<button class="user_avatar" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
						<view style="width: 100%;height: 100%;border-radius: 50%;" v-if="avatar">
							<image :src="baseUrl+avatar" mode="aspectFit" style="width: 100%;height: 100%;">
							</image>
						</view>
						<view style="width: 100%;height: 100%;border-radius: 50%;"
							class="tn-flex tn-flex-col-center tn-flex-row-center" v-else>
							<text class="tn-icon-add" style="font-size: 38rpx;color: #666666;"></text>
						</view>
					</button>
					<view class="user_name_warp tn-flex tn-flex-col-center tn-flex-row-between">
						<view class="tn-flex tn-flex-direction-column tn-flex-col-center" style="margin-right: 50rpx;">
							<image src="../../static/icon/user.png" mode="widthFix" style="width: 52rpx;height: 52rpx;">
							</image>
							<text style="font-size: 22rpx;">昵称</text>
						</view>
						<input type="nickname" v-model="nick" placeholder="请输入昵称" @blur="bindblur"></input>
					</view>
					<view class="submit" @click.stop="submitUser()">立即授权</view>
				</view>
				<image src="../../static/icon/close.png" mode="widthFix"
					style="width: 100rpx;height: 100rpx;margin-top: 40rpx;" @click.stop="showUser=false"></image>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	import shareMixin from "@/mixins/shareMixin.js"
	export default {
		mixins: [shareMixin],
		data() {
			return {
				baseUrl: this.$config.baseUrl,
				showKefu: false,
				showUser: false,
				nick: '',
				avatar: '',
				userForm: {
					nick: "",
					avatar: ''
				},
				service_img: '',
				share_back: '',
				userInfo: {}
			}
		},
		onShow() {
			this.getsysconfig()
			this.getUserInfo()
		},
		onLoad() {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
		},

		methods: {
			toShare() {
				if (this.userInfo.exam_status == 1) {
					uni.switchTab({
						url: "/pages/quest/quest"
					})
				} else {
					uni.navigateTo({
						url: "./share"
					})
				}
			},
			toCode() {
				if (this.userInfo.recite_status == 1) {
					uni.switchTab({
						url: "/pages/rember/rember"
					})
				} else {
					uni.navigateTo({
						url: "./code"
					})
				}
			},
			bindblur(event) {
				this.nick = event.target.value;
			},
			submitUser() {
				this.$http.post(this.$api.setUser, {
					avatar: this.avatar,
					nick: this.nick
				}).then(res => {
					if (res.code == 200) {
						this.getUserInfo();
						this.showUser = false
						this.nick = ""
						this.avatar = ""
					}
				})
			},
			onChooseAvatar(res) {
				uni.uploadFile({
					url: this.$config.baseUrl + '/api/upload/upload',
					filePath: res.detail.avatarUrl,
					name: 'file',
					header: {
						'Authorization': uni.getStorageSync('TOKEN'),
					},
					success: (uploadFileRes) => {
						this.avatar = JSON.parse(uploadFileRes.data).data.src
					},
					fail(err) {
						console.log(err);
					}
				});
			},
			getUserInfo() {
				this.$http.post(this.$api.getUserInfo, {}).then(res => {
					if (res.code == 200) {
						this.userInfo = res.data
						this.userForm.nick = res.data.nick
						if (res.data.avatar) {
							this.userForm.avatar = this.baseUrl + res.data.avatar
						}
						uni.setStorageSync('userinfo', res.data)
					}
				})
			},
			getsysconfig() { // 获取系统配置
				this.$http.post(this.$api.systemData, {}).then(res => {
					if (res.code == 200) {
						let datas = res.data;
						this.service_img = datas.config.service_img
						this.share_back = datas.config.share_back;
						uni.setStorageSync('systemData', datas)
					}
				})
			},
			updateManagerfun() { // 更新
				const updateManager = uni.getUpdateManager();
				updateManager.onCheckForUpdate(res => {
					if (res.hasUpdate) {
						uni.showModal({
							content: '新版本已经准备好，是否重启应用？',
							showCancel: false,
							confirmText: '确定',
							success: res => {
								if (res.confirm) {
									updateManager.onUpdateReady(function(res) {
										updateManager.applyUpdate();
									});
									updateManager.onUpdateFailed(res => { // 新版本下载失败的回调
										// 新版本下载失败，提示用户删除后通过冷启动重新打开
										uni.showModal({
											content: '下载失败，请删除当前小程序后重新打开',
											showCancel: false,
											confirmText: '知道了'
										})
									})
								}
							}
						})
					} else {
						this.$tools.toast('当前已经是最新版本')
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.star_l {
		width: 120rpx;
		height: 40rpx;
		border-radius: 24rpx;
		background-color: #DAE9FF;
	}

	.star_r {
		width: 120rpx;
		height: 40rpx;
		border-radius: 24rpx;
		background-color: #DDFFF5;
	}

	.submit {
		width: 422rpx;
		height: 100rpx;
		border-radius: 60rpx;
		background: #3775F6;
		color: #FFFFFF;
		font-size: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.user_name_warp {
		width: 422rpx;
		height: 120rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		box-sizing: border-box;
		border: 1px solid #7270FF;
		padding: 0rpx 36rpx;
	}

	.user_avatar {
		width: 140rpx;
		height: 140rpx;
		border-radius: 50%;
		padding: 0rpx;
		overflow: hidden;
		background-color: #FFFFFF;
	}

	.user_set {
		padding: 50rpx;
		box-sizing: border-box;
		height: 552rpx;
		border-radius: 40rpx;
		background: url('../../static/pop_c_bg.png') no-repeat;
		background-size: 100% auto;
	}

	.userPop /deep/ .tn-popup__content__center_box {
		background-color: transparent !important;
	}

	.meau {
		width: 690rpx;
		height: 236rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		margin-top: 20rpx;
		padding: 40rpx 30rpx;
	}

	.poster_btn {
		width: 152rpx;
		height: 56rpx;
		border-radius: 50rpx;
		background: linear-gradient(180deg, #EBC877 0%, #FCAF92 100%);
		font-size: 26rpx;
		color: #FFFFFF;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.poster {
		width: 690rpx;
		height: 160rpx;
		border-radius: 20rpx;
		padding: 36rpx 36rpx 36rpx 120rpx;
		position: relative;
	}

	.posterbg {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		z-index: 0;
	}

	.body {
		width: 100%;
		padding: 0rpx 30rpx 30rpx 30rpx;
		box-sizing: border-box;
	}

	.tips {
		width: 120rpx;
		height: 36rpx;
		line-height: 38rpx;
		border-radius: 20rpx 20rpx 20rpx 0rpx;
		background: linear-gradient(180deg, #FF9C4C 0%, #FFB200 100%);
		font-size: 20rpx;
		color: #FFFFFF;
		text-align: center;
		margin-left: 10rpx;
		font-weight: normal;
	}

	.vib_l {
		background: linear-gradient(180deg, #E7BE6C 0%, #C8A956 100%);
	}

	.vib_r {
		background: linear-gradient(180deg, #FCAF92 0%, #FF7076 100%);
	}

	.vi_l {
		background: url("../../static/vip_l.png") no-repeat;
		background-size: 100% 100%;
	}

	.vi_r {
		background: url("../../static/vip_r.png") no-repeat;
		background-size: 100% 100%;
	}

	.vip_warp {
		width: 690rpx;
		padding: 30rpx;
		box-sizing: border-box;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		margin-top: 50rpx;

		.vip_inner {
			width: 306rpx;
			height: 230rpx;
			box-sizing: border-box;
			border-radius: 20rpx;
			padding: 24rpx 24rpx 36rpx 24rpx;

			.vi_t {
				color: #222222;
				font-size: 32rpx;
				font-weight: 600;
			}

			.vi_time {
				color: #222222;
				font-size: 24rpx;
				margin-top: 10rpx;
				height: 30rpx;
			}

			.vi_btn {
				width: 152rpx;
				height: 56rpx;
				border-radius: 50rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				color: #FFFFFF;
				font-size: 26rpx;
				margin-top: 28rpx;
			}
		}
	}

	.name {
		font-size: 18px;
	}

	.name_warp {
		width: 250rpx;
		margin-left: 15rpx;
	}

	.set_btn {
		width: 282rpx;
		height: 80rpx;
		border-radius: 40rpx;
		background-color: #5552FF;
		color: #ffffff;
		font-size: 26rpx;
	}

	.header {
		width: 100%;
		background: linear-gradient(180deg, #F1F0FF 71%, rgba(255, 255, 255, 0) 99%) no-repeat;
		background-size: 100% 304rpx;
		box-sizing: border-box;
		padding: 30rpx 30rpx 20rpx 30rpx;
	}
</style>