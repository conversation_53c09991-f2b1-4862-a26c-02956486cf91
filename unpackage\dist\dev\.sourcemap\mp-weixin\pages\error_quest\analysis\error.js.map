{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/project/shuati_new/pages/error_quest/analysis/error.vue?c06f", "webpack:///D:/project/shuati_new/pages/error_quest/analysis/error.vue?903b", "webpack:///D:/project/shuati_new/pages/error_quest/analysis/error.vue?932e", "webpack:///D:/project/shuati_new/pages/error_quest/analysis/error.vue?855f", "uni-app:///pages/error_quest/analysis/error.vue", "webpack:///D:/project/shuati_new/pages/error_quest/analysis/error.vue?1b4d", "webpack:///D:/project/shuati_new/pages/error_quest/analysis/error.vue?e78f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "answerCom", "questFixedRem", "answerComTum", "noteOther", "noteMine", "data", "questList", "questItem", "questIndex", "cardIndex", "showError", "showAdd", "showEdit", "showColl", "noteType", "note_value", "baseUrl", "list_type", "name", "current", "noteList", "page", "more", "is_sort", "editItem", "collItem", "isEnd", "topicRecordList", "mask", "chapterId", "sel<PERSON><PERSON><PERSON>", "err_value", "idArr", "onLoad", "withShareTicket", "menus", "console", "onReachBottom", "methods", "submitErrorQuest", "uni", "title", "icon", "content", "topic_id", "toColl", "getTopicRecord", "submitEnd", "showCancel", "confirmText", "success", "nextQuestBefore", "lastQuestBefore", "submitNoteColl", "id", "toCollectOther", "getNoteBefore", "toMineDel", "that", "toMineEdit", "changeSort", "submitNoteEdit", "submitNote", "type", "getListNote", "limit", "sort", "change", "getQlist", "topic_id_arr"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sQAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChIA;AAAA;AAAA;AAAA;AAAinB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8OroB;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;MACA;QACAA;MACA;QACAA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEAvC;MAAA;MACAwC;MACAC;IACA;IAEAC;IACA;MACA;MACA;MACA;IACA;EACA;EAEAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;MACA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MACA;QACAD;QACAE;QACAC;MACA;MACA;QACA;UACAJ;YACAC;YACAC;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAG;MAAA;MACA;QACAD;MACA;QACA;UACAJ;YACAC;YACAC;UACA;UACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IACAI;MAAA;MACA;QACAF;MACA;QACA;UACA;QACA;MACA;IACA;IACAG;MACAP;QACAC;QACAE;QACAK;QACAC;QACAC;UACA;YACAV;UACA;QACA;MACA;IACA;IACAW;MACA;MACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;QACA;MACA;IACA;IACAC;MACA;QACAZ;UACAC;UACAC;QACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IAEAW;MAAA;MACA;QACAV;QACAW;MACA;MACA;QACA;UACAd;YACAC;YACAC;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAa;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACAjB;QACAC;QACAS;UACA;YACAQ;cACAJ;YACA;cACA;gBACAd;kBACAC;kBACAC;gBACA;gBACAgB;cACA;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACAlB;QACAW;MACA;MACA;QACA;UACAd;YACAC;YACAC;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAoB;MAAA;MACA;QACAnB;QACAoB;MACA;MACA;QACA1D;MACA;MACA;QACAA;MACA;MACA;QACA;UACAmC;YACAC;YACAC;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAsB;MAAA;MACA;QACA3C;QACA4C;QACArB;QACAsB;MACA;MACA;QACA7D;MACA;MACA;QACAA;MACA;MACA;QACA;UACA;YACA;UACA;YACA;cACA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA8D;MACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACAC;MACA;QACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1iBA;AAAA;AAAA;AAAA;AAAgsC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACAptC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/error_quest/analysis/error.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/error_quest/analysis/error.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./error.vue?vue&type=template&id=17602e72&scoped=true&\"\nvar renderjs\nimport script from \"./error.vue?vue&type=script&lang=js&\"\nexport * from \"./error.vue?vue&type=script&lang=js&\"\nimport style0 from \"./error.vue?vue&type=style&index=0&id=17602e72&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"17602e72\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/error_quest/analysis/error.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./error.vue?vue&type=template&id=17602e72&scoped=true&\"", "var components\ntry {\n  components = {\n    tnTabs: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-tabs/tn-tabs\" */ \"@/tuniao-ui/components/tn-tabs/tn-tabs.vue\"\n      )\n    },\n    tnPopup: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-popup/tn-popup\" */ \"@/tuniao-ui/components/tn-popup/tn-popup.vue\"\n      )\n    },\n    tnInput: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-input/tn-input\" */ \"@/tuniao-ui/components/tn-input/tn-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.questList.length\n  var g1 = _vm.questItem.title_img && _vm.questItem.title_img.length > 0\n  var l0 = _vm.__map(_vm.questItem.option, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g2 = !item.answer\n      ? _vm.questItem.answer_record.indexOf(String(Number(index) + 1))\n      : null\n    return {\n      $orig: $orig,\n      g2: g2,\n    }\n  })\n  var l1 = _vm.__map(_vm.topicRecordList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g3 = _vm.$publicjs.numToCode([item])\n    return {\n      $orig: $orig,\n      g3: g3,\n    }\n  })\n  var g4 =\n    _vm.current == 0 &&\n    _vm.questItem.analysis_img &&\n    _vm.questItem.analysis_img.length > 0\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.showError = true\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      _vm.showAdd = true\n    }\n    _vm.e2 = function ($event) {\n      $event.stopPropagation()\n      _vm.showAdd = true\n    }\n    _vm.e3 = function ($event) {\n      _vm.showAdd = true\n    }\n    _vm.e4 = function ($event) {\n      $event.stopPropagation()\n      _vm.selError = \"有错别字\"\n    }\n    _vm.e5 = function ($event) {\n      $event.stopPropagation()\n      _vm.selError = \"题干有误\"\n    }\n    _vm.e6 = function ($event) {\n      $event.stopPropagation()\n      _vm.selError = \"答案有误\"\n    }\n    _vm.e7 = function ($event) {\n      $event.stopPropagation()\n      _vm.selError = \"解析有误\"\n    }\n    _vm.e8 = function ($event) {\n      $event.stopPropagation()\n      _vm.selError = \"解析缺失\"\n    }\n    _vm.e9 = function ($event) {\n      $event.stopPropagation()\n      _vm.selError = \"选择有误\"\n    }\n    _vm.e10 = function ($event) {\n      $event.stopPropagation()\n      _vm.noteType = 0\n    }\n    _vm.e11 = function ($event) {\n      $event.stopPropagation()\n      _vm.noteType = 1\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        l1: l1,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./error.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./error.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"padding-bottom: 140rpx;\">\r\n\t\t<view class=\"quest_primary tn-bg-white\">\r\n\t\t\t<view class=\"qp_top tn-flex tn-flex-row-between tn-flex-col-center\">\r\n\t\t\t\t<view class=\"qpt_left tn-flex tn-flex-col-center\">\r\n\t\t\t\t\t<view class=\"type_tips\">\r\n\t\t\t\t\t\t<text v-if=\"questItem.type==0\">单选题</text>\r\n\t\t\t\t\t\t<text v-if=\"questItem.type==1\">多选题</text>\r\n\t\t\t\t\t\t<text v-if=\"questItem.type==2\">不定项</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text style=\"margin-left: 0rpx; font-size: 28rpx;color: #999999;\"> <text\r\n\t\t\t\t\t\t\tstyle=\"color: #FF000A;\">{{questIndex+1}}</text> <text style=\"margin: 0rpx 5rpx;\">/</text>\r\n\t\t\t\t\t\t{{questList.length}} </text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-flex tn-flex-col-center\">\r\n\t\t\t\t\t<view style=\"font-size: 28rpx;color: #A1A1A1;\" v-if=\"questItem.is_collect==1\"\r\n\t\t\t\t\t\************=\"toColl()\">\r\n\t\t\t\t\t\t<text class=\"tn-icon-star-fill\" style=\"color: #FFBD23;\"></text> 已收藏\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"font-size: 28rpx;color: #A1A1A1;\" v-if=\"questItem.is_collect==0\"\r\n\t\t\t\t\t\************=\"toColl()\">\r\n\t\t\t\t\t\t<text class=\"tn-icon-star-fill\" style=\"color: #A1A1A1;\"></text> 收藏\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"correction\" @click.stop=\"showError=true\">\r\n\t\t\t\t\t\t<text class=\"tn-icon-help-fill\" style=\"margin-right: 4rpx;\"></text> 纠错\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"quest_warp\">\r\n\t\t\t\t<view class=\"quest_title\">\r\n\t\t\t\t\t{{questItem.title_id+\"、\"}}{{questItem.title||''}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-width-full\" v-if=\"questItem.title_img&&questItem.title_img.length>0\"\r\n\t\t\t\t\tstyle=\"margin-top: 20rpx;margin-bottom: 20rpx;\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in questItem.title_img\" :key=\"index\" class=\"tn-width-full\">\r\n\t\t\t\t\t\t<image :src=\"baseUrl+item\" mode=\"widthFix\" style=\"width: 100%;\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"answer_warp tn-width-full\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in questItem.option\" :key=\"index\"\r\n\t\t\t\t\t\tclass=\"answer_item tn-flex tn-flex-col-center tn-flex-row-between\"\r\n\t\t\t\t\t\t:class=\"item.answer?'suc_bg':questItem.answer_record.indexOf(String(Number(index)+1))>=0?'err_bg':'nom_bg'\">\r\n\t\t\t\t\t\t<view :class=\"item.answer?'suc_word':'tn-width-full'\">\r\n\t\t\t\t\t\t\t{{item.option}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<image src=\"../../../static/icon/suc_sel.png\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\tstyle=\"width: 44rpx;height: 44rpx;\" v-if=\"item.answer\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"answer_history\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center\">\r\n\t\t\t\t<image src=\"../../../static/icon/his.png\" mode=\"widthFix\" style=\"width: 32rpx;\"></image>\r\n\t\t\t\t<view style=\"font-size: 32rpx;color: #333333;margin-left: 5rpx;\">\r\n\t\t\t\t\t答题记录\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-wrap\">\r\n\t\t\t\t<view v-for=\"(item,index) in topicRecordList\" :key=\"index\"\r\n\t\t\t\t\tstyle=\"width: 20%;text-align: center; margin-top: 20rpx;\">\r\n\t\t\t\t\t<view style=\"font-size: 28rpx;color: #999999;\">{{index+1}}、<text\r\n\t\t\t\t\t\t\tstyle=\"color: #FF000A;\">{{ $publicjs.numToCode([item])}}</text> </view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"answer_history\">\r\n\t\t\t<tn-tabs :list=\"list_type\" bold :isScroll=\"false\" activeColor=\"#333333\" inactiveColor=\"#333333\"\r\n\t\t\t\t:current=\"current\" name=\"name\" @change=\"change\"></tn-tabs>\r\n\t\t\t<view class=\"tn-width-full\" v-if=\"current==0&&questItem.analysis_img&&questItem.analysis_img.length>0\"\r\n\t\t\t\tstyle=\"margin-top: 20rpx;margin-bottom: 20rpx;\">\r\n\t\t\t\t<view v-for=\"(item,index) in questItem.analysis_img\" :key=\"index\" class=\"tn-width-full\">\r\n\t\t\t\t\t<image :src=\"baseUrl+item\" mode=\"widthFix\" style=\"width: 100%;\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"margin-top: 40rpx;\" v-if=\"current==0\">\r\n\t\t\t\t<text user-select>{{questItem.analysis}}</text>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"margin-top: 30rpx;\" class=\"\" v-if=\"current==1\">\r\n\t\t\t\t<view class=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center\">\r\n\t\t\t\t\t<view class=\"tn-flex tn-flex-col-center\">\r\n\t\t\t\t\t\t<view :class=\"is_sort==0?'is_sel':'no_sel'\" @click.stop=\"changeSort('0')\">\r\n\t\t\t\t\t\t\t采纳数排序\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"margin: 0rpx 10rpx;color: #D8D8D8;\">\r\n\t\t\t\t\t\t\t|\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view :class=\"is_sort==1?'is_sel':'no_sel'\" @click.stop=\"changeSort('1')\">\r\n\t\t\t\t\t\t\t时间排序\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"add_note tn-flex tn-flex-col-center tn-flex-row-center\" @click.stop=\"showAdd=true\">\r\n\t\t\t\t\t\t<image src=\"../../../static/icon/add_note.png\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\tstyle=\"width: 30rpx;margin-right: 2rpx;\"></image>\r\n\t\t\t\t\t\t<view class=\"\">\r\n\t\t\t\t\t\t\t做笔记\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-width-full\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in noteList\" :key=\"index\" class=\"tn-width-full\">\r\n\t\t\t\t\t\t<note-other :item=\"item\" @toCollect=\"toCollectOther\"></note-other>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"margin-top: 30rpx;\" class=\"\" v-if=\"current==2\">\r\n\t\t\t\t<view class=\"mine_note_top tn-width-full tn-flex tn-flex-row-between tn-flex-col-center\">\r\n\t\t\t\t\t<view class=\"tn-flex tn-flex-col-center\" style=\"font-size: 24rpx;color: #333333;\">\r\n\t\t\t\t\t\t好记性不如烂笔头~\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tn-flex tn-flex-col-center tn-flex-row-center\" @click.stop=\"showAdd=true\">\r\n\t\t\t\t\t\t<image src=\"../../../static/icon/add_note.png\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\tstyle=\"width: 30rpx;margin-right: 2rpx;\"></image>\r\n\t\t\t\t\t\t<view style=\"color: #5552FF;font-size: 24rpx;\">\r\n\t\t\t\t\t\t\t做笔记\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-width-full\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in noteList\" :key=\"index\" class=\"tn-width-full\">\r\n\t\t\t\t\t\t<note-mine :item=\"item\" @todel=\"toMineDel\" @toedit=\"toMineEdit\"></note-mine>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<quest-fixed-rem :end=\"isEnd\" @nextQuest=\"nextQuestBefore\" @lastQuest=\"lastQuestBefore\" @submitNow=\"submitNow\"\r\n\t\t\t@submitEnd=\"submitEnd\" @showNote=\"showAdd=true\"></quest-fixed-rem>\r\n\r\n\t\t<tn-popup v-model=\"showError\" mode=\"bottom\" :borderRadius=\"40\" safeAreaInsetBottom>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-flex-row-between\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t<text class=\"tn-text-bold\">\r\n\t\t\t\t\t反馈\r\n\t\t\t\t</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-direction-column\"\r\n\t\t\t\tstyle=\"padding:0rpx 30rpx 30rpx 30rpx;box-sizing: border-box;\">\r\n\t\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-flex-row-between\">\r\n\t\t\t\t\t<view :class=\"selError=='有错别字'?'sel_is':'sel_no'\" @click.stop=\"selError='有错别字'\">\r\n\t\t\t\t\t\t有错别字\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view :class=\"selError=='题干有误'?'sel_is':'sel_no'\" @click.stop=\"selError='题干有误'\">\r\n\t\t\t\t\t\t题干有误\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view :class=\"selError=='答案有误'?'sel_is':'sel_no'\" @click.stop=\"selError='答案有误'\">\r\n\t\t\t\t\t\t答案有误\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-flex-row-between\" style=\"margin-top: 30rpx;\">\r\n\t\t\t\t\t<view :class=\"selError=='解析有误'?'sel_is':'sel_no'\" @click.stop=\"selError='解析有误'\">\r\n\t\t\t\t\t\t解析有误\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view :class=\"selError=='解析缺失'?'sel_is':'sel_no'\" @click.stop=\"selError='解析缺失'\">\r\n\t\t\t\t\t\t解析缺失\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view :class=\"selError=='选择有误'?'sel_is':'sel_no'\" @click.stop=\"selError='选择有误'\">\r\n\t\t\t\t\t\t选择有误\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"scroll_warp2 tn-width-full\">\r\n\t\t\t\t<tn-input v-model=\"err_value\" placeholder=\"开始输入...\" :clearable=\"false\" type=\"textarea\" :border=\"false\"\r\n\t\t\t\t\t:height=\"324\" :autoHeight=\"false\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-flex tn-flex-col-top tn-flex-row-center\" style=\"height: 150rpx;\">\r\n\t\t\t\t<view class=\"submit\" @click.stop=\"submitErrorQuest()\">立即反馈</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t\t<tn-popup v-model=\"showAdd\" mode=\"bottom\" :borderRadius=\"40\" safeAreaInsetBottom>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t<view style=\"font-size: 34rpx;\" v-if=\"noteType==1\">\r\n\t\t\t\t\t章笔记\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"font-size: 34rpx;\" v-if=\"noteType==0\">\r\n\t\t\t\t\t试题笔记\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-flex tn-flex-row-center tn-flex-col-center\"\r\n\t\t\t\t\tstyle=\"width: 180rpx;height: 48rpx;border-radius: 55rpx;background-color: #E3E3FF; color: #5552FF;font-size: 24rpx;\"\r\n\t\t\t\t\tv-if=\"noteType==1\" @click.stop=\"noteType=0\">\r\n\t\t\t\t\t<text class=\"tn-icon-edit\" style=\"font-size: 32rpx;margin-right: 5rpx;\"></text>\r\n\t\t\t\t\t试题笔记\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-flex tn-flex-row-center tn-flex-col-center\"\r\n\t\t\t\t\tstyle=\"width: 180rpx;height: 48rpx;border-radius: 55rpx;background-color: #E3E3FF; color: #5552FF;font-size: 24rpx;\"\r\n\t\t\t\t\tv-if=\"noteType==0\" @click.stop=\"noteType=1\">\r\n\t\t\t\t\t<text class=\"tn-icon-edit\" style=\"font-size: 32rpx;margin-right: 5rpx;\"></text>\r\n\t\t\t\t\t章笔记\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"scroll_warp2 tn-width-full\">\r\n\t\t\t\t<tn-input v-model=\"note_value\" placeholder=\"开始输入...\" :clearable=\"false\" type=\"textarea\" :border=\"false\"\r\n\t\t\t\t\t:height=\"324\" :autoHeight=\"false\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-flex tn-flex-col-top tn-flex-row-center\" style=\"height: 150rpx;\">\r\n\t\t\t\t<view class=\"submit\" @click.stop=\"submitNote()\">保存</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t\t<tn-popup v-model=\"showEdit\" mode=\"bottom\" :borderRadius=\"40\" safeAreaInsetBottom>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t<view style=\"font-size: 34rpx;\">\r\n\t\t\t\t\t修改笔记\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"scroll_warp2 tn-width-full\">\r\n\t\t\t\t<tn-input v-model=\"note_value\" placeholder=\"开始输入...\" :clearable=\"false\" type=\"textarea\" :border=\"false\"\r\n\t\t\t\t\t:height=\"324\" :autoHeight=\"false\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-flex tn-flex-col-top tn-flex-row-center\" style=\"height: 150rpx;\">\r\n\t\t\t\t<view class=\"submit\" @click.stop=\"submitNoteEdit()\">修改</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t\t<tn-popup v-model=\"showColl\" mode=\"bottom\" :borderRadius=\"40\" safeAreaInsetBottom>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t<view style=\"font-size: 34rpx;\">\r\n\t\t\t\t\t采纳笔记\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"scroll_warp2 tn-width-full\">\r\n\t\t\t\t<tn-input v-model=\"note_value\" placeholder=\"开始输入...\" :clearable=\"false\" type=\"textarea\" :border=\"false\"\r\n\t\t\t\t\t:height=\"324\" :autoHeight=\"false\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-flex tn-flex-col-top tn-flex-row-center\" style=\"height: 150rpx;\">\r\n\t\t\t\t<view class=\"submit\" @click.stop=\"submitNoteColl()\">修改</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport answerCom from \"@/components/exercise/answer_com.vue\"\r\n\timport answerComTum from \"@/components/exercise/answer_com_tum.vue\"\r\n\timport noteOther from \"@/components/quest/note_other.vue\"\r\n\timport noteMine from \"@/components/quest/note_mine.vue\"\r\n\timport questFixedRem from \"@/components/note/quest_fixed_rem.vue\"\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tanswerCom,\r\n\t\t\tquestFixedRem,\r\n\t\t\tanswerComTum,\r\n\t\t\tnoteOther,\r\n\t\t\tnoteMine,\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tquestList: [],\r\n\t\t\t\tquestItem: {},\r\n\t\t\t\tquestIndex: -1,\r\n\t\t\t\tcardIndex: null,\r\n\t\t\t\tshowError: false,\r\n\t\t\t\tshowAdd: false,\r\n\t\t\t\tshowEdit: false,\r\n\t\t\t\tshowColl: false,\r\n\t\t\t\tnoteType: 0,\r\n\t\t\t\tnote_value: \"\",\r\n\t\t\t\tbaseUrl: this.$config.baseUrl,\r\n\t\t\t\tlist_type: [{\r\n\t\t\t\t\tname: '解析'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tname: '研友笔记'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tname: '我的笔记',\r\n\t\t\t\t}],\r\n\t\t\t\tcurrent: 0,\r\n\t\t\t\tnoteList: [],\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tmore: true,\r\n\t\t\t\tis_sort: '0',\r\n\t\t\t\teditItem: {},\r\n\t\t\t\tcollItem: {},\r\n\t\t\t\tisEnd: -1,\r\n\t\t\t\ttopicRecordList: [],\r\n\t\t\t\tmask: [],\r\n\t\t\t\tchapterId: null,\r\n\t\t\t\tselError: '',\r\n\t\t\t\terr_value: '',\r\n\t\t\t\tidArr: []\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\tconsole.log(\"dddd\", options);\r\n\t\t\tif (options.id) {\r\n\t\t\t\tthis.chapterId = options.id\r\n\t\t\t\tthis.idArr = options.arr\r\n\t\t\t\tthis.getQlist()\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonReachBottom() {\r\n\t\t\tif (this.more) {\r\n\t\t\t\tthis.page += 1\r\n\t\t\t\tthis.getListNote()\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsubmitErrorQuest() {\r\n\t\t\t\tif (!this.selError) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"请选择反馈类目\",\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.err_value) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"请输入反馈内容\",\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\ttitle: this.selError,\r\n\t\t\t\t\tcontent: this.err_value,\r\n\t\t\t\t\ttopic_id: this.questItem.id\r\n\t\t\t\t}\r\n\t\t\t\tthis.$http.post(this.$api.errorCorrection, data).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"提交成功\",\r\n\t\t\t\t\t\t\ticon: \"success\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.selError = ''\r\n\t\t\t\t\t\tthis.err_value = ''\r\n\t\t\t\t\t\tthis.showError = false\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoColl() {\r\n\t\t\t\tthis.$http.post(this.$api.addColl, {\r\n\t\t\t\t\ttopic_id: this.questItem.id\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"操作成功\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tif (this.questItem.is_collect == 1) {\r\n\t\t\t\t\t\t\tthis.questItem.is_collect = 0\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.questItem.is_collect = 1\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetTopicRecord() {\r\n\t\t\t\tthis.$http.post(this.$api.topicRecord, {\r\n\t\t\t\t\ttopic_id: this.questItem.id\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.topicRecordList = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsubmitEnd() {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: \"小贴士\",\r\n\t\t\t\t\tcontent: \"请认真做好“错题笔记，章节笔记”，对冲刺阶段的查漏补缺有极大帮助\",\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tconfirmText: \"确定\",\r\n\t\t\t\t\tsuccess: (suc) => {\r\n\t\t\t\t\t\tif (suc.confirm) {\r\n\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tnextQuestBefore() {\r\n\t\t\t\tthis.current = 0\r\n\t\t\t\tif (this.questList.length - 1 == this.questIndex) {} else {\r\n\t\t\t\t\tthis.questIndex += 1\r\n\t\t\t\t\tthis.questItem = this.questList[this.questIndex]\r\n\t\t\t\t\tif (this.questList.length - 1 == this.questIndex) {\r\n\t\t\t\t\t\tthis.isEnd = 2\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.isEnd = 1\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.getTopicRecord()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tlastQuestBefore() {\r\n\t\t\t\tif (this.questIndex == 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"当前已是第一题\",\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.current = 0\r\n\t\t\t\t\tthis.questIndex -= 1\r\n\t\t\t\t\tthis.questItem = this.questList[this.questIndex]\r\n\t\t\t\t\tthis.isEnd = 1\r\n\t\t\t\t\tthis.getTopicRecord()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tsubmitNoteColl() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tcontent: this.note_value,\r\n\t\t\t\t\tid: this.collItem.id,\r\n\t\t\t\t}\r\n\t\t\t\tthis.$http.post(this.$api.acceptNote, data).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"采纳成功\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.note_value = \"\"\r\n\t\t\t\t\t\tthis.collItem = {}\r\n\t\t\t\t\t\tthis.showColl = false\r\n\t\t\t\t\t\tthis.getNoteBefore()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoCollectOther(item) {\r\n\t\t\t\tthis.collItem = item\r\n\t\t\t\tthis.note_value = item.content\r\n\t\t\t\tthis.showColl = true\r\n\t\t\t},\r\n\t\t\tgetNoteBefore() {\r\n\t\t\t\tthis.page = 1\r\n\t\t\t\tthis.noteList = []\r\n\t\t\t\tthis.more = true\r\n\t\t\t\tthis.getListNote()\r\n\t\t\t},\r\n\t\t\ttoMineDel(item) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: \"确认删除当前笔记吗？\",\r\n\t\t\t\t\tsuccess: (suc) => {\r\n\t\t\t\t\t\tif (suc.confirm) {\r\n\t\t\t\t\t\t\tthat.$http.post(that.$api.delNote, {\r\n\t\t\t\t\t\t\t\tid: item.id\r\n\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: \"已删除\",\r\n\t\t\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\tthat.getNoteBefore()\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoMineEdit(item) {\r\n\t\t\t\tthis.editItem = item\r\n\t\t\t\tthis.note_value = item.content\r\n\t\t\t\tthis.showEdit = true\r\n\t\t\t},\r\n\t\t\tchangeSort(num) {\r\n\t\t\t\tif (num != this.is_sort) {\r\n\t\t\t\t\tthis.is_sort = num\r\n\t\t\t\t\tthis.getNoteBefore()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsubmitNoteEdit() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tcontent: this.note_value,\r\n\t\t\t\t\tid: this.editItem.id,\r\n\t\t\t\t}\r\n\t\t\t\tthis.$http.post(this.$api.editNote, data).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"已修改\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.note_value = \"\"\r\n\t\t\t\t\t\tthis.editItem = {}\r\n\t\t\t\t\t\tthis.showEdit = false\r\n\t\t\t\t\t\tthis.getNoteBefore()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsubmitNote() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tcontent: this.note_value,\r\n\t\t\t\t\ttype: this.noteType,\r\n\t\t\t\t}\r\n\t\t\t\tif (this.noteType == 0) {\r\n\t\t\t\t\tdata.topic_id = this.questItem.id\r\n\t\t\t\t}\r\n\t\t\t\tif (this.noteType == 1) {\r\n\t\t\t\t\tdata.chapter_id = this.chapterId\r\n\t\t\t\t}\r\n\t\t\t\tthis.$http.post(this.$api.addNote, data).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"已新增\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.note_value = \"\"\r\n\t\t\t\t\t\tthis.showAdd = false\r\n\t\t\t\t\t\tthis.getNoteBefore()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetListNote() {\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpage: this.page,\r\n\t\t\t\t\tlimit: 10,\r\n\t\t\t\t\ttopic_id: this.questItem.id,\r\n\t\t\t\t\tsort: this.is_sort\r\n\t\t\t\t}\r\n\t\t\t\tif (this.current == 1) {\r\n\t\t\t\t\tdata.type = \"0\"\r\n\t\t\t\t}\r\n\t\t\t\tif (this.current == 2) {\r\n\t\t\t\t\tdata.type = \"1\"\r\n\t\t\t\t}\r\n\t\t\t\tthis.$http.post(this.$api.listNote, data).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tif (this.page == 1) {\r\n\t\t\t\t\t\t\tthis.noteList = res.data.data\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tif (res.data.data.length == 0) {\r\n\t\t\t\t\t\t\t\tthis.more = false\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.noteList = this.noteList.concat(res.data.data)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tchange(index) {\r\n\t\t\t\tif (this.current != index) {\r\n\t\t\t\t\tthis.current = index;\r\n\t\t\t\t\tif (this.current != 0) {\r\n\t\t\t\t\t\tthis.getNoteBefore()\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetQlist() {\r\n\t\t\t\tthis.$http.post(this.$api.errorQuestAnalysis, {\r\n\t\t\t\t\ttopic_id_arr: this.idArr\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.questList = res.data\r\n\t\t\t\t\t\tthis.isEnd = 1\r\n\t\t\t\t\t\tthis.questItem = res.data[0]\r\n\t\t\t\t\t\tthis.questIndex = 0\r\n\t\t\t\t\t\tthis.getTopicRecord()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.sel_no {\r\n\t\twidth: 200rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 55rpx;\r\n\t\tbackground-color: #F1F1F1;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.sel_is {\r\n\t\twidth: 200rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 55rpx;\r\n\t\tbackground-color: #5552FF;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.scroll_warp2 {\r\n\t\twidth: 690rpx;\r\n\t\theight: 384rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #F8F8F8;\r\n\t\tmargin: 0rpx auto;\r\n\t\tmargin-bottom: 26rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.submit {\r\n\t\twidth: 630rpx;\r\n\t\theight: 96rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t\tbackground-color: #5552FF;\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #FFFFFF;\r\n\t\ttext-align: center;\r\n\t\tline-height: 96rpx;\r\n\t}\r\n\r\n\t.mine_note_top {\r\n\t\twidth: 690rpx;\r\n\t\theight: 66rpx;\r\n\t\tborder-radius: 55rpx;\r\n\t\tbackground-color: #E3E3FF;\r\n\t\tpadding: 0rpx 30rpx;\r\n\t}\r\n\r\n\t.add_note {\r\n\t\twidth: 146rpx;\r\n\t\theight: 50rpx;\r\n\t\tborder-radius: 55rpx;\r\n\t\tbackground-color: #E3E3FF;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #5552FF;\r\n\t}\r\n\r\n\t.no_sel {\r\n\t\tcolor: #333333;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.is_sel {\r\n\t\tcolor: #5552FF;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.answer_history {\r\n\t\twidth: 750rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tmargin: 20rpx 0rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.answer_mask {\r\n\t\tposition: absolute;\r\n\t\ttop: 0rpx;\r\n\t\tbottom: 0rpx;\r\n\t\tleft: 0rpx;\r\n\t\tright: 0rpx;\r\n\t\tbackground: linear-gradient(180deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.05) 100%);\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t\tz-index: 100;\r\n\t}\r\n\r\n\t.answer_mask_title {\r\n\t\tposition: absolute;\r\n\t\ttop: 0rpx;\r\n\t\tbottom: 0rpx;\r\n\t\tleft: 0rpx;\r\n\t\tright: 0rpx;\r\n\t\tz-index: 120;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.answer_warp {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.suc_word {\r\n\t\twidth: 550rpx;\r\n\t}\r\n\r\n\t.nom_bg {\r\n\t\tbackground-color: #F7F7F7;\r\n\t}\r\n\r\n\t.err_bg {\r\n\t\tbackground-color: #FFD8D9;\r\n\t}\r\n\r\n\t.suc_bg {\r\n\t\tbackground-color: #DDFFF5;\r\n\t}\r\n\r\n\t.answer_item {\r\n\t\twidth: 690rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\tcolor: #333333;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\r\n\t.cri_red {\r\n\t\twidth: 14rpx;\r\n\t\theight: 14rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-radius: 50%;\r\n\t\tborder: 2rpx solid #FF585F;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.cri_blue {\r\n\t\twidth: 14rpx;\r\n\t\theight: 14rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-radius: 50%;\r\n\t\tborder: 2rpx solid #5552FF;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.cri_gary {\r\n\t\twidth: 14rpx;\r\n\t\theight: 14rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-radius: 50%;\r\n\t\tborder: 2rpx solid #C8C8C8;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.card_no {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 55rpx;\r\n\t\tbackground-color: #C8C8C8;\r\n\t\ttext-align: center;\r\n\t\tline-height: 100rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.card_now {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 55rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 100rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #5552FF;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\tmargin-left: 20rpx;\r\n\t\tborder: 2rpx solid #5552FF;\r\n\t}\r\n\r\n\t.card_sub {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 55rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 100rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #FF585F;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\tmargin-left: 20rpx;\r\n\t\tborder: 2rpx solid #FF585F;\r\n\t}\r\n\r\n\t.scroll_warp {\r\n\t\theight: 450rpx;\r\n\t\tpadding: 0rpx 40rpx 40rpx 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.quest_title {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333333;\r\n\t\tmargin-bottom: 50rpx;\r\n\t}\r\n\r\n\t.quest_warp {\r\n\t\tmargin-top: 50rpx;\r\n\t}\r\n\r\n\t.correction {\r\n\t\twidth: 122rpx;\r\n\t\theight: 44rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tbackground-color: #EEEEEE;\r\n\t\ttext-align: center;\r\n\t\tline-height: 44rpx;\r\n\t\tcolor: #A1A1A1;\r\n\t\tfont-size: 26rpx;\r\n\t\tmargin-left: 48rpx;\r\n\t}\r\n\r\n\t.type_tips {\r\n\t\twidth: 114rpx;\r\n\t\theight: 52rpx;\r\n\t\tborder-radius: 26rpx 0rpx 26rpx 0rpx;\r\n\t\tbackground-color: #5552FF;\r\n\t\ttext-align: center;\r\n\t\tline-height: 52rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-right: 30rpx;\r\n\t}\r\n\r\n\t.quest_primary {\r\n\t\twidth: 750rpx;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-radius: 0rpx 0rpx 20rpx 20rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./error.vue?vue&type=style&index=0&id=17602e72&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./error.vue?vue&type=style&index=0&id=17602e72&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980620057\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}