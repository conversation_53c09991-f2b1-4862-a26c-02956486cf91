{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-popup/tn-popup.vue?15b7", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-popup/tn-popup.vue?2daf", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-popup/tn-popup.vue?cc82", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-popup/tn-popup.vue?803d", "uni-app:///tuniao-ui/components/tn-popup/tn-popup.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-popup/tn-popup.vue?358b", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-popup/tn-popup.vue?7966"], "names": ["mixins", "name", "props", "value", "type", "default", "mode", "mask", "length", "width", "height", "zoom", "safeAreaInsetBottom", "maskCloseable", "customStyle", "borderRadius", "zIndex", "closeBtn", "closeBtnIcon", "closeBtnPosition", "closeIconColor", "closeIconSize", "negativeTop", "marginTop", "popup", "computed", "popupStyle", "style", "contentStyle", "transform", "centerStyle", "closeBtnStyle", "elZIndex", "data", "timer", "visibleSync", "showPopup", "closeFromInner", "watch", "mounted", "methods", "maskClick", "open", "close", "modeCenterClose", "change", "clearTimeout"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAonB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACwExoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;QACA;MACA;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACA;IACAmB;MACApB;MACAC;IACA;EACA;EACAoB;IACA;IACAC;MACA;MACA;QACAC;MACA;MAEA;IACA;IACA;IACAC;MACA;MACA;MACA;QACAD;UACAlB;UACAC;UACAmB;QACA;MACA;QACAF;UACAlB;UACAC;UACAmB;QACA;MACA;MACAF;MACA;MACA;QACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;QAAA;QAEAA;MACA;MAEA;QACAA;MACA;MAEA;IACA;IACA;IACAG;MACA;MACAH;MACA;MACAA;MACAA;MACA;QACAA;MACA;MACA;QACAA;QACAA;MACA;MACA;QACAA;MACA;MACA;IACA;IACA;IACAI;MACA;MACA;QACAJ;MACA;MACA;QACAA;MACA;MAEA;IACA;IACAK;MACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAnC;MACA;QACA;QACA;UACA;UACA;QACA;QACA;MACA;QACA;MACA;MACA;IACA;EACA;EACAoC;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;MACA;MACA;QAEA;UACA;UACA;UACAC;QACA;MAQA;QACA;UACA;UACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AChWA;AAAA;AAAA;AAAA;AAAmsC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACAvtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-popup/tn-popup.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-popup.vue?vue&type=template&id=01456b9c&scoped=true&\"\nvar renderjs\nimport script from \"./tn-popup.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-popup.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-popup.vue?vue&type=style&index=0&id=01456b9c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"01456b9c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-popup/tn-popup.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-popup.vue?vue&type=template&id=01456b9c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.visibleSync\n    ? _vm.__get_style([\n        _vm.customStyle,\n        _vm.popupStyle,\n        {\n          zIndex: _vm.elZIndex - 1,\n        },\n      ])\n    : null\n  var s1 = _vm.visibleSync ? _vm.__get_style([_vm.contentStyle]) : null\n  var s2 =\n    _vm.visibleSync && _vm.mode === \"center\"\n      ? _vm.__get_style([_vm.centerStyle])\n      : null\n  var s3 =\n    _vm.visibleSync && _vm.mode === \"center\" && _vm.closeBtn\n      ? _vm.__get_style([\n          _vm.closeBtnStyle,\n          {\n            zIndex: _vm.elZIndex,\n          },\n        ])\n      : null\n  var s4 =\n    _vm.visibleSync && _vm.mode !== \"center\" && _vm.closeBtn\n      ? _vm.__get_style([_vm.closeBtnStyle])\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      $event.preventDefault()\n      return (function () {})($event)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n        s4: s4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-popup.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-popup.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view\r\n    v-if=\"visibleSync\"\r\n    class=\"tn-popup-class tn-popup\"\r\n    :style=\"[customStyle, popupStyle, { zIndex: elZIndex - 1}]\"\r\n    hover-stop-propagation\r\n  >\r\n    <!-- mask -->\r\n    <view\r\n      class=\"tn-popup__mask\"\r\n      :class=\"[{'tn-popup__mask--show': showPopup && mask}]\"\r\n      :style=\"{zIndex: elZIndex - 2}\"\r\n      @tap=\"maskClick\"\r\n      @touchmove.stop.prevent = \"() => {}\"\r\n      hover-stop-propagation\r\n    ></view>\r\n    <!-- 弹框内容 -->\r\n    <view\r\n      class=\"tn-popup__content\"\r\n      :class=\"[\r\n        mode !== 'center' ? backgroundColorClass : '',\r\n        safeAreaInsetBottom ? 'tn-safe-area-inset-bottom' : '',\r\n        'tn-popup--' + mode,\r\n        showPopup ? 'tn-popup__content--visible' : '',\r\n        zoom && mode === 'center' ? 'tn-popup__content__center--animation-zoom' : ''\r\n      ]\"\r\n      :style=\"[contentStyle]\"\r\n      @tap=\"modeCenterClose\"\r\n      @touchmove.stop.prevent\r\n      @tap.stop.prevent\r\n    >\r\n      <!-- 居中时候的内容 -->\r\n      <view\r\n        v-if=\"mode === 'center'\"\r\n        class=\"tn-popup__content__center_box\"\r\n        :class=\"[backgroundColorClass]\"\r\n        :style=\"[centerStyle]\"\r\n        @touchmove.stop.prevent\r\n        @tap.stop.prevent\r\n      >\r\n        <!-- 关闭按钮 -->\r\n        <view\r\n          v-if=\"closeBtn\"\r\n          class=\"tn-popup__close\"\r\n          :class=\"[`tn-icon-${closeBtnIcon}`, `tn-popup__close--${closeBtnPosition}`]\"\r\n          :style=\"[closeBtnStyle, {zIndex: elZIndex}]\"\r\n          @tap=\"close\"\r\n        ></view>\r\n        <scroll-view scroll-y class=\"tn-popup__content__scroll-view\">\r\n          <slot></slot>\r\n        </scroll-view>\r\n      </view>\r\n      \r\n      <!-- 除居中外的其他情况 -->\r\n      <scroll-view scroll-y v-else class=\"tn-popup__content__scroll-view\">\r\n        <slot></slot>\r\n      </scroll-view>\r\n      <!-- 关闭按钮 -->\r\n      <view\r\n        v-if=\"mode !== 'center' && closeBtn\"\r\n        class=\"tn-popup__close\"\r\n        :class=\"[`tn-popup__close--${closeBtnPosition}`]\"\r\n        :style=\"{zIndex: elZIndex}\"\r\n        @tap=\"close\"\r\n      >\r\n        <view :class=\"[`tn-icon-${closeBtnIcon}`]\" :style=\"[closeBtnStyle]\"></view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import componentsColorMixin from '../../libs/mixin/components_color.js'\r\n  export default {\r\n    mixins: [componentsColorMixin],\r\n    name: 'tn-popup',\r\n    props: {\r\n      value: {\r\n      \ttype: Boolean,\r\n      \tdefault: false\r\n      },\r\n      // 弹出方向\r\n      // left/right/top/bottom/center\r\n      mode: {\r\n        type: String,\r\n        default: 'left'\r\n      },\r\n      // 是否显示遮罩\r\n      mask: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 抽屉的宽度（mode=left/right）,高度（mode=top/bottom）\r\n      length: {\r\n        type: [Number, String],\r\n        default: 'auto'\r\n      },\r\n      // 宽度，只对左，右，中部弹出时起作用，单位rpx，或者\"auto\"\r\n      // 或者百分比\"50%\"，表示由内容撑开高度或者宽度，优先级高于length参数\r\n      width: {\r\n      \ttype: String,\r\n      \tdefault: ''\r\n      },\r\n      // 高度，只对上，下，中部弹出时起作用，单位rpx，或者\"auto\"\r\n      // 或者百分比\"50%\"，表示由内容撑开高度或者宽度，优先级高于length参数\r\n      height: {\r\n      \ttype: String,\r\n      \tdefault: ''\r\n      },\r\n      // 是否开启动画，只在mode=center有效\r\n      zoom: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 是否开启底部安全区适配，开启的话，会在iPhoneX机型底部添加一定的内边距\r\n      safeAreaInsetBottom: {\r\n      \ttype: Boolean,\r\n      \tdefault: false\r\n      },\r\n      // 是否可以通过点击遮罩进行关闭\r\n      maskCloseable: {\r\n      \ttype: Boolean,\r\n      \tdefault: true\r\n      },\r\n      // 用户自定义样式\r\n      customStyle: {\r\n      \ttype: Object,\r\n      \tdefault() {\r\n      \t\treturn {}\r\n      \t}\r\n      },\r\n      // 显示圆角的大小\r\n      borderRadius: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // zIndex\r\n      zIndex: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // 是否显示关闭按钮\r\n      closeBtn: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 关闭按钮的图标\r\n      closeBtnIcon: {\r\n        type: String,\r\n        default: 'close'\r\n      },\r\n      // 关闭按钮显示的位置\r\n      // top-left/top-right/bottom-left/bottom-right\r\n      closeBtnPosition: {\r\n        type: String,\r\n        default: 'top-right'\r\n      },\r\n      // 关闭按钮图标颜色\r\n      closeIconColor: {\r\n        type: String,\r\n        default: '#AAAAAA'\r\n      },\r\n      // 关闭按钮图标的大小\r\n      closeIconSize: {\r\n        type: Number,\r\n        default: 30\r\n      },\r\n      // 给一个负的margin-top，往上偏移，避免和键盘重合的情况，仅在mode=center时有效\r\n      negativeTop: {\r\n      \ttype: Number,\r\n      \tdefault: 0\r\n      },\r\n      // marginTop，在mode = top,left,right时生效，避免用户使用了自定义导航栏，组件把导航栏遮挡了\r\n      marginTop: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // 此为内部参数，不在文档对外使用，为了解决Picker和keyboard等融合了弹窗的组件\r\n      // 对v-model双向绑定多层调用造成报错不能修改props值的问题\r\n      popup: {\r\n      \ttype: Boolean,\r\n      \tdefault: true\r\n      },\r\n    },\r\n    computed: {\r\n      // 处理使用了自定义导航栏时被遮挡的问题\r\n      popupStyle() {\r\n        let style = {}\r\n        if ((this.mode === 'top' || this.mode === 'left' || this.mode === 'right') && this.marginTop) {\r\n          style.marginTop = this.$tn.string.getLengthUnitValue(this.marginTop, 'px')\r\n        }\r\n        \r\n        return style\r\n      },\r\n      // 根据mode的位置，设定其弹窗的宽度(mode = left|right)，或者高度(mode = top|bottom)\r\n      contentStyle() {\r\n        let style = {}\r\n        // 如果是左边或者上边弹出时，需要给translate设置为负值，用于隐藏\r\n        if (this.mode === 'left' || this.mode === 'right') {\r\n          style = {\r\n            width: this.width ? this.$tn.string.getLengthUnitValue(this.width) : this.$tn.string.getLengthUnitValue(this.length),\r\n            height: '100%',\r\n            transform: `translate3D(${this.mode === 'left' ? '-100%' : '100%'}, 0px, 0px)`\r\n          }\r\n        } else if (this.mode === 'top' || this.mode === 'bottom') {\r\n          style = {\r\n            width: '100%',\r\n            height: this.height ? this.$tn.string.getLengthUnitValue(this.height) : this.$tn.string.getLengthUnitValue(this.length),\r\n            transform: `translate3D(0px, ${this.mode === 'top' ? '-100%': '100%'}, 0px)`\r\n          }\r\n        }\r\n        style.zIndex = this.elZIndex\r\n        // 如果设置了圆角的值，添加弹窗的圆角\r\n        if (this.borderRadius) {\r\n          switch(this.mode) {\r\n            case 'left':\r\n              style.borderRadius = `0 ${this.borderRadius}rpx ${this.borderRadius}rpx 0`\r\n              break\r\n            case 'top':\r\n              style.borderRadius = `0 0 ${this.borderRadius}rpx ${this.borderRadius}rpx`\r\n              break\r\n            case 'right':\r\n              style.borderRadius = `${this.borderRadius}rpx 0 0 ${this.borderRadius}rpx`\r\n              break\r\n            case 'bottom':\r\n              style.borderRadius = `${this.borderRadius}rpx ${this.borderRadius}rpx 0 0`\r\n              break\r\n          }\r\n          style.overflow = 'hidden'\r\n        }\r\n        \r\n        if (this.backgroundColorStyle && this.mode !== 'center') {\r\n          style.backgroundColor = this.backgroundColorStyle\r\n        }\r\n        \r\n        return style\r\n      },\r\n      // 中部弹窗的样式\r\n      centerStyle() {\r\n        let style = {}\r\n        style.width = this.width ? this.$tn.string.getLengthUnitValue(this.width) : this.$tn.string.getLengthUnitValue(this.length)\r\n        // 中部弹出的模式，如果没有设置高度，就用auto值，由内容撑开\r\n        style.height = this.height ? this.$tn.string.getLengthUnitValue(this.height) : 'auto'\r\n        style.zIndex = this.elZIndex\r\n        if (this.negativeTop) {\r\n          style.marginTop = `-${this.$tn.string.getLengthUnitValue(this.negativeTop)}`\r\n        }\r\n        if (this.borderRadius) {\r\n          style.borderRadius = `${this.borderRadius}rpx`\r\n          style.overflow='hidden'\r\n        }\r\n        if (this.backgroundColorStyle) {\r\n          style.backgroundColor = this.backgroundColorStyle\r\n        }\r\n        return style\r\n      },\r\n      // 关闭按钮样式\r\n      closeBtnStyle() {\r\n        let style = {}\r\n        if (this.closeIconColor) {\r\n          style.color = this.closeIconColor\r\n        }\r\n        if (this.closeIconSize) {\r\n          style.fontSize = this.closeIconSize + 'rpx'\r\n        }\r\n        \r\n        return style\r\n      },\r\n      elZIndex() {\r\n        return this.zIndex ? this.zIndex : this.$tn.zIndex.popup\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        timer: null,\r\n        visibleSync: false,\r\n        showPopup: false,\r\n        closeFromInner: false\r\n      }\r\n    },\r\n    watch: {\r\n      value(val) {\r\n        if (val) {\r\n          // console.log(this.visibleSync);\r\n          if (this.visibleSync) {\r\n            this.visibleSync = false\r\n            return\r\n          }\r\n          this.open()\r\n        } else if (!this.closeFromInner) {\r\n          this.close()\r\n        }\r\n        this.closeFromInner = false\r\n      }\r\n    },\r\n    mounted() {\r\n      // 组件渲染完成时，检查value是否为true，如果是，弹出popup\r\n      this.value && this.open()\r\n    },\r\n    methods: {\r\n      // 点击遮罩\r\n      maskClick() {\r\n        if (!this.maskCloseable) return\r\n        this.close()\r\n      },\r\n      open() {\r\n        this.change('visibleSync', 'showPopup', true)\r\n      },\r\n      // 关闭弹框\r\n      close() {\r\n        // 标记关闭是内部发生的，否则修改了value值，导致watch中对value检测，导致再执行一遍close\r\n        // 造成@close事件触发两次\r\n        this.closeFromInner = true\r\n        this.change('showPopup', 'visibleSync', false)\r\n      },\r\n      // 中部弹出时，需要.tn-drawer-content将内容居中，此元素会铺满屏幕，点击需要关闭弹窗\r\n      // 让其只在mode=center时起作用 \r\n      modeCenterClose() {\r\n        if (this.mode != 'center' || !this.maskCloseable) return\r\n        this.close()\r\n      },\r\n      // 关闭时先通过动画隐藏弹窗和遮罩，再移除整个组件\r\n      // 打开时，先渲染组件，延时一定时间再让遮罩和弹窗的动画起作用\r\n      change(param1, param2, status) {\r\n        // 如果this.popup为false，意味着为picker，actionsheet等组件调用了popup组件\r\n        if (this.popup === true) {\r\n          this.$emit('input', status)\r\n        }\r\n        this[param1] = status\r\n        if (status) {\r\n          // #ifdef H5 || MP\r\n          this.timer = setTimeout(() => {\r\n            this[param2] = status\r\n            this.$emit(status ? 'open' : 'close')\r\n            clearTimeout(this.timer)\r\n          }, 10)\r\n          // #endif\r\n          // #ifndef H5 || MP\r\n          this.$nextTick(() => {\r\n            this[param2] = status\r\n            this.$emit(status ? 'open' : 'close')\r\n          })\r\n          // #endif\r\n        } else {\r\n          this.timer = setTimeout(() => {\r\n            this[param2] = status\r\n            this.$emit(status ? 'open' : 'close')\r\n            clearTimeout(this.timer)\r\n          }, 250)\r\n        }\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  \r\n  .tn-popup {\r\n    /* #ifndef APP-NVUE */\r\n    display: block;\r\n    /* #endif */\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    overflow: hidden;\r\n    z-index: 29091 !important;\r\n    \r\n    &__content {\r\n      /* #ifndef APP-NVUE */\r\n      display: block;\r\n      /* #endif */\r\n      position: absolute;\r\n      transition: all 0.25s linear;\r\n      \r\n      &--visible {\r\n        transform: translate3D(0px, 0px, 0px) !important;\r\n        &.tn-popup--center {\r\n          transform: scale(1);\r\n          opacity: 1;\r\n        }\r\n      }\r\n      \r\n      &__center_box {\r\n        min-width: 100rpx;\r\n        min-height: 100rpx;\r\n        /* #ifndef APP-NVUE */\r\n        display: block;\r\n        /* #endif */\r\n        position: relative;\r\n        background-color: #FFFFFF;\r\n      }\r\n      \r\n      &__scroll-view {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n      \r\n      &__center--animation-zoom {\r\n        transform: scale(1.15);\r\n      }\r\n    }\r\n    \r\n    &__scroll_view {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    \r\n    &--left {\r\n      top: 0;\r\n      bottom: 0;\r\n      left: 0;\r\n      background-color: #FFFFFF;\r\n    }\r\n    \r\n    &--right {\r\n      top: 0;\r\n      bottom: 0;\r\n      right: 0;\r\n      background-color: #FFFFFF;\r\n    }\r\n    \r\n    &--top {\r\n      left: 0;\r\n      right: 0;\r\n      top: 0;\r\n      background-color: #FFFFFF;\r\n    }\r\n    \r\n    &--bottom {\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background-color: #FFFFFF;\r\n    }\r\n    \r\n    &--center {\r\n      display: flex;\r\n      flex-direction: column;\r\n      bottom: 0;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      justify-content: center;\r\n      align-items: center;\r\n      opacity: 0;\r\n    }\r\n    \r\n    &__close {\r\n      position: absolute;\r\n      \r\n      &--top-left {\r\n        top: 30rpx;\r\n        left: 30rpx;\r\n      }\r\n      \r\n      &--top-right {\r\n        top: 30rpx;\r\n        right: 30rpx;\r\n      }\r\n      \r\n      &--bottom-left {\r\n        bottom: 30rpx;\r\n        left: 30rpx;\r\n      }\r\n      \r\n      &--bottom-right {\r\n        bottom: 30rpx;\r\n        right: 30rpx;\r\n      }\r\n    }\r\n    \r\n    &__mask {\r\n      width: 100%;\r\n      height: 100%;\r\n      position: fixed;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      border: 0;\r\n      background-color: $tn-mask-bg-color;\r\n      transition: 0.25s linear;\r\n      transition-property: opacity;\r\n      opacity: 0;\r\n      \r\n      &--show {\r\n        opacity: 1;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-popup.vue?vue&type=style&index=0&id=01456b9c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-popup.vue?vue&type=style&index=0&id=01456b9c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980404844\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}