{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/project/shuati_new/pages/mine/share.vue?ba5f", "webpack:///D:/project/shuati_new/pages/mine/share.vue?4690", "webpack:///D:/project/shuati_new/pages/mine/share.vue?5933", "webpack:///D:/project/shuati_new/pages/mine/share.vue?3643", "uni-app:///pages/mine/share.vue", "webpack:///D:/project/shuati_new/pages/mine/share.vue?210d", "webpack:///D:/project/shuati_new/pages/mine/share.vue?1e1c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "data", "baseUrl", "code", "isSub", "service_img", "showKefu", "img1", "img2", "onLoad", "withShareTicket", "menus", "methods", "to<PERSON>eidian", "shortLink", "fail", "uni", "title", "icon", "uploadImg", "count", "sizeType", "sourceType", "success", "profileImg", "myUpload", "url", "header", "filePath", "name", "duration", "submit", "that", "img", "getUserInfo", "prevPage", "delta", "getsysconfig"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvCA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+EtnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEAd;MAAA;MACAe;MACAC;IACA;IAEA;EACA;EAEAC;IACAC;MACA;MACAlB;QACAmB;QACAC;UACAC;YACAC;YACAC;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACAH;QACAI;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;UACAC;UACA;QACA;MACA;IACA;IACAC;MAAA;MACAT;QACAU;QACAC;UACA;QACA;QACAC;QACAC;QACAN;UACA;UACA;YACA;cACA;YACA;YACA;cACA;YACA;YACA;UACA;YACAP;cACAC;cACAa;cACAZ;YACA;UACA;QACA;MACA;IACA;IACAa;MACA;MACA;MACA;QACA9B;MACA;MACA;QACAA;MACA;MACA;QACAe;UACAC;UACAC;QACA;QACA;MACA;MACAc;QACAC;MACA;QACA;UACAjB;YACAC;YACAC;YACAK;cACAS;YACA;UACA;QACA;MACA;IACA;IACAE;MACA;QACA;UACAlB;UACA;UACA;UACA;YACAmB;YACAnB;cACAoB;YACA;UACA;YACApB;UACA;QACA;MACA;IACA;IACAqB;MAAA;MAAA;MACA;QACA;UACA;UACA;UACArB;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrNA;AAAA;AAAA;AAAA;AAAqqC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACAzrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/share.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/share.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./share.vue?vue&type=template&id=1dac5edc&scoped=true&\"\nvar renderjs\nimport script from \"./share.vue?vue&type=script&lang=js&\"\nexport * from \"./share.vue?vue&type=script&lang=js&\"\nimport style0 from \"./share.vue?vue&type=style&index=0&id=1dac5edc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1dac5edc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/share.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./share.vue?vue&type=template&id=1dac5edc&scoped=true&\"", "var components\ntry {\n  components = {\n    tnPopup: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-popup/tn-popup\" */ \"@/tuniao-ui/components/tn-popup/tn-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.showKefu = true\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./share.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./share.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"header\" @click.stop=\"toWeidian()\">\r\n\t\t\t<image class=\"headerimg\" src=\"@/static/shoretop.png\" mode=\"\"></image>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"tn-width-full\" style=\"padding:20rpx;box-sizing: border-box;\">\r\n\t\t\t<view class=\"tn-width-full submit_body\">\r\n\t\t\t\t<view class=\"brush\">\r\n\t\t\t\t\t<view class=\"brush-title\">刷题会员-3分钟免费解锁</view>\r\n\t\t\t\t\t<view class=\"brush-btn\">强化题库</view>\r\n\t\t\t\t\t<view class=\"brush-title\">分享考研群（微信）</view>\r\n\t\t\t\t\t<view class=\"brush-msg\">\r\n\t\t\t\t\t\t<button open-type=\"share\"\r\n\t\t\t\t\t\t\tstyle=\"background-color: transparent;display: inline-block;padding: 0rpx; margin: 0rpx; height: 70rpx;\">\r\n\t\t\t\t\t\t\t<image class=\"brush-msgimg\" src=\"@/static/shorebtn.png\" mode=\"\"></image>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<text>小程序到\r\n\t\t\t\t\t\t\t<text style=\"color:#FF0000;\">考研群</text>（群人数\r\n\t\t\t\t\t\t\t<text style=\"color:#FF0000;\">50人以上</text>）分享成功\r\n\t\t\t\t\t\t\t<text style=\"color:#FF0000;\">3分钟后</text> 截图；\r\n\t\t\t\t\t\t\t<text style=\"color:#FF0000;\">1个群</text>解锁\r\n\t\t\t\t\t\t\t<text style=\"color:#FF0000;\">1周</text>,\r\n\t\t\t\t\t\t\t<text style=\"color:#FF0000;\">2个群</text>解锁\r\n\t\t\t\t\t\t\t<text style=\"color:#FF0000;\">全程</text>。\r\n\t\t\t\t\t\t</text>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"brush-ts\">请勿重复使用历史图片，会被拉小黑屋哟~</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-text-bold tn-text-lx\">\r\n\t\t\t\t\t上传截图-解锁强化题库\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-flex\" style=\"margin-top: 30rpx;\">\r\n\t\t\t\t\t<view class=\"upload\" v-if=\"img1\" @click.stop=\"uploadImg(1)\">\r\n\t\t\t\t\t\t<image :src=\"baseUrl+img1\" mode=\"aspectFill\" style=\"width: 100%;height: 100%;\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @click.stop=\"uploadImg(1)\"\r\n\t\t\t\t\t\tclass=\"upload tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-center\" v-else>\r\n\t\t\t\t\t\t<text class=\"tn-icon-add tn-text-bold\" style=\"font-size: 44rpx;color: #999999;\"></text>\r\n\t\t\t\t\t\t<text style=\"font-size: 22rpx;color: #999999;\">截图1</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"width: 50rpx;\">\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"upload\" v-if=\"img2\" @click.stop=\"uploadImg(2)\">\r\n\t\t\t\t\t\t<image :src=\"baseUrl+img2\" mode=\"aspectFill\" style=\"width: 100%;height: 100%;\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @click.stop=\"uploadImg(2)\"\r\n\t\t\t\t\t\tclass=\"upload tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-center\" v-else>\r\n\t\t\t\t\t\t<text class=\"tn-icon-add tn-text-bold\" style=\"font-size: 44rpx;color: #999999;\"></text>\r\n\t\t\t\t\t\t<text style=\"font-size: 22rpx;color: #999999;\">截图2</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view :class=\"isSub?'submit':'nosubmit'\" @click.stop=\"submit()\">\r\n\t\t\t\t\t立即上传\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<image src=\"../../static/icon/zixun.png\" mode=\"widthFix\" class=\"fab_btn\" @click.stop=\"showKefu=true\">\r\n\t\t</image>\r\n\t\t<tn-popup v-model=\"showKefu\" mode=\"bottom\" :borderRadius=\"40\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t客服帮助</view>\r\n\t\t\t<view class=\"tn-width-full\" style=\"\">\r\n\t\t\t\t<view class=\"tn-flex tn-flex-row-center\"\r\n\t\t\t\t\tstyle=\"padding: 30rpx 46rpx 66rpx 46rpx;box-sizing: border-box;\">\r\n\t\t\t\t\t<view style=\"width: 400rpx;height: 400rpx;\">\r\n\t\t\t\t\t\t<image show-menu-by-longpress style=\"width: 400rpx;height: 400rpx;\" :src=\"baseUrl+service_img\"\r\n\t\t\t\t\t\t\tmode=\"\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport shareMixin from \"@/mixins/shareMixin.js\"\r\n\texport default {\r\n\t\tmixins: [shareMixin],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbaseUrl: this.$config.baseUrl,\r\n\t\t\t\tcode: \"\",\r\n\t\t\t\tisSub: true,\r\n\t\t\t\tservice_img: \"\",\r\n\t\t\t\tshowKefu: false,\r\n\t\t\t\timg1: \"\",\r\n\t\t\t\timg2: \"\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\tthis.getsysconfig()\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\ttoWeidian() {\r\n\t\t\t\tlet url = uni.getStorageSync('systemData').config.share_jump_link\r\n\t\t\t\twx.navigateToMiniProgram({\r\n\t\t\t\t\tshortLink: url,\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: err,\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tuploadImg(type) {\r\n\t\t\t\tlet profileImg = ''\r\n\t\t\t\tuni.chooseImage({\r\n\t\t\t\t\tcount: 1, //默认9\r\n\t\t\t\t\tsizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有\r\n\t\t\t\t\tsourceType: ['album'], //从相册选择\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tprofileImg = res.tempFilePaths[0];\r\n\t\t\t\t\t\tthis.myUpload(profileImg, type)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tmyUpload(rsp, type) {\r\n\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\turl: this.$config.baseUrl + \"/api/upload/upload\",\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\t'Authorization': uni.getStorageSync('TOKEN'),\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfilePath: rsp,\r\n\t\t\t\t\tname: 'file',\r\n\t\t\t\t\tsuccess: (ress) => {\r\n\t\t\t\t\t\tlet mdata = JSON.parse(ress.data);\r\n\t\t\t\t\t\tif (mdata.code == 200) {\r\n\t\t\t\t\t\t\tif (type == 1) {\r\n\t\t\t\t\t\t\t\tthis.img1 = mdata.data.src\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (type == 2) {\r\n\t\t\t\t\t\t\t\tthis.img2 = mdata.data.src\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tthis.$forceUpdate()\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: mdata.msg,\r\n\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsubmit() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet data = []\r\n\t\t\t\tif (this.img1) {\r\n\t\t\t\t\tdata.push(this.img1)\r\n\t\t\t\t}\r\n\t\t\t\tif (this.img2) {\r\n\t\t\t\t\tdata.push(this.img2)\r\n\t\t\t\t}\r\n\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"请上传图片\",\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\tthat.$http.post(that.$api.shareActive, {\r\n\t\t\t\t\timg: data\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"解锁成功\",\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tthat.getUserInfo()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetUserInfo() {\r\n\t\t\t\tthis.$http.post(this.$api.getUserInfo, {}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tuni.setStorageSync('userinfo', res.data)\r\n\t\t\t\t\t\tlet pages = getCurrentPages();\r\n\t\t\t\t\t\tlet prevPage = pages[pages.length - 2];\r\n\t\t\t\t\t\tif (prevPage && prevPage.route == \"pages/exercise/index\") {\r\n\t\t\t\t\t\t\tprevPage.$vm.refreshData();\r\n\t\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetsysconfig() { // 获取系统配置\r\n\t\t\t\tthis.$http.post(this.$api.systemData, {}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tlet datas = res.data;\r\n\t\t\t\t\t\tthis.service_img = datas.config.service_img\r\n\t\t\t\t\t\tuni.setStorageSync('systemData', datas)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.upload {\r\n\t\twidth: 160rpx;\r\n\t\theight: 160rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #F7F7F7;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder: 2rpx dashed #999999;\r\n\r\n\t}\r\n\r\n\t.submit_body {\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tborder-radius: 30rpx;\r\n\t}\r\n\r\n\tpage {\r\n\t\tbackground-color: #f7f7f7 !important;\r\n\t}\r\n\r\n\t.header {\r\n\t\twidth: 750rpx;\r\n\t\tpadding: 28rpx 0;\r\n\t\tborder-radius: 0rpx 0rpx 20rpx 20rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t}\r\n\r\n\t.headerimg {\r\n\t\twidth: 670rpx;\r\n\t\theight: 90rpx;\r\n\t\tdisplay: block;\r\n\t\tmargin: auto;\r\n\t}\r\n\r\n\t.brush-title {\r\n\t\tline-height: 44rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #333333;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\r\n\t.brush-btn {\r\n\t\twidth: 200rpx;\r\n\t\theight: 72rpx;\r\n\t\tline-height: 72rpx;\r\n\t\tmargin: 24rpx 0 50rpx;\r\n\t\tborder-radius: 100rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tbackground: linear-gradient(to bottom, #5552FF, #8F8DFF);\r\n\t}\r\n\r\n\t.brush-msg {\r\n\t\tmargin-top: 20rpx;\r\n\t\tcolor: #333333;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 50rpx;\r\n\t}\r\n\r\n\t.brush-msgimg {\r\n\t\twidth: 170rpx;\r\n\t\theight: 70rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.brush-ts {\r\n\t\tmargin: 32rpx auto;\r\n\t\twidth: 630rpx;\r\n\t\theight: 90rpx;\r\n\t\tline-height: 90rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t\tbackground-color: #FFF7EE;\r\n\t\tcolor: #FFAE5B;\r\n\t\tfont-size: 28rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t// \r\n\t.fab_btn {\r\n\t\twidth: 154rpx;\r\n\t\tposition: fixed;\r\n\t\tright: 0rpx;\r\n\t\tbottom: 150rpx;\r\n\t}\r\n\r\n\t.submit {\r\n\t\twidth: 630rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbackground-color: #3775F6;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #Ffffff;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.nosubmit {\r\n\t\twidth: 630rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbackground-color: #9f9f9f;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #Ffffff;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.input_warp {\r\n\t\twidth: 630rpx;\r\n\t\theight: 114rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #F4F4F4;\r\n\t\tmargin-top: 24rpx;\r\n\t\tpadding: 0rpx 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\tpage {\r\n\t\tbackground-color: #Ffffff;\r\n\t}\r\n\r\n\t.content {\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./share.vue?vue&type=style&index=0&id=1dac5edc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./share.vue?vue&type=style&index=0&id=1dac5edc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980403098\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}