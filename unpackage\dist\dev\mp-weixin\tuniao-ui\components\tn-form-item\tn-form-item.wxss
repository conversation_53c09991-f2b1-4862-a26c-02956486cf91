@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.tn-form-item.data-v-3a5efd9c {
  display: flex;
  flex-direction: column;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #080808;
  box-sizing: border-box;
  line-height: 70rpx;
}
.tn-form-item__border-bottom--error.data-v-3a5efd9c:after {
  border-color: #E83A30;
}
.tn-form-item__body.data-v-3a5efd9c {
  display: flex;
  flex-direction: row;
}
.tn-form-item--left.data-v-3a5efd9c {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.tn-form-item--left__content.data-v-3a5efd9c {
  display: flex;
  flex-direction: row;
  position: relative;
  align-items: center;
  padding-right: 18rpx;
  flex: 1;
}
.tn-form-item--left__content--required.data-v-3a5efd9c {
  position: relative;
  right: 0;
  vertical-align: middle;
  color: #E83A30;
  font-size: 40rpx;
  margin-right: 5rpx;
}
.tn-form-item--left__content__icon.data-v-3a5efd9c {
  color: #AAAAAA;
  margin-right: 8rpx;
}
.tn-form-item--right.data-v-3a5efd9c {
  flex: 1;
}
.tn-form-item--right__content.data-v-3a5efd9c {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}
.tn-form-item--right__content__slot.data-v-3a5efd9c {
  flex: 1;
}
.tn-form-item--right__content__icon.data-v-3a5efd9c {
  margin-left: 10rpx;
  color: #AAAAAA;
  font-size: 30rpx;
}
.tn-form-item__message.data-v-3a5efd9c {
  font-size: 24rpx;
  line-height: 24rpx;
  color: #E83A30;
  margin-top: 12rpx;
}

