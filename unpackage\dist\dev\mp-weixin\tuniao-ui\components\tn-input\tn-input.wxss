@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.tn-input.data-v-628dad5c {
  display: flex;
  flex-direction: row;
  position: relative;
  flex: 1;
}
.tn-input__input.data-v-628dad5c {
  font-size: 28rpx;
  color: #080808;
  flex: 1;
}
.tn-input__text.data-v-628dad5c {
  font-size: 28rpx;
  color: #080808;
  flex: 1;
  min-width: 296rpx;
  max-width: 100%;
  text-overflow: clip;
}
.tn-input__textarea.data-v-628dad5c {
  width: auto;
  font-size: 28rpx;
  color: #080808;
  padding: 10rpx 0;
  line-height: normal;
  flex: 1;
}
.tn-input--border.data-v-628dad5c {
  border-radius: 6rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.1);
}
.tn-input--error.data-v-628dad5c {
  border-color: #E83A30 !important;
}
.tn-input__right-icon.data-v-628dad5c {
  line-height: 1;
}
.tn-input__right-icon .icon.data-v-628dad5c {
  color: #AAAAAA;
}
.tn-input__right-icon__item.data-v-628dad5c {
  margin-left: 10rpx;
}
.tn-input__right-icon__clear .icon.data-v-628dad5c {
  font-size: 32rpx;
}
.tn-input__right-icon__select.data-v-628dad5c {
  transition: -webkit-transform .4s;
  transition: transform .4s;
  transition: transform .4s, -webkit-transform .4s;
}
.tn-input__right-icon__select .icon.data-v-628dad5c {
  font-size: 26rpx;
}
.tn-input__right-icon__select--reverse.data-v-628dad5c {
  -webkit-transform: rotate(-180deg);
          transform: rotate(-180deg);
}
.tn-input__left-icon.data-v-628dad5c {
  line-height: 1;
}
.tn-input__left-icon__item.data-v-628dad5c {
  margin-left: 0rpx;
  margin-top: 4rpx;
}
.tn-input__left-icon__clear .icon.data-v-628dad5c {
  font-size: 32rpx;
  color: #AAAAAA;
}
.tn-input__left-icon__select.data-v-628dad5c {
  transition: -webkit-transform .4s;
  transition: transform .4s;
  transition: transform .4s, -webkit-transform .4s;
}
.tn-input__left-icon__select .icon.data-v-628dad5c {
  font-size: 26rpx;
}
.tn-input__left-icon__select--reverse.data-v-628dad5c {
  -webkit-transform: rotate(-180deg);
          transform: rotate(-180deg);
}

