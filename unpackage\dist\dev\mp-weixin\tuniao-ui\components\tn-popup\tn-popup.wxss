@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.tn-popup.data-v-01456b9c {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 29091 !important;
}
.tn-popup__content.data-v-01456b9c {
  display: block;
  position: absolute;
  transition: all 0.25s linear;
}
.tn-popup__content--visible.data-v-01456b9c {
  -webkit-transform: translate3D(0px, 0px, 0px) !important;
          transform: translate3D(0px, 0px, 0px) !important;
}
.tn-popup__content--visible.tn-popup--center.data-v-01456b9c {
  -webkit-transform: scale(1);
          transform: scale(1);
  opacity: 1;
}
.tn-popup__content__center_box.data-v-01456b9c {
  min-width: 100rpx;
  min-height: 100rpx;
  display: block;
  position: relative;
  background-color: #FFFFFF;
}
.tn-popup__content__scroll-view.data-v-01456b9c {
  width: 100%;
  height: 100%;
}
.tn-popup__content__center--animation-zoom.data-v-01456b9c {
  -webkit-transform: scale(1.15);
          transform: scale(1.15);
}
.tn-popup__scroll_view.data-v-01456b9c {
  width: 100%;
  height: 100%;
}
.tn-popup--left.data-v-01456b9c {
  top: 0;
  bottom: 0;
  left: 0;
  background-color: #FFFFFF;
}
.tn-popup--right.data-v-01456b9c {
  top: 0;
  bottom: 0;
  right: 0;
  background-color: #FFFFFF;
}
.tn-popup--top.data-v-01456b9c {
  left: 0;
  right: 0;
  top: 0;
  background-color: #FFFFFF;
}
.tn-popup--bottom.data-v-01456b9c {
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
}
.tn-popup--center.data-v-01456b9c {
  display: flex;
  flex-direction: column;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
  justify-content: center;
  align-items: center;
  opacity: 0;
}
.tn-popup__close.data-v-01456b9c {
  position: absolute;
}
.tn-popup__close--top-left.data-v-01456b9c {
  top: 30rpx;
  left: 30rpx;
}
.tn-popup__close--top-right.data-v-01456b9c {
  top: 30rpx;
  right: 30rpx;
}
.tn-popup__close--bottom-left.data-v-01456b9c {
  bottom: 30rpx;
  left: 30rpx;
}
.tn-popup__close--bottom-right.data-v-01456b9c {
  bottom: 30rpx;
  right: 30rpx;
}
.tn-popup__mask.data-v-01456b9c {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  border: 0;
  background-color: rgba(0, 0, 0, 0.4);
  transition: 0.25s linear;
  transition-property: opacity;
  opacity: 0;
}
.tn-popup__mask--show.data-v-01456b9c {
  opacity: 1;
}

