{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-badge/tn-badge.vue?99a8", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-badge/tn-badge.vue?49aa", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-badge/tn-badge.vue?75d8", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-badge/tn-badge.vue?0545", "uni-app:///tuniao-ui/components/tn-badge/tn-badge.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-badge/tn-badge.vue?7d5f", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-badge/tn-badge.vue?0c76"], "names": ["mixins", "name", "props", "index", "type", "default", "radius", "padding", "margin", "dot", "absolute", "top", "right", "translateCenter", "computed", "badgeClass", "clazz", "badgeStyle", "style", "data", "methods", "handleClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAonB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgBxoB;;;;;;;;;;;;;;;;eACA;EACAA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;EACA;EACAS;IACAC;MACA;MACA;QACAC;MACA;MACA;QACAA;QAEA;UACAA;QACA;MACA;MAEA;IACA;IACAC;MACA;MAEA;QACAC;QACAA;QACAA;;QAEA;MACA;;MAEA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MAEA;QACAA;MACA;MAEA;QACAA;MACA;MACA;QACAA;MACA;MAEA;IACA;EAEA;EACAC;IACA,QAEA;EACA;EACAC;IACA;IACAC;MACA;QACAlB;MACA;MACA;QACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC1IA;AAAA;AAAA;AAAA;AAAmsC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACAvtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-badge/tn-badge.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-badge.vue?vue&type=template&id=494ae17c&scoped=true&\"\nvar renderjs\nimport script from \"./tn-badge.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-badge.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-badge.vue?vue&type=style&index=0&id=494ae17c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"494ae17c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-badge/tn-badge.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-badge.vue?vue&type=template&id=494ae17c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.badgeStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-badge.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-badge.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view\r\n    class=\"tn-badge-class tn-badge\"\r\n    :class=\"[\r\n      backgroundColorClass,\r\n      fontColorClass,\r\n      badgeClass\r\n    ]\"\r\n    :style=\"[badgeStyle]\"\r\n    @click=\"handleClick\"\r\n  >\r\n    <slot v-if=\"!dot\"></slot>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import componentsColorMixin from '../../libs/mixin/components_color.js'\r\n  export default {\r\n    mixins: [componentsColorMixin],\r\n    name: 'tn-badge',\r\n    props: {\r\n      // 序号\r\n      index: {\r\n        type: [Number, String],\r\n        default: '0'\r\n      },\r\n      // 徽章的大小 rpx\r\n      radius: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // 内边距\r\n      padding: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // 外边距\r\n      margin: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // 是否为一个点\r\n      dot: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 是否使用绝对定位\r\n      absolute: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // top\r\n      top: {\r\n        type: [String, Number],\r\n        default: ''\r\n      },\r\n      // right\r\n      right: {\r\n        type: [String, Number],\r\n        default: ''\r\n      },\r\n      // 居中 对齐右上角\r\n      translateCenter: {\r\n        type: Boolean,\r\n        default: true\r\n      }\r\n    },\r\n    computed: {\r\n      badgeClass() {\r\n        let clazz = ''\r\n        if (this.dot) {\r\n          clazz += ' tn-badge--dot'\r\n        }\r\n        if (this.absolute) {\r\n          clazz += ' tn-badge--absolute'\r\n          \r\n          if (this.translateCenter) {\r\n            clazz += ' tn-badge--center-position'\r\n          }\r\n        }\r\n        \r\n        return clazz\r\n      },\r\n      badgeStyle() {\r\n        let style = {}\r\n        \r\n        if (this.radius !== 0) {\r\n          style.width = this.radius + 'rpx'\r\n          style.height = this.radius + 'rpx'\r\n          style.lineHeight = this.radius + 'rpx'\r\n          \r\n          // style.borderRadius = (this.radius * 8) + 'rpx'\r\n        }\r\n        \r\n        if (this.padding) {\r\n          style.padding = this.padding\r\n        }\r\n        if (this.margin) {\r\n          style.margin = this.margin\r\n        }\r\n        if (this.fontColorStyle) {\r\n          style.color = this.fontColorStyle\r\n        }\r\n        if (this.fontSize) {\r\n          style.fontSize = this.fontSize + this.fontUnit\r\n        }\r\n        \r\n        if (this.backgroundColorStyle) {\r\n          style.backgroundColor = this.backgroundColorStyle\r\n        }\r\n        \r\n        if (this.top) {\r\n          style.top = this.$tn.string.getLengthUnitValue(this.top)\r\n        }\r\n        if (this.right) {\r\n          style.right = this.$tn.string.getLengthUnitValue(this.right)\r\n        }\r\n        \r\n        return style\r\n      },\r\n      \r\n    },\r\n    data() {\r\n      return {\r\n        \r\n      }\r\n    },\r\n    methods: {\r\n      // 处理点击事件\r\n      handleClick() {\r\n        this.$emit('click', {\r\n          index: Number(this.index)\r\n        })\r\n        this.$emit('tap', {\r\n          index: Number(this.index)\r\n        })\r\n      },\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .tn-badge {\r\n    width: auto;\r\n    height: auto;\r\n    box-sizing: border-box;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 10;\r\n    font-size: 20rpx;\r\n    background-color: #FFFFFF;\r\n    // color: #FFFFFF;\r\n    border-radius: 100rpx;\r\n    padding: 4rpx 8rpx;\r\n    line-height: initial;\r\n    \r\n    &--dot {\r\n      width: 8rpx;\r\n      height: 8rpx;\r\n      border-radius: 50%;\r\n      padding: 0;\r\n    }\r\n    &--absolute {\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n    }\r\n    &--center-position {\r\n      transform: translate(50%, -50%);\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-badge.vue?vue&type=style&index=0&id=494ae17c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-badge.vue?vue&type=style&index=0&id=494ae17c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980406041\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}