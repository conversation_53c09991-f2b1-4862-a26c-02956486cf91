<template>
	<view class="content">
		<view class="header" @click.stop="toWeidian()">
			<image class="headerimg" src="@/static/shoretop.png" mode=""></image>
		</view>

		<view class="tn-width-full" style="padding:20rpx;box-sizing: border-box;">
			<view class="tn-width-full submit_body">
				<view class="brush">
					<view class="brush-title">刷题会员-3分钟免费解锁</view>
					<view class="brush-btn">强化题库</view>
					<view class="brush-title">分享考研群（微信）</view>
					<view class="brush-msg">
						<button open-type="share"
							style="background-color: transparent;display: inline-block;padding: 0rpx; margin: 0rpx; height: 70rpx;">
							<image class="brush-msgimg" src="@/static/shorebtn.png" mode=""></image>
						</button>
						<text>小程序到
							<text style="color:#FF0000;">考研群</text>（群人数
							<text style="color:#FF0000;">50人以上</text>）分享成功
							<text style="color:#FF0000;">3分钟后</text> 截图；
							<text style="color:#FF0000;">1个群</text>解锁
							<text style="color:#FF0000;">1周</text>,
							<text style="color:#FF0000;">2个群</text>解锁
							<text style="color:#FF0000;">全程</text>。
						</text>

					</view>
					<view class="brush-ts">请勿重复使用历史图片，会被拉小黑屋哟~</view>
				</view>
				<view class="tn-text-bold tn-text-lx">
					上传截图-解锁强化题库
				</view>
				<view class="tn-flex" style="margin-top: 30rpx;">
					<view class="upload" v-if="img1" @click.stop="uploadImg(1)">
						<image :src="baseUrl+img1" mode="aspectFill" style="width: 100%;height: 100%;"></image>
					</view>
					<view @click.stop="uploadImg(1)"
						class="upload tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-center" v-else>
						<text class="tn-icon-add tn-text-bold" style="font-size: 44rpx;color: #999999;"></text>
						<text style="font-size: 22rpx;color: #999999;">截图1</text>
					</view>
					<view style="width: 50rpx;">

					</view>
					<view class="upload" v-if="img2" @click.stop="uploadImg(2)">
						<image :src="baseUrl+img2" mode="aspectFill" style="width: 100%;height: 100%;"></image>
					</view>
					<view @click.stop="uploadImg(2)"
						class="upload tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-center" v-else>
						<text class="tn-icon-add tn-text-bold" style="font-size: 44rpx;color: #999999;"></text>
						<text style="font-size: 22rpx;color: #999999;">截图2</text>
					</view>
				</view>
				<view :class="isSub?'submit':'nosubmit'" @click.stop="submit()">
					立即上传
				</view>
			</view>
		</view>
		<image src="../../static/icon/zixun.png" mode="widthFix" class="fab_btn" @click.stop="showKefu=true">
		</image>
		<tn-popup v-model="showKefu" mode="bottom" :borderRadius="40">
			<view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				客服帮助</view>
			<view class="tn-width-full" style="">
				<view class="tn-flex tn-flex-row-center"
					style="padding: 30rpx 46rpx 66rpx 46rpx;box-sizing: border-box;">
					<view style="width: 400rpx;height: 400rpx;">
						<image show-menu-by-longpress style="width: 400rpx;height: 400rpx;" :src="baseUrl+service_img"
							mode=""></image>
					</view>
				</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	import shareMixin from "@/mixins/shareMixin.js"
	export default {
		mixins: [shareMixin],
		data() {
			return {
				baseUrl: this.$config.baseUrl,
				code: "",
				isSub: true,
				service_img: "",
				showKefu: false,
				img1: "",
				img2: ""
			}
		},
		onLoad() {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			this.getsysconfig()
		},

		methods: {
			toWeidian() {
				let url = uni.getStorageSync('systemData').config.share_jump_link
				wx.navigateToMiniProgram({
					shortLink: url,
					fail: (err) => {
						uni.showToast({
							title: err,
							icon: "none"
						})
					}
				})
			},
			uploadImg(type) {
				let profileImg = ''
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album'], //从相册选择
					success: (res) => {
						profileImg = res.tempFilePaths[0];
						this.myUpload(profileImg, type)
					}
				});
			},
			myUpload(rsp, type) {
				uni.uploadFile({
					url: this.$config.baseUrl + "/api/upload/upload",
					header: {
						'Authorization': uni.getStorageSync('TOKEN'),
					},
					filePath: rsp,
					name: 'file',
					success: (ress) => {
						let mdata = JSON.parse(ress.data);
						if (mdata.code == 200) {
							if (type == 1) {
								this.img1 = mdata.data.src
							}
							if (type == 2) {
								this.img2 = mdata.data.src
							}
							this.$forceUpdate()
						} else {
							uni.showToast({
								title: mdata.msg,
								duration: 2000,
								icon: 'none'
							});
						}
					}
				});
			},
			submit() {
				let that = this
				let data = []
				if (this.img1) {
					data.push(this.img1)
				}
				if (this.img2) {
					data.push(this.img2)
				}
				if (data.length == 0) {
					uni.showToast({
						title: "请上传图片",
						icon: "none"
					})
					return false
				}
				that.$http.post(that.$api.shareActive, {
					img: data
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: "解锁成功",
							icon: "none",
							success: () => {
								that.getUserInfo()
							}
						})
					}
				})
			},
			getUserInfo() {
				this.$http.post(this.$api.getUserInfo, {}).then(res => {
					if (res.code == 200) {
						uni.setStorageSync('userinfo', res.data)
						let pages = getCurrentPages();
						let prevPage = pages[pages.length - 2];
						if (prevPage && prevPage.route == "pages/exercise/index") {
							prevPage.$vm.refreshData();
							uni.navigateBack({
								delta: 1
							})
						}else{
							uni.navigateBack()
						}
					}
				})
			},
			getsysconfig() { // 获取系统配置
				this.$http.post(this.$api.systemData, {}).then(res => {
					if (res.code == 200) {
						let datas = res.data;
						this.service_img = datas.config.service_img
						uni.setStorageSync('systemData', datas)
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.upload {
		width: 160rpx;
		height: 160rpx;
		border-radius: 20rpx;
		background-color: #F7F7F7;
		box-sizing: border-box;
		border: 2rpx dashed #999999;

	}

	.submit_body {
		padding: 30rpx;
		box-sizing: border-box;
		background-color: #FFFFFF;
		border-radius: 30rpx;
	}

	page {
		background-color: #f7f7f7 !important;
	}

	.header {
		width: 750rpx;
		padding: 28rpx 0;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		background-color: #FFFFFF;
	}

	.headerimg {
		width: 670rpx;
		height: 90rpx;
		display: block;
		margin: auto;
	}

	.brush-title {
		line-height: 44rpx;
		font-size: 32rpx;
		color: #333333;
		font-weight: 600;
	}

	.brush-btn {
		width: 200rpx;
		height: 72rpx;
		line-height: 72rpx;
		margin: 24rpx 0 50rpx;
		border-radius: 100rpx;
		color: #fff;
		text-align: center;
		background: linear-gradient(to bottom, #5552FF, #8F8DFF);
	}

	.brush-msg {
		margin-top: 20rpx;
		color: #333333;
		font-size: 28rpx;
		line-height: 50rpx;
	}

	.brush-msgimg {
		width: 170rpx;
		height: 70rpx;
		margin-right: 10rpx;
	}

	.brush-ts {
		margin: 32rpx auto;
		width: 630rpx;
		height: 90rpx;
		line-height: 90rpx;
		padding: 0 20rpx;
		background-color: #FFF7EE;
		color: #FFAE5B;
		font-size: 28rpx;
		border-radius: 10rpx;
	}

	// 
	.fab_btn {
		width: 154rpx;
		position: fixed;
		right: 0rpx;
		bottom: 150rpx;
	}

	.submit {
		width: 630rpx;
		height: 100rpx;
		border-radius: 50rpx;
		background-color: #3775F6;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #Ffffff;
		margin-top: 30rpx;
	}

	.nosubmit {
		width: 630rpx;
		height: 100rpx;
		border-radius: 50rpx;
		background-color: #9f9f9f;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #Ffffff;
		margin-top: 30rpx;
	}

	.input_warp {
		width: 630rpx;
		height: 114rpx;
		border-radius: 20rpx;
		background-color: #F4F4F4;
		margin-top: 24rpx;
		padding: 0rpx 40rpx;
		box-sizing: border-box;
	}

	page {
		background-color: #Ffffff;
	}

	.content {
		width: 100%;
		box-sizing: border-box;
	}
</style>