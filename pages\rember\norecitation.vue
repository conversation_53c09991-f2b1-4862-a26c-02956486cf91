<template>
	<view class="content">
		<view class="tn-flex tn-flex-col-center tn-flex-row-between">
			<view style="width: 600rpx;">
				<tn-line-progress :percent="per" activeColor="#5552FF" :height="20"
					inactiveColor="#ffffff"></tn-line-progress>
			</view>
			<view class="total"> <text style="color: #FF000A;">{{questIndex+1}}</text> /{{reciteExamList.length}}</view>
		</view>
		<view class="quest_body">
			<quest-item ref="quertRemItem" :questTitle="questTitle" @changebg="changebg"></quest-item>
		</view>
		<view class="quest_footer tn-flex tn-flex-row-between tn-flex-col-center" v-if="type==1">
			<view class="qf_left" @click.stop="lastQuest()">
				<text class="tn-icon-left"></text>
			</view>
			<view class="qf_btn" @click.stop="submit('0')">
				未掌握
			</view>
			<view class="qf_btn2" @click.stop="submit('1')">
				掌握
			</view>
			<view class="qf_right">
				<text class="tn-icon-right" @click.stop="nextQuest()"></text>
			</view>
		</view>
		<view class="quest_footer tn-flex tn-flex-row-between tn-flex-col-center" v-if="type==0">
			<view class="qf_left" @click.stop="lastQuest()">
				<text class="tn-icon-left"></text>
			</view>
			<view class="qf_btn" @click.stop="submit('0')">
				困难
			</view>
			<view class="qf_btn2" @click.stop="submit('1')">
				简单
			</view>
			<view class="qf_right">
				<text class="tn-icon-right" @click.stop="nextQuest()"></text>
			</view>
		</view>
	</view>
</template>

<script>
	import questItem from "@/components/quest_item.vue"
	import shareMixin from "@/mixins/shareMixin.js"
	export default {
		mixins: [shareMixin],
		components: {
			questItem
		},
		data() {
			return {
				questTitle: "",
				questSub: "",
				chapter_id: null,
				reciteExamList: [],
				now: 0,
				total: 0,
				per: 0,
				questItem: {},
				questIndex: 0,
				nowIndex: 0,
				questIds: {},
				isSubArr: [],
				type: null
			};
		},
		onLoad(options) {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			if (options.chapter_id) {
				this.chapter_id = options.chapter_id
				this.type = options.type
				this.getExamList()
			}
		},

		methods: {
			setContent() {
				this.$refs.quertRemItem.setContent({
					questTitle: this.questTitle,
					questSub: this.questSub
				})
			},
			lastQuest() {
				if (this.questIndex != 0) {
					this.questIndex -= 1
					this.questTitle = this.reciteExamList[this.questIndex].title
					this.questItem = this.reciteExamList[this.questIndex]
					if (this.reciteExamList[this.questIndex].type == 1) {
						this.questSub = this.colorWord(this.$publicjs.formatRichText(this.reciteExamList[this.questIndex]
							.content), this.reciteExamList[this.questIndex].cover)
						this.setContent()
					} else if (this.reciteExamList[this.questIndex].type == 2) {
						this.questSub = this.bgColorWord(this.$publicjs.formatRichText(this.reciteExamList[this.questIndex]
								.content),
							this.reciteExamList[this.questIndex].cover)
						this.setContent()
					} else {
						this.questSub = this.$publicjs.formatRichText(this.reciteExamList[this.questIndex].content)
						this.setContent()
					}
					this.per = Number(Number(Number(this.questIndex + 1) / Number(this.reciteExamList.length) * 100)
						.toFixed(
							2))
				} else {
					uni.showToast({
						title: "当前已是第一题啦",
						icon: "none"
					})
				}
			},
			nextQuest() {
				if (this.isSubArr.indexOf(this.questItem.id) >= 0) {
					if (this.questIndex != this.reciteExamList.length - 1) {
						this.questIndex += 1
						this.questTitle = this.reciteExamList[this.questIndex].title
						this.questItem = this.reciteExamList[this.questIndex]
						if (this.reciteExamList[this.questIndex].type == 1) {
							this.questSub = this.colorWord(this.$publicjs.formatRichText(this.reciteExamList[this
									.questIndex]
								.content), this.reciteExamList[this.questIndex].cover)
							this.setContent()
						} else if (this.reciteExamList[this.questIndex].type == 2) {
							this.questSub = this.bgColorWord(this.$publicjs.formatRichText(this.reciteExamList[this
										.questIndex]
									.content),
								this.reciteExamList[this.questIndex].cover)
							this.setContent()
						} else {
							this.questSub = this.$publicjs.formatRichText(this.reciteExamList[this.questIndex].content)
							this.setContent()
						}
						this.per = Number(Number(Number(this.questIndex + 1) / Number(this.reciteExamList.length) * 100)
							.toFixed(
								2))
					} else {
						let pages = getCurrentPages();
						let prevPage = pages[pages.length - 2];
						prevPage.$vm.refData();
						uni.navigateBack({
							delta: 1
						})
					}
				} else {
					uni.showToast({
						title: "请选择当前题目",
						icon: "none"
					})

				}
			},
			submit(type) {
				this.$http.post(this.$api.reciteSubmitExam, {
					exam_id: this.questItem.id,
					status: type
				}).then(res => {
					if (res.code == 200) {
						if (this.isSubArr.indexOf(this.questItem.id) < 0) {
							this.isSubArr.push(this.questItem.id)
						}
						this.nextQuest()
					}
				})
			},

			bgColorWord(richText, keyword) {
				// 创建正则表达式，使用全局标志'g'来匹配所有实例
				let us = richText.match(/<u>(.*?)<\/u>/g)
				let replacedText = richText
				let arr = []
				if (this.questIds[this.questItem.id] && this.questIds[this.questItem.id].length) {
					arr = this.questIds[this.questItem.id]
				} else {
					arr = this.questIds[this.questItem.id] = []
				}
				for (let i = 0; i < us.length; i++) {
					let uso = us[i].match(/<u>(.*?)<\/u>/)[1]
					// 执行替换操作
					if (arr.indexOf(String(i)) >= 0) {
						replacedText = replacedText.replace(us[i],
							`<a href="http://localhost:8080?` + i +
							`" style="word-break: break-all; color: #5552FF;">${uso}</a>`
						);
					} else {
						replacedText = replacedText.replace(us[i],
							`<a href="http://localhost:8080?` + i +
							`" style="word-break: break-all;background-color:#E8E7FF; color: #E8E7FF;">${uso}</a>`
						);
					}
				}
				// 输出替换后的富文本内容
				return replacedText
			},
			nobgColorWord(richText, keyword) {
				// 创建正则表达式，使用全局标志'g'来匹配所有实例
				let us = richText.match(/<u>(.*?)<\/u>/g)
				let replacedText = richText
				let arr = []
				if (this.questIds[this.questItem.id] && this.questIds[this.questItem.id].length) {
					arr = this.questIds[this.questItem.id]
				} else {
					this.questIds[this.questItem.id] = []
				}
				for (let i = 0; i < us.length; i++) {
					let uso = us[i].match(/<u>(.*?)<\/u>/)[1]
					// 执行替换操作
					if (arr.indexOf(String(i)) >= 0) {
						replacedText = replacedText.replace(us[i],
							`<a href="http://localhost:8080?` + i +
							`" style="word-break: break-all; color: #5552FF;">${uso}</a>`
						);
					} else {
						replacedText = replacedText.replace(us[i],
							`<a href="http://localhost:8080?` + i +
							`" style="word-break: break-all;background-color:#E8E7FF; color: #E8E7FF;">${uso}</a>`
						);
					}
				}
				// 输出替换后的富文本内容
				return replacedText
			},
			changebg(e) {
				if (this.questIds[this.questItem.id] && this.questIds[this.questItem.id].length > 0) {
					let data = []
					data = JSON.parse(JSON.stringify(this.questIds))[this.questItem.id]
					let arr = []
					if (data.indexOf(e) >= 0) {
						arr = data.filter(item => {
							return item != e
						})
					} else {
						data.push(String(e))
						arr = data
					}
					this.questIds[this.questItem.id] = arr
					this.$nextTick(() => {
						this.questSub = this.nobgColorWord(this.$publicjs.formatRichText(this.questItem
								.content), this
							.questItem.cover)
						this.setContent()
					})
				} else {
					this.questIds[this.questItem.id] = [String(e)]
					this.$nextTick(() => {
						this.questSub = this.nobgColorWord(this.$publicjs.formatRichText(this.questItem
								.content), this
							.questItem.cover)
						this.setContent()
					})
				}
			},
			getExamList() {
				this.$http.post(this.$api.reciteExamList, {
					chapter_id: this.chapter_id,
					grasp: 1
				}).then(res => {
					if (res.code == 200) {
						this.reciteExamList = res.data
						this.questTitle = res.data[0].title
						this.questItem = res.data[0]
						this.questIndex = 0
						if (res.data[0].type == 1) {
							this.questSub = this.colorWord(this.$publicjs.formatRichText(res.data[0].content), res
								.data[0].cover)
							this.setContent()
						} else if (res.data[0].type == 2) {
							this.questSub = this.bgColorWord(this.$publicjs.formatRichText(res.data[0].content),
								res.data[0].cover)
							this.setContent()
						} else {
							this.questSub = this.$publicjs.formatRichText(res.data[0].content)
							this.setContent()
						}
						this.per = Number(Number(Number(this.questIndex + 1) / Number(this.reciteExamList.length) *
								100)
							.toFixed(
								2))
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.quest_body {
		margin-top: 30rpx;
		padding-bottom: calc(env(safe-area-inset-bottom) + 140rpx);
	}

	.quest_footer {
		width: 750rpx;
		position: fixed;
		background-color: #FFFFFF;
		height: calc(env(safe-area-inset-bottom) + 120rpx);
		bottom: 0rpx;
		left: 0rpx;
		right: 0rpx;
		padding: 0rpx 30rpx;
	}

	.qf_left {
		width: 60rpx;
		height: 60rpx;
		border-radius: 12rpx;
		background: #EAEAEA;
		font-size: 45rpx;
		text-align: center;
		line-height: 60rpx;
		color: #979797;
	}

	.qf_right {
		width: 60rpx;
		height: 60rpx;
		border-radius: 12rpx;
		background: #EAEAEA;
		font-size: 45rpx;
		text-align: center;
		line-height: 60rpx;
		color: #979797;
	}

	.qf_btn {
		width: 250rpx;
		height: 80rpx;
		border-radius: 100rpx;
		background-color: #E8E7FF;
		color: #5552FF;
		font-size: 28rpx;
		text-align: center;
		line-height: 80rpx;
	}

	.qf_btn2 {
		width: 250rpx;
		height: 80rpx;
		border-radius: 100rpx;
		background-color: #5552FF;
		color: #FFFFFF;
		font-size: 28rpx;
		text-align: center;
		line-height: 80rpx;
	}

	.total {
		color: #999999;
		font-size: 28rpx;
		margin-left: 10rpx;
	}

	.content {
		width: 100%;
		padding: 30rpx;
		box-sizing: border-box;
	}
</style>