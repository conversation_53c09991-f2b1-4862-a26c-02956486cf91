@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.tn-avatar.data-v-27426dd6 {
  display: inline-flex;
  margin: 0;
  padding: 0;
  text-align: center;
  align-items: center;
  justify-content: center;
  background-color: #E6E6E6;
  white-space: nowrap;
  position: relative;
  width: 64rpx;
  height: 64rpx;
  z-index: 1;
}
.tn-avatar--sm.data-v-27426dd6 {
  width: 48rpx;
  height: 48rpx;
}
.tn-avatar--lg.data-v-27426dd6 {
  width: 96rpx;
  height: 96rpx;
}
.tn-avatar--xl.data-v-27426dd6 {
  width: 128rpx;
  height: 128rpx;
}
.tn-avatar--square.data-v-27426dd6 {
  border-radius: 10rpx;
}
.tn-avatar--circle.data-v-27426dd6 {
  border-radius: 5000rpx;
}
.tn-avatar--shadow.data-v-27426dd6 {
  position: relative;
}
.tn-avatar--shadow.data-v-27426dd6::after {
  content: " ";
  display: block;
  background: inherit;
  -webkit-filter: blur(10rpx);
          filter: blur(10rpx);
  position: absolute;
  width: 100%;
  height: 100%;
  top: 10rpx;
  left: 10rpx;
  z-index: -1;
  opacity: 0.4;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  border-radius: inherit;
  -webkit-transform: scale(1, 1);
          transform: scale(1, 1);
}
.tn-avatar__img.data-v-27426dd6 {
  width: 100%;
  height: 100%;
}
.tn-avatar__img--square.data-v-27426dd6 {
  border-radius: 10rpx;
}
.tn-avatar__img--circle.data-v-27426dd6 {
  border-radius: 5000rpx;
}
.tn-avatar__text.data-v-27426dd6 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

