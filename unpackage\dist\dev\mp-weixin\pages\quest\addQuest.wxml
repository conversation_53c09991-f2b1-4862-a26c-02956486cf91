<view class="content data-v-44c085f2"><view class="header data-v-44c085f2"><view class="tn-text-bold tn-text-lx data-v-44c085f2">上传截图-解锁强化题库</view><view class="tn-width-full tn-flex data-v-44c085f2" style="margin-top:30rpx;"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="{{['data-v-44c085f2',selType==0?'is_sel':'no_sel']}}" catchtap="__e">上传试题</view><view style="width:30rpx;" class="data-v-44c085f2"></view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="{{['data-v-44c085f2',selType==1?'is_sel':'no_sel']}}" catchtap="__e">上传试卷</view></view></view><block wx:if="{{selType==0}}"><view class="body data-v-44c085f2"><tn-form vue-id="534f3642-1" model="{{form1}}" errorType="{{['message']}}" data-ref="form" class="data-v-44c085f2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><tn-form-item vue-id="{{('534f3642-2')+','+('534f3642-1')}}" label="标题" labelStyle="{{labelStyle}}" required="{{true}}" prop="title" borderBottom="{{false}}" labelPosition="top" class="data-v-44c085f2" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full data-v-44c085f2" style="background-color:#F8F8F8;padding:30rpx;box-sizing:border-box;border-radius:20rpx;"><tn-input bind:input="__e" vue-id="{{('534f3642-3')+','+('534f3642-2')}}" type="textarea" placeholder="如果是选择题，试题内容需包含选项" value="{{form1.title}}" data-event-opts="{{[['^input',[['__set_model',['$0','title','$event',[]],['form1']]]]]}}" class="data-v-44c085f2" bind:__l="__l"></tn-input></view></tn-form-item><tn-form-item vue-id="{{('534f3642-4')+','+('534f3642-1')}}" label="答案" labelStyle="{{labelStyle}}" required="{{true}}" prop="solution" borderBottom="{{false}}" labelPosition="top" class="data-v-44c085f2" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full data-v-44c085f2" style="background-color:#F8F8F8;padding:30rpx;box-sizing:border-box;border-radius:20rpx;"><tn-input bind:input="__e" vue-id="{{('534f3642-5')+','+('534f3642-4')}}" type="textarea" placeholder="请输入完整答案" value="{{form1.solution}}" data-event-opts="{{[['^input',[['__set_model',['$0','solution','$event',[]],['form1']]]]]}}" class="data-v-44c085f2" bind:__l="__l"></tn-input></view></tn-form-item><tn-form-item vue-id="{{('534f3642-6')+','+('534f3642-1')}}" label="解析" labelStyle="{{labelStyle}}" prop="analysis" borderBottom="{{false}}" labelPosition="top" class="data-v-44c085f2" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full data-v-44c085f2" style="background-color:#F8F8F8;padding:30rpx;box-sizing:border-box;border-radius:20rpx;"><tn-input bind:input="__e" vue-id="{{('534f3642-7')+','+('534f3642-6')}}" type="textarea" placeholder="如果没有解析可以不填写" value="{{form1.analysis}}" data-event-opts="{{[['^input',[['__set_model',['$0','analysis','$event',[]],['form1']]]]]}}" class="data-v-44c085f2" bind:__l="__l"></tn-input></view></tn-form-item><tn-form-item vue-id="{{('534f3642-8')+','+('534f3642-1')}}" label="试题来源" labelStyle="{{labelStyle}}" prop="source" borderBottom="{{false}}" labelPosition="top" class="data-v-44c085f2" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full data-v-44c085f2" style="background-color:#F8F8F8;padding:30rpx;box-sizing:border-box;border-radius:20rpx;"><tn-input bind:input="__e" vue-id="{{('534f3642-9')+','+('534f3642-8')}}" type="textarea" placeholder="填写试题的来源教材或网课名称，如《1000题》马原第一章第二题" value="{{form1.source}}" data-event-opts="{{[['^input',[['__set_model',['$0','source','$event',[]],['form1']]]]]}}" class="data-v-44c085f2" bind:__l="__l"></tn-input></view></tn-form-item></tn-form><view class="tn-width-full tn-flex tn-flex-row-center tn-flex-col-center data-v-44c085f2"><view data-event-opts="{{[['tap',[['submit1']]]]}}" class="{{['data-v-44c085f2',isSub?'submit':'nosubmit']}}" catchtap="__e">提交</view></view></view></block><block wx:if="{{selType==1}}"><view class="body data-v-44c085f2"><tn-form vue-id="534f3642-10" model="{{form2}}" errorType="{{['message']}}" data-ref="form" class="data-v-44c085f2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><tn-form-item vue-id="{{('534f3642-11')+','+('534f3642-10')}}" label="标题" labelStyle="{{labelStyle}}" required="{{true}}" prop="title" borderBottom="{{false}}" labelPosition="top" class="data-v-44c085f2" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full data-v-44c085f2" style="background-color:#F8F8F8;padding:30rpx;box-sizing:border-box;border-radius:20rpx;"><tn-input bind:input="__e" vue-id="{{('534f3642-12')+','+('534f3642-11')}}" type="textarea" placeholder="如：英语" value="{{form2.title}}" data-event-opts="{{[['^input',[['__set_model',['$0','title','$event',[]],['form2']]]]]}}" class="data-v-44c085f2" bind:__l="__l"></tn-input></view></tn-form-item><tn-form-item vue-id="{{('534f3642-13')+','+('534f3642-10')}}" label="考试全称" required="{{true}}" labelStyle="{{labelStyle}}" prop="exam_title" borderBottom="{{false}}" labelPosition="top" class="data-v-44c085f2" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full data-v-44c085f2" style="background-color:#F8F8F8;padding:30rpx;box-sizing:border-box;border-radius:20rpx;"><tn-input bind:input="__e" vue-id="{{('534f3642-14')+','+('534f3642-13')}}" type="textarea" placeholder="如：2022高等数学A期末考试" value="{{form2.exam_title}}" data-event-opts="{{[['^input',[['__set_model',['$0','exam_title','$event',[]],['form2']]]]]}}" class="data-v-44c085f2" bind:__l="__l"></tn-input></view></tn-form-item><tn-form-item vue-id="{{('534f3642-15')+','+('534f3642-10')}}" label="试卷文档" required="{{true}}" labelStyle="{{labelStyle}}" prop="src" borderBottom="{{false}}" labelPosition="top" class="data-v-44c085f2" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{form2.src}}"><view data-event-opts="{{[['tap',[['uploadFile']]]]}}" class="upload2 data-v-44c085f2" catchtap="__e">文件已上传，可点击更改或提交</view></block><block wx:else><view data-event-opts="{{[['tap',[['uploadFile']]]]}}" class="upload tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-center data-v-44c085f2" catchtap="__e"><text class="tn-icon-add tn-text-bold data-v-44c085f2" style="font-size:44rpx;color:#999999;"></text></view></block></tn-form-item></tn-form><view class="tn-width-full tn-flex tn-flex-row-center tn-flex-col-center data-v-44c085f2"><view data-event-opts="{{[['tap',[['submit1']]]]}}" class="{{['data-v-44c085f2',isSub?'submit':'nosubmit']}}" catchtap="__e">提交</view></view></view></block><tn-popup bind:input="__e" vue-id="534f3642-16" mode="bottom" borderRadius="{{40}}" value="{{showKefu}}" data-event-opts="{{[['^input',[['__set_model',['','showKefu','$event',[]]]]]]}}" class="data-v-44c085f2" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold data-v-44c085f2" style="width:750rpx;height:120rpx;padding:30rpx;box-sizing:border-box;background:url('../../static/pop_head.png') no-repeat;background-size:100% 100%;">客服帮助</view><view class="tn-width-full data-v-44c085f2"><view class="tn-flex tn-flex-row-center data-v-44c085f2" style="padding:30rpx 46rpx 66rpx 46rpx;box-sizing:border-box;"><view style="width:400rpx;height:400rpx;" class="data-v-44c085f2"><image style="width:400rpx;height:400rpx;" show-menu-by-longpress="{{true}}" src="{{baseUrl+service_img}}" mode class="data-v-44c085f2"></image></view></view></view></tn-popup></view>