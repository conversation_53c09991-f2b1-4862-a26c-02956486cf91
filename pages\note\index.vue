<template>
	<view>
		<view class="top tn-flex tn-flex-row-between tn-flex-col-center">
			<view class="top_left tn-text-ellipsis tn-text-bold">{{selBookItem.title||""}}</view>
			<view class="top_right tn-flex tn-flex-col-center" v-if="list_type.length>0">
				<view class="tr_word tn-text-ellipsis tn-text-right" @click.stop="showQuest=true">
					{{selTemItem.template_name||""}}
				</view>
				<view class="tn-icon-right"></view>
			</view>
		</view>
		<view class="tn-width-full" style="padding: 30rpx;box-sizing: border-box;">
			<view v-for="(item,index) in noteBook" :key="index" class="note_item" @click.stop="toItem(item)">
				<view class="note_top tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
					<image src="../../static/icon/zhang_note.png" mode="heightFix" style="width: 176rpx;height: 48rpx;">
					</image>
					<view class="" style="font-size: 28rpx;color: #666666;">
						共{{item.num||0}}个错题笔记<text class="tn-icon-right" style="margin-left: 5rpx;"></text>
					</view>
				</view>
				<view class="tn-text-bold" style="margin-top: 20rpx;color: #333333;font-size: 28rpx;">
					{{item.chapterTitle}}
				</view>
			</view>
		</view>
		<tn-popup v-model="showQuest" mode="bottom" :borderRadius="40" safeAreaInsetBottom @close="close">
			<view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				请选择</view>
			<view class="scroll_warp tn-width-full">
				<scroll-view scroll-y="true" style="width: 100%;height: 100%;">
					<view v-for="(item,index) in list_type" :key="index" style="margin-bottom: 40rpx;"
						class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center" @click.stop="selTem(item)">
						<view class="q_pop_left tn-text-ellipsis">
							{{item.template_name}}
						</view>
						<view class="" v-if="item.template_id!=selTemItem1.template_id">
							<image src="../../static/icon/nosel_icon.png" mode="widthFix"
								style="width: 44rpx;height: 44rpx;"></image>
						</view>
						<view class="" v-if="item.template_id==selTemItem1.template_id">
							<image src="../../static/icon/sel_icon.png" mode="widthFix"
								style="width: 44rpx;height: 44rpx;"></image>
						</view>
					</view>
				</scroll-view>
			</view>
			<view class="tn-flex tn-flex-col-top tn-flex-row-center" style="height: 150rpx;">
				<view class="submit" @click="subTem()">保存设置</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list_type: [],
				selBookItem: {},
				selTemItem: {},
				selTemItem1: {},
				showQuest: false,
				noteBook: []
			};
		},
		onLoad(options) {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			if (options.item) {
				this.selBookItem = JSON.parse(options.item)
				this.getBookTem()
			}
		},

		methods: {
			toItem(item) {
				this.$publicjs.toUrl("/pages/note/inner?item=" + JSON.stringify(item))
			},
			getBookNote() {
				this.$http.post(this.$api.listBookNote, {
					book_id: this.selBookItem.id,
					template_id: this.selTemItem.template_id || 0
				}).then(res => {
					if (res.code == 200) {
						this.noteBook = res.data
					}
				})
			},
			close() {
				this.selTemItem1 = this.selTemItem
			},
			subTem() {
				this.showQuest = false
				this.selTemItem = this.selTemItem1
				this.getBookNote()
			},
			selTem(item) {
				this.selTemItem1 = item
			},
			getBookTem() { //获取书籍模板
				this.$http.post(this.$api.bookTem, {
					book_id: this.selBookItem.id
				}).then(res => {
					if (res.code == 200) {
						this.list_type = res.data
						if (res.data.length > 0) {
							this.selTemItem = this.list_type[0]
							this.selTemItem1 = this.list_type[0]
						}
						this.getBookNote()
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.note_item {
		width: 690rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		box-sizing: border-box;
		background-color: #FFFFFF;
		margin-bottom: 20rpx;
	}

	.submit {
		width: 630rpx;
		height: 96rpx;
		border-radius: 48rpx;
		background: #5552FF;
		font-size: 32rpx;
		color: #FFFFFF;
		text-align: center;
		line-height: 96rpx;
	}

	.q_pop_left {
		width: 550rpx;
	}

	.scroll_warp {
		height: 450rpx;
		padding: 0rpx 40rpx 40rpx 40rpx;
		box-sizing: border-box;
	}

	.scroll_warp2 {
		height: 300rpx;
		padding: 0rpx 40rpx 40rpx 40rpx;
		box-sizing: border-box;
	}

	.check_body {
		margin-top: 20rpx;
		width: 750rpx;
		padding: 30rpx;
		background-color: #FFFFFF;
	}

	.check_inner {
		width: 690rpx;
		height: 100rpx;
		border-radius: 20rpx;
		background-color: #E3E3FF;
		color: #5552FF;
		font-size: 28rpx;
		padding: 0rpx 30rpx;
		font-size: 28rpx;
		font-weight: bold;
	}

	.check_title {
		width: 750rpx;
		height: 140rpx;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		background-color: #FFFFFF;
		padding: 0rpx 30rpx;
	}

	.top {
		width: 750rpx;
		height: 100rpx;
		background-color: #FFFFFF;
		padding: 0rpx 40rpx;
		box-sizing: border-box;

		.top_left {
			color: #222222;
			font-size: 30rpx;
			width: 350rpx;
		}

		.top_right {
			color: #666666;
			font-size: 28rpx;

			.tr_word {
				width: 250rpx;
			}
		}
	}
</style>