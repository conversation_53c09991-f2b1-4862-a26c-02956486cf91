@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.tn-row-notice.data-v-770a6f7a {
  width: 100%;
  overflow: hidden;
}
.tn-row-notice__wrap.data-v-770a6f7a {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.tn-row-notice__content.data-v-770a6f7a {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  text-align: right;
  padding-left: 100%;
  -webkit-animation: tn-notice-loop-animation-data-v-770a6f7a 10s linear infinite both;
          animation: tn-notice-loop-animation-data-v-770a6f7a 10s linear infinite both;
}
.tn-row-notice__content-box.data-v-770a6f7a {
  flex: 1;
  display: flex;
  flex-direction: row;
  overflow: hidden;
  margin-left: 12rpx;
}
.tn-row-notice__content--text.data-v-770a6f7a {
  word-break: keep-all;
  white-space: nowrap;
}
.tn-row-notice__icon--left.data-v-770a6f7a {
  display: inline-flex;
  align-items: center;
}
.tn-row-notice__icon--right.data-v-770a6f7a {
  margin-left: 12rpx;
  display: inline-flex;
  align-items: center;
}
@-webkit-keyframes tn-notice-loop-animation-data-v-770a6f7a {
0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
}
100% {
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
}
}
@keyframes tn-notice-loop-animation-data-v-770a6f7a {
0% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
}
100% {
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
}
}

