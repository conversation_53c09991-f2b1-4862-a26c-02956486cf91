/**
 * 分享功能混入
 * 提供统一的分享功能，方便各页面使用
 */

import shareHelper from '@/utils/shareHelper.js'

export default {
  data() {
    return {
      shareImageUrl: '', // 分享图片URL
      shareTitle: '考研政治刷题库，名师题库免费刷，超100w考生都在用！',
      sharePath: '/pages/quest/quest'
    }
  },

  async onLoad() {
    // 设置分享菜单
    // #ifdef MP-WEIXIN
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
    // #endif

    // 预加载分享图片
    this.loadShareImage()
  },

  methods: {
    /**
     * 加载分享图片
     */
    async loadShareImage() {
      try {
        this.shareImageUrl = await shareHelper.getShareImage()
      } catch (error) {
        console.error('加载分享图片失败:', error)
        // 使用默认图片
        this.shareImageUrl = this.$config.baseUrl + '/static/222.jpg?t=' + new Date().getTime()
      }
    },

    /**
     * 设置分享配置
     * @param {Object} options 分享选项
     * @param {string} options.title 分享标题
     * @param {string} options.path 分享路径
     */
    setShareConfig(options = {}) {
      if (options.title) {
        this.shareTitle = options.title
      }
      if (options.path) {
        this.sharePath = options.path
      }
    },

    /**
     * 获取分享配置对象
     * @returns {Object} 分享配置对象
     */
    getShareConfigObject() {
      return {
        title: this.shareTitle,
        path: this.sharePath,
        imageUrl: this.shareImageUrl || (this.$config.baseUrl + '/static/222.jpg?t=' + new Date().getTime())
      }
    }
  },

  /**
   * 微信小程序分享到聊天
   */
  onShareAppMessage() {
    return this.getShareConfigObject()
  },

  /**
   * 微信小程序分享到朋友圈
   */
  onShareTimeline() {
    return this.getShareConfigObject()
  }
}
