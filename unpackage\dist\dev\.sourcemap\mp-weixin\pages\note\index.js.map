{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/project/shuati_new/pages/note/index.vue?71ac", "webpack:///D:/project/shuati_new/pages/note/index.vue?27df", "webpack:///D:/project/shuati_new/pages/note/index.vue?a572", "webpack:///D:/project/shuati_new/pages/note/index.vue?a84d", "uni-app:///pages/note/index.vue", "webpack:///D:/project/shuati_new/pages/note/index.vue?c32c", "webpack:///D:/project/shuati_new/pages/note/index.vue?cbdd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "list_type", "selBookItem", "selTemItem", "selTemItem1", "showQuest", "noteBook", "onLoad", "withShareTicket", "menus", "methods", "toItem", "getBookNote", "book_id", "template_id", "close", "subTem", "selTem", "getBookTem"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuDtnB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEAZ;MAAA;MACAa;MACAC;IACA;IAEA;MACA;MACA;IACA;EACA;EAEAC;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACAC;QACAC;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAAA;MACA;QACAL;MACA;QACA;UACA;UACA;YACA;YACA;UACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvHA;AAAA;AAAA;AAAA;AAAqqC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACAzrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/note/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/note/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1a1f51a4&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1a1f51a4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1a1f51a4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/note/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1a1f51a4&scoped=true&\"", "var components\ntry {\n  components = {\n    tnPopup: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-popup/tn-popup\" */ \"@/tuniao-ui/components/tn-popup/tn-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list_type.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.showQuest = true\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"top tn-flex tn-flex-row-between tn-flex-col-center\">\r\n\t\t\t<view class=\"top_left tn-text-ellipsis tn-text-bold\">{{selBookItem.title||\"\"}}</view>\r\n\t\t\t<view class=\"top_right tn-flex tn-flex-col-center\" v-if=\"list_type.length>0\">\r\n\t\t\t\t<view class=\"tr_word tn-text-ellipsis tn-text-right\" @click.stop=\"showQuest=true\">\r\n\t\t\t\t\t{{selTemItem.template_name||\"\"}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-icon-right\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"tn-width-full\" style=\"padding: 30rpx;box-sizing: border-box;\">\r\n\t\t\t<view v-for=\"(item,index) in noteBook\" :key=\"index\" class=\"note_item\" @click.stop=\"toItem(item)\">\r\n\t\t\t\t<view class=\"note_top tn-width-full tn-flex tn-flex-row-between tn-flex-col-center\">\r\n\t\t\t\t\t<image src=\"../../static/icon/zhang_note.png\" mode=\"heightFix\" style=\"width: 176rpx;height: 48rpx;\">\r\n\t\t\t\t\t</image>\r\n\t\t\t\t\t<view class=\"\" style=\"font-size: 28rpx;color: #666666;\">\r\n\t\t\t\t\t\t共{{item.num||0}}个错题笔记<text class=\"tn-icon-right\" style=\"margin-left: 5rpx;\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-text-bold\" style=\"margin-top: 20rpx;color: #333333;font-size: 28rpx;\">\r\n\t\t\t\t\t{{item.chapterTitle}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<tn-popup v-model=\"showQuest\" mode=\"bottom\" :borderRadius=\"40\" safeAreaInsetBottom @close=\"close\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t请选择</view>\r\n\t\t\t<view class=\"scroll_warp tn-width-full\">\r\n\t\t\t\t<scroll-view scroll-y=\"true\" style=\"width: 100%;height: 100%;\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in list_type\" :key=\"index\" style=\"margin-bottom: 40rpx;\"\r\n\t\t\t\t\t\tclass=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center\" @click.stop=\"selTem(item)\">\r\n\t\t\t\t\t\t<view class=\"q_pop_left tn-text-ellipsis\">\r\n\t\t\t\t\t\t\t{{item.template_name}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\" v-if=\"item.template_id!=selTemItem1.template_id\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/icon/nosel_icon.png\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\t\tstyle=\"width: 44rpx;height: 44rpx;\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\" v-if=\"item.template_id==selTemItem1.template_id\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/icon/sel_icon.png\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\t\tstyle=\"width: 44rpx;height: 44rpx;\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-flex tn-flex-col-top tn-flex-row-center\" style=\"height: 150rpx;\">\r\n\t\t\t\t<view class=\"submit\" @click=\"subTem()\">保存设置</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tlist_type: [],\r\n\t\t\t\tselBookItem: {},\r\n\t\t\t\tselTemItem: {},\r\n\t\t\t\tselTemItem1: {},\r\n\t\t\t\tshowQuest: false,\r\n\t\t\t\tnoteBook: []\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\tif (options.item) {\r\n\t\t\t\tthis.selBookItem = JSON.parse(options.item)\r\n\t\t\t\tthis.getBookTem()\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\ttoItem(item) {\r\n\t\t\t\tthis.$publicjs.toUrl(\"/pages/note/inner?item=\" + JSON.stringify(item))\r\n\t\t\t},\r\n\t\t\tgetBookNote() {\r\n\t\t\t\tthis.$http.post(this.$api.listBookNote, {\r\n\t\t\t\t\tbook_id: this.selBookItem.id,\r\n\t\t\t\t\ttemplate_id: this.selTemItem.template_id || 0\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.noteBook = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.selTemItem1 = this.selTemItem\r\n\t\t\t},\r\n\t\t\tsubTem() {\r\n\t\t\t\tthis.showQuest = false\r\n\t\t\t\tthis.selTemItem = this.selTemItem1\r\n\t\t\t\tthis.getBookNote()\r\n\t\t\t},\r\n\t\t\tselTem(item) {\r\n\t\t\t\tthis.selTemItem1 = item\r\n\t\t\t},\r\n\t\t\tgetBookTem() { //获取书籍模板\r\n\t\t\t\tthis.$http.post(this.$api.bookTem, {\r\n\t\t\t\t\tbook_id: this.selBookItem.id\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.list_type = res.data\r\n\t\t\t\t\t\tif (res.data.length > 0) {\r\n\t\t\t\t\t\t\tthis.selTemItem = this.list_type[0]\r\n\t\t\t\t\t\t\tthis.selTemItem1 = this.list_type[0]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.getBookNote()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.note_item {\r\n\t\twidth: 690rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.submit {\r\n\t\twidth: 630rpx;\r\n\t\theight: 96rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t\tbackground: #5552FF;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\ttext-align: center;\r\n\t\tline-height: 96rpx;\r\n\t}\r\n\r\n\t.q_pop_left {\r\n\t\twidth: 550rpx;\r\n\t}\r\n\r\n\t.scroll_warp {\r\n\t\theight: 450rpx;\r\n\t\tpadding: 0rpx 40rpx 40rpx 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.scroll_warp2 {\r\n\t\theight: 300rpx;\r\n\t\tpadding: 0rpx 40rpx 40rpx 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.check_body {\r\n\t\tmargin-top: 20rpx;\r\n\t\twidth: 750rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t}\r\n\r\n\t.check_inner {\r\n\t\twidth: 690rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #E3E3FF;\r\n\t\tcolor: #5552FF;\r\n\t\tfont-size: 28rpx;\r\n\t\tpadding: 0rpx 30rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.check_title {\r\n\t\twidth: 750rpx;\r\n\t\theight: 140rpx;\r\n\t\tborder-radius: 0rpx 0rpx 20rpx 20rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tpadding: 0rpx 30rpx;\r\n\t}\r\n\r\n\t.top {\r\n\t\twidth: 750rpx;\r\n\t\theight: 100rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tpadding: 0rpx 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\r\n\t\t.top_left {\r\n\t\t\tcolor: #222222;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\twidth: 350rpx;\r\n\t\t}\r\n\r\n\t\t.top_right {\r\n\t\t\tcolor: #666666;\r\n\t\t\tfont-size: 28rpx;\r\n\r\n\t\t\t.tr_word {\r\n\t\t\t\twidth: 250rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1a1f51a4&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1a1f51a4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980619925\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}