@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-table-scroll {
  width: 100%;
  overflow-x: auto;
}
.uni-table {
  position: relative;
  width: 100%;
  border-radius: 5px;
  background-color: #fff;
  box-sizing: border-box;
  display: table;
  overflow-x: auto;
}
.uni-table  .uni-table-tr:nth-child(n + 2):hover {
  background-color: #f5f7fa;
}
.uni-table  .uni-table-thead .uni-table-tr:hover {
  background-color: #fafafa;
}
.table--border {
  border: 1px #ebeef5 solid;
  border-right: none;
}
.border-none {
  border-bottom: none;
}
.table--stripe  .uni-table-tr:nth-child(2n + 3) {
  background-color: #fafafa;
}
/* 表格加载、无数据样式 */
.uni-table-loading {
  position: relative;
  display: table-row;
  height: 220px;
  line-height: 220px;
  overflow: hidden;
  box-sizing: border-box;
}
.empty-border {
  border-right: 1px #ebeef5 solid;
}
.uni-table-text {
  position: absolute;
  right: 0;
  left: 0;
  text-align: center;
  font-size: 14px;
  color: #999;
}
.uni-table-mask {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 99;
  display: flex;
  margin: auto;
  transition: all 0.5s;
  justify-content: center;
  align-items: center;
}
.uni-table--loader {
  width: 30px;
  height: 30px;
  border: 2px solid #aaa;
  border-radius: 50%;
  -webkit-animation: 2s uni-table--loader linear infinite;
          animation: 2s uni-table--loader linear infinite;
  position: relative;
}
@-webkit-keyframes uni-table--loader {
0% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
10% {
    border-left-color: transparent;
}
20% {
    border-bottom-color: transparent;
}
30% {
    border-right-color: transparent;
}
40% {
    border-top-color: transparent;
}
50% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
60% {
    border-top-color: transparent;
}
70% {
    border-left-color: transparent;
}
80% {
    border-bottom-color: transparent;
}
90% {
    border-right-color: transparent;
}
100% {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg);
}
}
@keyframes uni-table--loader {
0% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
10% {
    border-left-color: transparent;
}
20% {
    border-bottom-color: transparent;
}
30% {
    border-right-color: transparent;
}
40% {
    border-top-color: transparent;
}
50% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
60% {
    border-top-color: transparent;
}
70% {
    border-left-color: transparent;
}
80% {
    border-bottom-color: transparent;
}
90% {
    border-right-color: transparent;
}
100% {
    -webkit-transform: rotate(-360deg);
            transform: rotate(-360deg);
}
}

