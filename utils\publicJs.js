export default {
	toUrl(url, isToken = false) {
		if (isToken) {
			if (uni.getStorageSync('TOKEN')) {} else {
				uni.reLaunch({
					url: "/pages/login/login"
				})
				return false
			}
		}
		uni.navigateTo({
			url
		})
	},
	toTab(url, isToken = false) {
		if (isToken) {
			if (uni.getStorageSync('TOKEN')) {} else {
				uni.reLaunch({
					url: "/pages/login/login"
				})
				return false
			}
		}
		uni.switchTab({
			url
		})
	},
	toLaunch(url, isToken = false) {
		if (isToken) {
			if (uni.getStorageSync('TOKEN')) {} else {
				uni.showToast({
					title: "请先去登录！",
					icon: "none",
					duration: 1500
				})
				return false
			}
		}
		uni.reLaunch({
			url
		})
	},
	formatRichText(html) {
		// html 就是你要传进来地富文本参数
		// 去掉img标签里的style、width、height属性
		let newContent = html.replace(/<img[^>]*>/gi, function(match, capture) {
			match = match.replace(/style="[^"]*"/gi, '')
			//console.log("sss", match);
			return match;
		});
		//console.log("ddd", newContent);
		// 修改所有style里的width属性为max-width:100%
		newContent = newContent.replace(/\<img/gi,
			'<img style="max-width:100%;height:auto;display:block;margin:0px auto;"');
		// 去掉<br/>标签
		newContent = newContent.replace(/<br[^>]*\/>/gi, '');
		// 返回 处理后地结果
		return newContent;
	},
	copyText(wordData) {
		uni.setClipboardData({
			data: wordData,
			success: function() {
				uni.showToast({
					title: '复制成功',
					icon: 'success',
					duration: 2000
				});
			}
		});
	},
	numToCode(data) {
		let data2 = []
		if (typeof data === "string") {
			data2 = data.split(",")
		} else if (typeof data === "object" && Array.isArray(data)) {
			data2 = data
		}
		let arr = []
		for (var i = 0; i < data2.length; i++) {
			arr.push(String.fromCharCode(64 + Number(data2[i])))
		}
		return arr.join(" ")
	},
	checkType(value) {

	},
	timeMin(num) {
		let time_s = num;
		let minute = Math.floor(time_s / 60);
		let rest_seconds = time_s % 60;
		return minute.toString().padStart(2, '0') + ":" + rest_seconds.toString().padStart(2, '0')
	},
	goMinifun(appId, path, extraData = {}) {
		console.log(123)
		uni.navigateToMiniProgram({
			appId: appId || '',
			path: path || '',
			extraData: extraData || {
				'params': '1'
			},
			success(res) {
				console.log('已打开')
			}
		})
	}
}