<view style="padding-bottom:150rpx;" class="data-v-1b27d1e7"><view class="header data-v-1b27d1e7"><view class="header_top tn-flex tn-flex-direction-column tn-flex-row-between data-v-1b27d1e7"><view class="tn-width-full data-v-1b27d1e7"><view class="tn-width-full data-v-1b27d1e7" style="font-size:26rpx;color:#666666;">{{resDetail.template_name+''}}</view><view class="tn-width-full tn-text-bold data-v-1b27d1e7" style="margin-top:10rpx;font-size:30rpx;color:#333333;">{{resDetail.title}}</view></view><view class="tn-width-full tn-flex tn-flex-row-between data-v-1b27d1e7"><view class="tn-flex tn-flex-direction-column tn-flex-col-top data-v-1b27d1e7"><view class="title data-v-1b27d1e7">用时(min)</view><view class="num data-v-1b27d1e7">{{''+(resDetail.use_time>0?$root.g0:0)+''}}</view></view><view class="tn-flex tn-flex-direction-column tn-flex-col-top data-v-1b27d1e7"><view class="title data-v-1b27d1e7">正确率</view><view class="num data-v-1b27d1e7">{{''+(resDetail.correct_ratio||0)+'%'}}</view></view><view class="tn-flex tn-flex-direction-column tn-flex-col-top data-v-1b27d1e7"><view class="title data-v-1b27d1e7">平均正确率</view><view class="num data-v-1b27d1e7">{{''+(resDetail.avg_correct_ratio||0)+'%'}}</view></view></view></view><view class="header_bottom tn-flex tn-flex-direction-column tn-flex-row-between data-v-1b27d1e7"><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-1b27d1e7"><view class="data-v-1b27d1e7"><text style="color:#666666;font-size:26rpx;" class="data-v-1b27d1e7">正确率超过<text style="color:#FF000A;font-weight:bold;" class="data-v-1b27d1e7">{{resDetail.than_other||0}}</text>%的研友</text></view><tn-circle-progress vue-id="49d2ebff-1" percent="{{resDetail.than_other||0}}" width="{{128}}" borderWidth="{{12}}" activeColor="#5552FF" inactiveColor="#D8D8D8" class="data-v-1b27d1e7" bind:__l="__l" vue-slots="{{['default']}}"><view style="color:#222222;font-size:20rpx;" class="data-v-1b27d1e7">{{(resDetail.than_other||0)+"%"}}</view></tn-circle-progress></view><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-around data-v-1b27d1e7"><view data-event-opts="{{[['tap',[['toAnalysis',['1']]]]]}}" class="h_btn1 data-v-1b27d1e7" catchtap="__e">全部解析</view><view data-event-opts="{{[['tap',[['toAnalysis',['2']]]]]}}" class="h_btn2 data-v-1b27d1e7" catchtap="__e">错题研究</view></view></view></view><view class="card_warp data-v-1b27d1e7"><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-1b27d1e7"><view class="tn-flex tn-flex-col-center data-v-1b27d1e7"><text class="tn-text-bold data-v-1b27d1e7" style="font-size:30rpx;color:#333333;">答题卡</text></view><view class="tn-flex tn-flex-col-center data-v-1b27d1e7" style="font-size:24rpx;color:#333333;"><view class="tn-flex tn-flex-col-center data-v-1b27d1e7" style="margin-right:55rpx;"><view style="margin-right:8rpx;width:14rpx;height:14rpx;border-radius:50%;border:2rpx solid #5552FF;" class="data-v-1b27d1e7"></view>正确</view><view class="tn-flex tn-flex-col-center data-v-1b27d1e7"><view style="margin-right:8rpx;width:14rpx;height:14rpx;border-radius:50%;border:2rpx solid #FF585F;" class="data-v-1b27d1e7"></view>错误</view></view></view><block wx:if="{{$root.g1}}"><view class="tn-width-full tn-flex tn-flex-wrap tn-flex-row-between data-v-1b27d1e7"><block wx:for="{{resDetail.topic_record}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['toAnalysis',['1','$0'],[[['resDetail.topic_record','',index,'topic_id']]]]]]]}}" class="{{['quest_or','data-v-1b27d1e7',item.result==1?'quest_suc':'quest_err']}}" catchtap="__e">{{''+item.title_id+''}}</view></block><block wx:for="{{5-$root.g2%5}}" wx:for-item="item" wx:for-index="index"><view style="width:100rpx;margin-left:10rpx;margin-right:10rpx;" class="data-v-1b27d1e7"></view></block></view></block></view><view class="footer_model tn-flex tn-flex-col-center tn-flex-row-between data-v-1b27d1e7"><view data-event-opts="{{[['tap',[['back']]]]}}" class="f_btn1 data-v-1b27d1e7" catchtap="__e">返回列表</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="f_btn2 data-v-1b27d1e7" catchtap="__e">章笔记</view></view><tn-popup bind:input="__e" vue-id="49d2ebff-2" mode="bottom" borderRadius="{{40}}" safeAreaInsetBottom="{{true}}" value="{{showAdd}}" data-event-opts="{{[['^input',[['__set_model',['','showAdd','$event',[]]]]]]}}" class="data-v-1b27d1e7" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center tn-text-bold data-v-1b27d1e7" style="width:750rpx;height:120rpx;padding:30rpx;box-sizing:border-box;background:url('../../static/pop_head.png') no-repeat;background-size:100% 100%;"><view style="font-size:34rpx;" class="data-v-1b27d1e7">章笔记</view></view><view class="scroll_warp2 tn-width-full data-v-1b27d1e7"><tn-input bind:input="__e" vue-id="{{('49d2ebff-3')+','+('49d2ebff-2')}}" placeholder="开始输入..." clearable="{{false}}" type="textarea" border="{{false}}" height="{{324}}" autoHeight="{{false}}" value="{{note_value}}" data-event-opts="{{[['^input',[['__set_model',['','note_value','$event',[]]]]]]}}" class="data-v-1b27d1e7" bind:__l="__l"></tn-input></view><view class="tn-flex tn-flex-col-top tn-flex-row-center data-v-1b27d1e7" style="height:150rpx;"><view data-event-opts="{{[['tap',[['submitNote']]]]}}" class="submit data-v-1b27d1e7" catchtap="__e">保存</view></view></tn-popup></view>