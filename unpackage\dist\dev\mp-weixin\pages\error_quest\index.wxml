<view class="data-v-bbcc50f6"><view class="top tn-flex tn-flex-row-between tn-flex-col-center data-v-bbcc50f6"><view class="top_left tn-text-ellipsis tn-text-bold data-v-bbcc50f6">{{name}}</view><view data-event-opts="{{[['tap',[['showQuestfun']]]]}}" class="top_right tn-flex tn-flex-col-center data-v-bbcc50f6" catchtap="__e"><view class="tr_word tn-text-ellipsis tn-text-right data-v-bbcc50f6">{{''+(selTemItem.template_name||'')+''}}</view><view class="tn-icon-right data-v-bbcc50f6"></view></view></view><view class="tips_warp tn-flex tn-flex-row-between tn-flex-col-center data-v-bbcc50f6"><view style="width:500rpx;" class="data-v-bbcc50f6"><tn-tabs vue-id="bdbccbbe-1" list="{{list_type}}" bold="{{true}}" isScroll="{{false}}" activeColor="#333333" inactiveColor="#9E9E9E" current="{{current}}" name="name" data-event-opts="{{[['^change',[['change']]]]}}" bind:change="__e" class="data-v-bbcc50f6" bind:__l="__l"></tn-tabs></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="export_btn data-v-bbcc50f6" catchtap="__e">导出PDF</view></view><view class="body data-v-bbcc50f6"><view class="tn-width-full tn-flex tn-flex-row-around data-v-bbcc50f6" style="margin-top:20rpx;margin-bottom:20rpx;"><image style="width:326rpx;height:148rpx;" src="../../static/vip_btn.png" mode="widthFix" data-event-opts="{{[['tap',[['toRember']]]]}}" catchtap="__e" class="data-v-bbcc50f6"></image><image style="width:326rpx;height:148rpx;" src="../../static/err_btn.png" mode="widthFix" data-event-opts="{{[['tap',[['toRember']]]]}}" catchtap="__e" class="data-v-bbcc50f6"></image></view><block wx:if="{{$root.g0==0}}"><view class="tn-width-full tn-flex tn-flex-row-center data-v-bbcc50f6"><image style="width:404rpx;" src="../../static/empty.png" mode="widthFix" class="data-v-bbcc50f6"></image></view></block><block wx:for="{{errorList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tn-width-full data-v-bbcc50f6" style="margin-bottom:20rpx;"><error-model vue-id="{{'bdbccbbe-2-'+index}}" item="{{item}}" foldId="{{foldId}}" data-event-opts="{{[['^changeFold',[['changeFold']]],['^toQuest',[['toQuest']]]]}}" bind:changeFold="__e" bind:toQuest="__e" class="data-v-bbcc50f6" bind:__l="__l"></error-model></view></block></view><block wx:if="{{current==2}}"><view class="error_footer tn-flex tn-flex-direction-column tn-flex-row-between data-v-bbcc50f6"><view class="tn-flex tn-flex-row-between data-v-bbcc50f6"><view class="ef_left data-v-bbcc50f6"><view class="efl_top data-v-bbcc50f6">右边可设置每组刷题道数</view><view class="efl_bottom data-v-bbcc50f6">共错<text style="color:#FF000A;" class="data-v-bbcc50f6">{{total}}</text>道，已解决<text style="color:#5552FF;" class="data-v-bbcc50f6">{{res_total}}</text>道</view></view><tn-number-box bind:input="__e" vue-id="bdbccbbe-3" min="{{10}}" max="{{9999}}" positiveInteger="{{true}}" disabledInput="{{true}}" step="{{5}}" inputWidth="{{68}}" value="{{value}}" data-event-opts="{{[['^input',[['__set_model',['','value','$event',[]]]]]]}}" class="data-v-bbcc50f6" bind:__l="__l"></tn-number-box></view><view data-event-opts="{{[['tap',[['toSort']]]]}}" class="ef_sub data-v-bbcc50f6" catchtap="__e">乱序刷题</view></view></block><tn-popup vue-id="bdbccbbe-4" mode="bottom" borderRadius="{{40}}" safeAreaInsetBottom="{{true}}" value="{{showQuest}}" data-event-opts="{{[['^close',[['close']]],['^input',[['__set_model',['','showQuest','$event',[]]]]]]}}" bind:close="__e" bind:input="__e" class="data-v-bbcc50f6" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold data-v-bbcc50f6" style="width:750rpx;height:120rpx;padding:30rpx;box-sizing:border-box;background:url('../../static/pop_head.png') no-repeat;background-size:100% 100%;">请选择</view><view class="scroll_warp tn-width-full data-v-bbcc50f6"><scroll-view style="width:100%;height:100%;" scroll-y="true" class="data-v-bbcc50f6"><block wx:for="{{temList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selTem',['$0'],[[['temList','',index]]]]]]]}}" class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center data-v-bbcc50f6" style="margin-bottom:40rpx;" catchtap="__e"><view class="q_pop_left tn-text-ellipsis data-v-bbcc50f6">{{''+item.template_name+''}}</view><block wx:if="{{item.template_id!=selTemItem1.template_id}}"><view class="data-v-bbcc50f6"><image style="width:44rpx;height:44rpx;" src="../../static/icon/nosel_icon.png" mode="widthFix" class="data-v-bbcc50f6"></image></view></block><block wx:if="{{item.template_id==selTemItem1.template_id}}"><view class="data-v-bbcc50f6"><image style="width:44rpx;height:44rpx;" src="../../static/icon/sel_icon.png" mode="widthFix" class="data-v-bbcc50f6"></image></view></block></view></block></scroll-view></view><view class="tn-flex tn-flex-col-top tn-flex-row-center data-v-bbcc50f6" style="height:150rpx;"><view data-event-opts="{{[['tap',[['subTem']]]]}}" class="submit data-v-bbcc50f6" bindtap="__e">保存设置</view></view></tn-popup><tn-select vue-id="bdbccbbe-5" mode="single" list="{{typeData}}" value="{{showType}}" data-event-opts="{{[['^confirm',[['confirmPdf']]],['^input',[['__set_model',['','showType','$event',[]]]]]]}}" bind:confirm="__e" bind:input="__e" class="data-v-bbcc50f6" bind:__l="__l"></tn-select></view>