<template>
	<view class="rember_item" :class="{'red':index%4==0,'blue':index%4==1,'green':index%4==2,'purple':index%4==3}">
		<view class="rember_item_top tn-width-full tn-flex tn-flex-col-center tn-flex-row-between">
			<view class="rember_item_top_left tn-flex tn-flex-col-center">
				<view class="rember_item_line"
					:class="{'red_line':index%4==0,'blue_line':index%4==1,'green_line':index%4==2,'purple_line':index%4==3}">
				</view>
				<view class="rember_item_title tn-text-bold tn-text-ellipsis">
					{{item.title}}
				</view>
			</view>
			<view class="rember_item_top_right" v-if="selIndex!==index" @click.stop="changeFold('show')">
				展开<text class="tn-icon tn-icon-down" style="margin-left: 5rpx;font-size: 28rpx;"></text>
			</view>
			<view class="rember_item_top_right" v-if="selIndex===index" @click.stop="changeFold('noshow')">
				收起<text class="tn-icon tn-icon-up" style="margin-left: 5rpx;font-size: 28rpx;"></text>
			</view>
		</view>
		<view class="rember_item_body tn-width-full tn-flex tn-flex-wrap tn-flex-row-between">
			<view v-for="(it,ind) in item.book_list" :key="ind" v-if="selIndex===index?ind>=0:ind<4"
				class="rib_item tn-flex tn-flex-row-between tn-flex-col-center" @click.stop="toRember(it)">
				<image src="../static/icon/edit.png" mode="widthFix" style="width: 40rpx;height: 40rpx;"></image>
				<view class="ribi_word tn-text-ellipsis-2">
					{{it.title}}
				</view>
				<view class="tn-icon-right" style="font-size: 26rpx;color: #555555;"></view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "rember_item",
		props: {
			item: {
				type: Object,
				default: () => {}
			},
			index: {
				type: Number,
				default: 0
			},
			selIndex: {
				type: Number,
				default: -1
			},
		},
		data() {
			return {

			};
		},
		methods: {
			toRember(it) {
				this.$emit("toRember", it)
			},
			changeFold(type) {
				if (type == 'show') {
					this.$emit("showFold", this.index)
				}
				if (type == 'noshow') {
					this.$emit("noShowFold", this.index)
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.rib_item {
		width: 316rpx;
		height: 90rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		margin-top: 20rpx;
		padding: 12rpx 20rpx;
		box-sizing: border-box;

		.ribi_word {
			width: 195rpx;
			color: #333333;
			font-size: 12px;
			text-align: center;
		}
	}

	.rember_item_body {
		padding: 0rpx 20rpx 0rpx 20rpx;
		box-sizing: border-box;
	}

	.red_line {
		background-color: #FF585F;
	}

	.blue_line {
		background-color: #5552FF;
	}

	.green_line {
		background-color: #00864E;
	}

	.purple_line {
		background-color: #E552FF;
	}

	.red {
		background-color: #FFEAEA;
	}

	.blue {
		background-color: #F1F0FF;
	}

	.green {
		background-color: #E5F2ED;
	}

	.purple {
		background-color: #FAE9FD;
	}

	.rember_item_top_right {
		font-size: 24rpx;
		color: #555555;
	}

	.rember_item_top_left {
		width: 550rpx;
	}

	.rember_item {
		width: 690rpx;
		border-radius: 20rpx;
		padding: 20rpx 0rpx 30rpx 0rpx;
		box-sizing: border-box;

		.rember_item_top {
			padding-right: 20rpx;
			box-sizing: border-box;

			.rember_item_line {
				width: 10rpx;
				height: 68rpx;
				border-radius: 0rpx 10rpx 10rpx 0rpx;
				margin-right: 20rpx;
			}

			.rember_item_title {
				width: 510rpx;
				font-size: 36rpx;
				color: #333333;
			}
		}
	}
</style>