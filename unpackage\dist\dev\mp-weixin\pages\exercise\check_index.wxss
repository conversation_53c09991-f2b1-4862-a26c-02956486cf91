@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.submit.data-v-027dd71d {
  width: 630rpx;
  height: 96rpx;
  border-radius: 48rpx;
  background: #5552FF;
  font-size: 32rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 96rpx;
}
.q_pop_left.data-v-027dd71d {
  width: 550rpx;
}
.scroll_warp.data-v-027dd71d {
  height: 450rpx;
  padding: 0rpx 40rpx 40rpx 40rpx;
  box-sizing: border-box;
}
.scroll_warp2.data-v-027dd71d {
  height: 300rpx;
  padding: 0rpx 40rpx 40rpx 40rpx;
  box-sizing: border-box;
}
.check_body.data-v-027dd71d {
  margin-top: 20rpx;
  width: 750rpx;
  padding: 30rpx;
  background-color: #FFFFFF;
}
.check_inner.data-v-027dd71d {
  width: 690rpx;
  height: 100rpx;
  border-radius: 20rpx;
  background-color: #E3E3FF;
  color: #5552FF;
  font-size: 28rpx;
  padding: 0rpx 30rpx;
  font-size: 28rpx;
  font-weight: bold;
}
.check_title.data-v-027dd71d {
  width: 750rpx;
  height: 140rpx;
  border-radius: 0rpx 0rpx 20rpx 20rpx;
  background-color: #FFFFFF;
  padding: 0rpx 30rpx;
}
.top.data-v-027dd71d {
  width: 750rpx;
  height: 100rpx;
  background-color: #FFFFFF;
  padding: 0rpx 40rpx;
  box-sizing: border-box;
}
.top .top_left.data-v-027dd71d {
  color: #222222;
  font-size: 30rpx;
  width: 350rpx;
}
.top .top_right.data-v-027dd71d {
  color: #666666;
  font-size: 28rpx;
}
.top .top_right .tr_word.data-v-027dd71d {
  width: 250rpx;
}

