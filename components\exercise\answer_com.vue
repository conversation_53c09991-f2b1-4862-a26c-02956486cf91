<template>
	<view class="answer_warp tn-width-full">
		<view v-for="(item,index) in allData" :key="index"
			class="answer_item tn-flex tn-flex-col-center tn-flex-row-between"
			:class="suc_index.indexOf(index)>=0?'suc_bg':index==sel_index?'err_bg':'nom_bg'" @click.stop="selAns(index)">
			<view :class="suc_index.indexOf(index)>=0?'suc_word':'tn-width-full'">
				{{item.option}}
			</view>
			<image src="../../static/icon/suc_sel.png" mode="widthFix" style="width: 44rpx;height: 44rpx;"
				v-if="suc_index.indexOf(index)>=0">
			</image>
		</view>
	</view>
</template>

<script>
	export default {
		name: "answer_com",
		data() {
			return {
				sel_index: null,
				suc_index: [],
				item: {},
				allData: []
			};
		},
		methods: {
			changeData(item, answer, isAns = false) {
				this.item = item
				this.allData = item.option
				this.sel_index = null
				this.suc_index = []
				this.sel_index = answer[item.id] ? answer[item.id] - 1 : null
				if (isAns) {
					this.suc_index = item.solution.map(item => {
						return item -= 1
					})
				}
			},
			selAns(index) {
				this.sel_index = index
				this.$emit('selAnswer', this.item, [index + 1])
			}
		}
	}
</script>

<style lang="scss" scoped>
	.answer_warp {}

	.suc_word {
		width: 550rpx;
	}

	.nom_bg {
		background-color: #F7F7F7;
	}

	.err_bg {
		background-color: #fff6e4;
	}

	.suc_bg {
		background-color: #DDFFF5;
	}

	.answer_item {
		width: 690rpx;
		border-radius: 20rpx;
		padding: 40rpx;
		box-sizing: border-box;
		margin-bottom: 40rpx;
		color: #333333;
		font-size: 28rpx;
	}
</style>