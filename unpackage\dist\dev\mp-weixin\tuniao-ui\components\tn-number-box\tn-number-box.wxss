@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.tn-number-box.data-v-61aeb8b2 {
  display: inline-flex;
  align-items: center;
}
.tn-number-box__btn__plus.data-v-61aeb8b2, .tn-number-box__btn__minus.data-v-61aeb8b2 {
  width: 60rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: #E6E6E6;
}
.tn-number-box__btn__plus.data-v-61aeb8b2 {
  border-radius: 0 8rpx 8rpx 0;
}
.tn-number-box__btn__minus.data-v-61aeb8b2 {
  border-radius: 8rpx 0 0 8rpx;
}
.tn-number-box__btn--disabled.data-v-61aeb8b2 {
  color: #AAAAAA !important;
  background: #E6E6E6 !important;
}
.tn-number-box__input.data-v-61aeb8b2 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: relative;
  text-align: center;
  box-sizing: border-box;
  padding: 0 4rpx;
  margin: 0 6rpx;
  background-color: #E6E6E6;
}
.tn-number-box__input--disabled.data-v-61aeb8b2 {
  color: #AAAAAA !important;
  background: #E6E6E6 !important;
}

