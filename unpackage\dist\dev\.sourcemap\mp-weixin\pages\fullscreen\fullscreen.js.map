{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/project/shuati_new/pages/fullscreen/fullscreen.vue?3296", "webpack:///D:/project/shuati_new/pages/fullscreen/fullscreen.vue?8778", "webpack:///D:/project/shuati_new/pages/fullscreen/fullscreen.vue?64c7", "webpack:///D:/project/shuati_new/pages/fullscreen/fullscreen.vue?a229", "uni-app:///pages/fullscreen/fullscreen.vue", "webpack:///D:/project/shuati_new/pages/fullscreen/fullscreen.vue?47c6", "webpack:///D:/project/shuati_new/pages/fullscreen/fullscreen.vue?f0ee"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "show", "content", "detail", "progress", "onReady", "methods", "getversion", "plus", "uni", "url", "title", "success", "statusCode", "confirmText", "confirmColor", "q", "confirm", "install", "downloadResult", "tempFile<PERSON>ath", "force", "res", "clear", "restart", "quit", "downloadTask", "cancel", "closeModal"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAumB,CAAgB,onBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;eCU3nB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,6BAIA;EACAC;IACAC;MAAA;MACAC;QACA;UACA;YACA;cACAC;gBACAC;cACA;YACA;cACA;cACA;cACAD;gBACAE;gBACAT;gBACAU;kBACA;oBAAA;oBACA;oBACA;sBAAA;sBACAF;sBAAA;sBACAE;wBAAA;wBACA;wBACA,mBACAC,cACA,KACA;0BACAJ;4BACAE;4BACAT;4BACAY;4BACAC;4BACAH,0BACAI,GACA;8BACA,MACAC,WACA,MACA;gCACAT,aACAU;gCAAA;gCACAC,eACAC;kCACAC;gCACA,GACA,UACAC,KACA;kCACAd,WACAe;kCACAf,aACAgB;gCACA,EACA;8BACA;gCACAhB,aACAiB;8BACA;4BACA;0BACA;wBACA;sBACA;oBACA;;oBACAC;sBACA;oBACA;kBACA;oBACAlB;kBACA;gBACA;cACA;YACA;UAEA;QACA;MACA;IACA;IACAmB;MACA;MACAlB;QACAC;MACA;IACA;IACAO;IACAW;MACAnB;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjHA;AAAA;AAAA;AAAA;AAA0qC,CAAgB,2nCAAG,EAAC,C;;;;;;;;;;;ACA9rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/fullscreen/fullscreen.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/fullscreen/fullscreen.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./fullscreen.vue?vue&type=template&id=0ad76734&scoped=true&\"\nvar renderjs\nimport script from \"./fullscreen.vue?vue&type=script&lang=js&\"\nexport * from \"./fullscreen.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fullscreen.vue?vue&type=style&index=0&id=0ad76734&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0ad76734\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/fullscreen/fullscreen.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fullscreen.vue?vue&type=template&id=0ad76734&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fullscreen.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fullscreen.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"fullapp\">\r\n\t\t<view class=\"fullModel\" v-if=\"show\">\r\n\t\t\t<view class=\"title\">为了不影响使用，本次为强制升级！</view>\r\n\t\t\t<progress :percent=\"progress\" show-info stroke-width=\"15\" activeColor=\"#007AFF\" />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshow: false,\r\n\t\t\t\tcontent: \"\",\r\n\t\t\t\tdetail: {},\r\n\t\t\t\tprogress: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tthis.getversion()\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetversion() {\r\n\t\t\t\tplus.runtime.getProperty(plus.runtime.appid, (wgtinfo) => {\r\n\t\t\t\t\tthis.$http.post(this.$api.version).then(suc => {\r\n\t\t\t\t\t\tif (suc.code == 1) {\r\n\t\t\t\t\t\t\tif (wgtinfo.version == suc.data.newversion) {\r\n\t\t\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\t\t\turl: \"../home/<USER>\"\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.content = suc.data.content\r\n\t\t\t\t\t\t\t\tthis.detail = suc.data\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle: \"发现新版本\",\r\n\t\t\t\t\t\t\t\t\tcontent: \"确认下载更新\",\r\n\t\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\t\tif (res.confirm == true) { //当用户确定更新，执行更新\r\n\t\t\t\t\t\t\t\t\t\t\tthis.show = true\r\n\t\t\t\t\t\t\t\t\t\t\tlet downloadTask = uni.downloadFile({ //执行下载\r\n\t\t\t\t\t\t\t\t\t\t\t\turl: suc.data.downloadurl, //下载地址\r\n\t\t\t\t\t\t\t\t\t\t\t\tsuccess: downloadResult => { //下载成功\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tthis.show = false\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tif (downloadResult\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t.statusCode ==\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t200\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcontent: '更新成功，确定现在重启吗？',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconfirmText: '重启',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconfirmColor: '#EE8F57',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsuccess: function(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tq\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (q\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.confirm ==\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttrue\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplus.runtime\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.install( //安装\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdownloadResult\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.tempFilePath, {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tforce: true\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tfunction(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tres\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplus.cache\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.clear()\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplus.runtime\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.restart();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tplus.runtime\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t.quit(); // 退出应用\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\tdownloadTask.onProgressUpdate((res) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tthis.progress = res.progress\r\n\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\t\tplus.runtime.quit(); // 退出应用\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcancel() {\r\n\t\t\t\tthis.closeModal();\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: \"../home/<USER>\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tconfirm() {},\r\n\t\t\tcloseModal() {\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: \"../home/<USER>\"\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\t.fullModel /deep/ .uni-progress-bar {\r\n\t\tborder-radius: 40rpx;\r\n\t}\r\n\r\n\t.fullModel /deep/ .uni-progress-inner-bar {\r\n\t\tborder-radius: 40rpx;\r\n\t}\r\n\r\n\t.title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 700;\r\n\t\ttext-align: center;\r\n\r\n\t}\r\n\r\n\t.fullModel {\r\n\t\twidth: 90vw;\r\n\t\theight: 20vh;\r\n\t\tbackground-color: #ffffff;\r\n\t\tmargin: 0rpx auto;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 3vw;\r\n\t\tbox-sizing: border-box;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: space-around;\r\n\t}\r\n\r\n\t.fullapp {\r\n\t\twidth: 100vw;\r\n\t\theight: 100vh;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t// .u-full-content {\r\n\t// \tbackground-color: #00C777;\r\n\t// }\r\n\r\n\t// .u-update-content {\r\n\t// \tfont-size: 26rpx;\r\n\t// \tcolor: $u-content-color;\r\n\t// \tline-height: 1.7;\r\n\t// \tpadding: 30rpx;\r\n\t// }\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fullscreen.vue?vue&type=style&index=0&id=0ad76734&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./fullscreen.vue?vue&type=style&index=0&id=0ad76734&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980402599\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}