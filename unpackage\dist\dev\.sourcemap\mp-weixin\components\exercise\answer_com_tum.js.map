{"version": 3, "sources": ["webpack:///D:/project/shuati_new/components/exercise/answer_com_tum.vue?9463", "webpack:///D:/project/shuati_new/components/exercise/answer_com_tum.vue?705f", "webpack:///D:/project/shuati_new/components/exercise/answer_com_tum.vue?1cdc", "webpack:///D:/project/shuati_new/components/exercise/answer_com_tum.vue?3d87", "uni-app:///components/exercise/answer_com_tum.vue", "webpack:///D:/project/shuati_new/components/exercise/answer_com_tum.vue?25ec", "webpack:///D:/project/shuati_new/components/exercise/answer_com_tum.vue?3fec"], "names": ["name", "data", "sel_index", "suc_index", "item", "allData", "methods", "changeData", "selAns"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAA2mB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiB/nB;EACAA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;QACA;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;UACA;QACA;MACA;QACA;MACA;MACA;QACA;QACA;UACA;QACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AClEA;AAAA;AAAA;AAAA;AAA8qC,CAAgB,+nCAAG,EAAC,C;;;;;;;;;;;ACAlsC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/exercise/answer_com_tum.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./answer_com_tum.vue?vue&type=template&id=66e9ac79&scoped=true&\"\nvar renderjs\nimport script from \"./answer_com_tum.vue?vue&type=script&lang=js&\"\nexport * from \"./answer_com_tum.vue?vue&type=script&lang=js&\"\nimport style0 from \"./answer_com_tum.vue?vue&type=style&index=0&id=66e9ac79&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"66e9ac79\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/exercise/answer_com_tum.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./answer_com_tum.vue?vue&type=template&id=66e9ac79&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.allData, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = _vm.suc_index.indexOf(index)\n    var g1 = !(g0 >= 0) ? _vm.sel_index.indexOf(index) : null\n    var g2 = _vm.suc_index.indexOf(index)\n    var g3 = _vm.suc_index.indexOf(index)\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n      g2: g2,\n      g3: g3,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./answer_com_tum.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./answer_com_tum.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"answer_warp tn-width-full\">\r\n\t\t<view v-for=\"(item,index) in allData\" :key=\"index\"\r\n\t\t\tclass=\"answer_item tn-flex tn-flex-col-center tn-flex-row-between\"\r\n\t\t\t:class=\"suc_index.indexOf(index)>=0?'suc_bg':sel_index.indexOf(index)>=0?'err_bg':'nom_bg'\"\r\n\t\t\************=\"selAns(index)\">\r\n\t\t\t<view :class=\"suc_index.indexOf(index)>=0?'suc_word':'tn-width-full'\">\r\n\t\t\t\t{{item.option}}\r\n\t\t\t</view>\r\n\t\t\t<image src=\"../../static/icon/suc_sel.png\" mode=\"widthFix\" style=\"width: 44rpx;height: 44rpx;\"\r\n\t\t\t\tv-if=\"suc_index.indexOf(index)>=0\">\r\n\t\t\t</image>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: \"answer_com_tum\",\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tsel_index: [],\r\n\t\t\t\tsuc_index: [],\r\n\t\t\t\titem: {},\r\n\t\t\t\tallData: []\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tchangeData(item, answer, isAns = false) {\r\n\t\t\t\tthis.item = item\r\n\t\t\t\tthis.allData = item.option\r\n\t\t\t\tthis.sel_index = []\r\n\t\t\t\tthis.suc_index = []\r\n\t\t\t\tif (answer[item.id] && answer[item.id].length > 0) {\r\n\t\t\t\t\tthis.sel_index = answer[item.id].map(item => {\r\n\t\t\t\t\t\treturn item - 1\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.sel_index = []\r\n\t\t\t\t}\r\n\t\t\t\tif (isAns) {\r\n\t\t\t\t\tthis.suc_index = item.solution.map(item => {\r\n\t\t\t\t\t\treturn item -= 1\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tselAns(index) {\r\n\t\t\t\tif (this.sel_index.indexOf(index) >= 0) {\r\n\t\t\t\t\tlet data = JSON.parse(JSON.stringify(this.sel_index))\r\n\t\t\t\t\tthis.sel_index = data.filter(item => {\r\n\t\t\t\t\t\treturn item != index\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.sel_index.push(index)\r\n\t\t\t\t}\r\n\t\t\t\tif (this.sel_index.length > 0) {\r\n\t\t\t\t\tlet data = JSON.parse(JSON.stringify(this.sel_index))\r\n\t\t\t\t\tlet arr = data.map(item => {\r\n\t\t\t\t\t\treturn item + 1\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$emit('selAnswer', this.item, arr)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$emit('selAnswer', this.item, null)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.answer_warp {}\r\n\r\n\t.suc_word {\r\n\t\twidth: 550rpx;\r\n\t}\r\n\r\n\t.nom_bg {\r\n\t\tbackground-color: #F7F7F7;\r\n\t}\r\n\r\n\t.err_bg {\r\n\t\tbackground-color: #fff6e4;\r\n\t}\r\n\r\n\t.suc_bg {\r\n\t\tbackground-color: #DDFFF5;\r\n\t}\r\n\r\n\t.answer_item {\r\n\t\twidth: 690rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\tcolor: #333333;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./answer_com_tum.vue?vue&type=style&index=0&id=66e9ac79&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./answer_com_tum.vue?vue&type=style&index=0&id=66e9ac79&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980404528\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}