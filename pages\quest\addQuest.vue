<template>
	<view class="content">
		<view class="header">
			<view class="tn-text-bold tn-text-lx">
				上传截图-解锁强化题库
			</view>
			<view class="tn-width-full tn-flex" style="margin-top: 30rpx;">
				<view :class="selType==0?'is_sel':'no_sel'" @click.stop="selType=0">
					上传试题
				</view>
				<view style="width: 30rpx;"></view>
				<view :class="selType==1?'is_sel':'no_sel'" @click.stop="selType=1">
					上传试卷
				</view>
			</view>
		</view>
		<view class="body" v-if="selType==0">
			<tn-form :model="form1" ref="form" :errorType="['message']">
				<tn-form-item label="标题" :labelStyle="labelStyle" required prop="title" :borderBottom="false"
					labelPosition="top">
					<view class="tn-width-full"
						style="background-color: #F8F8F8;padding: 30rpx;box-sizing: border-box;border-radius: 20rpx;">
						<tn-input type="textarea" placeholder="如果是选择题，试题内容需包含选项" v-model="form1.title" />
					</view>
				</tn-form-item>
				<tn-form-item label="答案" :labelStyle="labelStyle" required prop="solution" :borderBottom="false"
					labelPosition="top">
					<view class="tn-width-full"
						style="background-color: #F8F8F8;padding: 30rpx;box-sizing: border-box;border-radius: 20rpx;">
						<tn-input type="textarea" placeholder="请输入完整答案" v-model="form1.solution" />
					</view>
				</tn-form-item>
				<tn-form-item label="解析" :labelStyle="labelStyle" prop="analysis" :borderBottom="false"
					labelPosition="top">
					<view class="tn-width-full"
						style="background-color: #F8F8F8;padding: 30rpx;box-sizing: border-box;border-radius: 20rpx;">
						<tn-input type="textarea" placeholder="如果没有解析可以不填写" v-model="form1.analysis" />
					</view>
				</tn-form-item>
				<tn-form-item label="试题来源" :labelStyle="labelStyle" prop="source" :borderBottom="false"
					labelPosition="top">
					<view class="tn-width-full"
						style="background-color: #F8F8F8;padding: 30rpx;box-sizing: border-box;border-radius: 20rpx;">
						<tn-input type="textarea" placeholder="填写试题的来源教材或网课名称，如《1000题》马原第一章第二题"
							v-model="form1.source" />
					</view>
				</tn-form-item>
			</tn-form>
			<view class="tn-width-full tn-flex tn-flex-row-center tn-flex-col-center">
				<view :class="isSub?'submit':'nosubmit'" @click.stop="submit1()">
					提交
				</view>
			</view>
		</view>
		<view class="body" v-if="selType==1">
			<tn-form :model="form2" ref="form" :errorType="['message']">
				<tn-form-item label="标题" :labelStyle="labelStyle" required prop="title" :borderBottom="false"
					labelPosition="top">
					<view class="tn-width-full"
						style="background-color: #F8F8F8;padding: 30rpx;box-sizing: border-box;border-radius: 20rpx;">
						<tn-input type="textarea" placeholder="如：英语" v-model="form2.title" />
					</view>
				</tn-form-item>
				<tn-form-item label="考试全称" required :labelStyle="labelStyle" prop="exam_title" :borderBottom="false"
					labelPosition="top">
					<view class="tn-width-full"
						style="background-color: #F8F8F8;padding: 30rpx;box-sizing: border-box;border-radius: 20rpx;">
						<tn-input type="textarea" placeholder="如：2022高等数学A期末考试" v-model="form2.exam_title" />
					</view>
				</tn-form-item>
				<tn-form-item label="试卷文档" required :labelStyle="labelStyle" prop="src" :borderBottom="false"
					labelPosition="top">
					<view class="upload2" v-if="form2.src" @click.stop="uploadFile()">
						<!-- <image :src="baseUrl+img1" mode="aspectFill" style="width: 100%;height: 100%;"></image> -->
						文件已上传，可点击更改或提交
					</view>
					<view @click.stop="uploadFile()"
						class="upload tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-center" v-else>
						<text class="tn-icon-add tn-text-bold" style="font-size: 44rpx;color: #999999;"></text>
					</view>
				</tn-form-item>
			</tn-form>
			<view class="tn-width-full tn-flex tn-flex-row-center tn-flex-col-center">
				<view :class="isSub?'submit':'nosubmit'" @click.stop="submit1()">
					提交
				</view>
			</view>
		</view>
		<tn-popup v-model="showKefu" mode="bottom" :borderRadius="40">
			<view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				客服帮助</view>
			<view class="tn-width-full" style="">
				<view class="tn-flex tn-flex-row-center"
					style="padding: 30rpx 46rpx 66rpx 46rpx;box-sizing: border-box;">
					<view style="width: 400rpx;height: 400rpx;">
						<image show-menu-by-longpress style="width: 400rpx;height: 400rpx;" :src="baseUrl+service_img"
							mode=""></image>
					</view>
				</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	import shareMixin from "@/mixins/shareMixin.js"
	export default {
		mixins: [shareMixin],
		data() {
			return {
				labelStyle: {
					fontSize: '30rpx',
					fontWeight: 'bold'
				},
				baseUrl: this.$config.baseUrl,
				selType: 0,
				code: "",
				isSub: true,
				service_img: "",
				showKefu: false,
				img1: "",
				img2: "",
				form1: {
					title: '',
					solution: '',
					analysis: '',
					source: ''
				},
				form2: {
					title: "",
					exam_title: "",
					src: ""
				}
			}
		},
		onLoad() {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			this.getsysconfig()
		},

		methods: {
			submit1() {
				let data = {}
				if (this.selType == 0) {
					if (!this.form1.title || !this.form1.solution) {
						uni.showToast({
							title: "请填写必填项",
							icon: "none"
						})
						return false
					}
					data = {
						type: this.selType,
						...this.form1
					}
				}
				if (this.selType == 1) {
					if (!this.form2.title || !this.form2.exam_title || !this.form2.src) {
						uni.showToast({
							title: "请填写必填项",
							icon: "none"
						})
						return false
					}
					data = {
						type: this.selType,
						...this.form2
					}
				}
				this.$http.post(this.$api.uploadExam, data).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: "上传成功",
							icon: "none",
							success: () => {
								uni.navigateBack()
							}
						})
					}
				})
			},
			uploadFile() {
				let profile = ''
				wx.chooseMessageFile({
					count: 1, //默认9
					type: 'file',
					success: (res) => {
						profile = res.tempFiles[0].path;
						this.myUpload(profile)
					}
				});
			},
			myUpload(rsp) {
				uni.showLoading({
					mask: true,
				})
				uni.uploadFile({
					url: this.$config.baseUrl + "/api/upload/upload",
					header: {
						'Authorization': uni.getStorageSync('TOKEN'),
					},
					filePath: rsp,
					name: 'file',
					success: (ress) => {
						uni.hideLoading()
						let mdata = JSON.parse(ress.data);
						if (mdata.code == 200) {
							this.form2.src = mdata.data.src
							this.$forceUpdate()
						} else {
							uni.showToast({
								title: mdata.msg,
								duration: 2000,
								icon: 'none'
							});
						}
					},
					fail: () => {
						uni.hideLoading()
					}
				});
			},
			submit() {
				let data = []
				if (this.img1) {
					data.push(this.img1)
				}
				if (this.img2) {
					data.push(this.img2)
				}
				if (data.length == 0) {
					uni.showToast({
						title: "请上传图片",
						icon: "none"
					})
					return false
				}
				this.$http.post(this.$api.shareActive, {
					img: data
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: "上传成功，等待审核",
							icon: "none",
							success: () => {
								uni.navigateBack()
							}
						})
					}
				})
			},
			getsysconfig() { // 获取系统配置
				this.$http.post(this.$api.systemData, {}).then(res => {
					if (res.code == 200) {
						let datas = res.data;
						this.service_img = datas.config.service_img
						uni.setStorageSync('systemData', datas)
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.body {
		width: 750rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		padding: 0rpx 30rpx 30rpx 30rpx;
		box-sizing: border-box;
		margin-top: 30rpx;
	}

	.is_sel {
		width: 180rpx;
		height: 88rpx;
		border-radius: 30rpx;
		background-color: #E3E3FF;
		font-size: 28rpx;
		color: #5552FF;
		font-weight: bold;
		text-align: center;
		line-height: 88rpx;
	}

	.no_sel {
		width: 180rpx;
		height: 88rpx;
		border-radius: 30rpx;
		background-color: #F7F7F7;
		font-size: 28rpx;
		color: #666666;
		text-align: center;
		line-height: 88rpx;
	}

	.upload2 {
		width: 450rpx;
		border-radius: 20rpx;
		background-color: #F7F7F7;
		box-sizing: border-box;
		text-align: center;
		font-size: 28rpx;
		color: #666666;
		border: 2rpx dashed #999999;
	}

	.upload {
		width: 160rpx;
		height: 160rpx;
		border-radius: 20rpx;
		background-color: #F7F7F7;
		box-sizing: border-box;
		border: 2rpx dashed #999999;
	}

	.submit_body {
		padding: 30rpx;
		box-sizing: border-box;
		background-color: #FFFFFF;
		border-radius: 30rpx;
	}

	page {
		background-color: #f7f7f7 !important;
	}

	.header {
		width: 750rpx;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		padding: 30rpx;
		box-sizing: border-box;
		background-color: #FFFFFF;
	}

	.fab_btn {
		width: 154rpx;
		position: fixed;
		right: 0rpx;
		bottom: 150rpx;
	}

	.submit {
		width: 630rpx;
		height: 80rpx;
		border-radius: 50rpx;
		background-color: #3775F6;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #Ffffff;
		margin-top: 30rpx;
	}

	.nosubmit {
		width: 630rpx;
		height: 80rpx;
		border-radius: 50rpx;
		background-color: #9f9f9f;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #Ffffff;
		margin-top: 30rpx;
	}

	.input_warp {
		width: 630rpx;
		height: 114rpx;
		border-radius: 20rpx;
		background-color: #F4F4F4;
		margin-top: 24rpx;
		padding: 0rpx 40rpx;
		box-sizing: border-box;
	}

	page {
		background-color: #Ffffff;
	}

	.content {
		width: 100%;
		box-sizing: border-box;
	}
</style>