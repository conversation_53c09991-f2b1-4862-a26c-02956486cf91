{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-swiper/tn-swiper.vue?7729", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-swiper/tn-swiper.vue?c2db", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-swiper/tn-swiper.vue?f223", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-swiper/tn-swiper.vue?5495", "uni-app:///tuniao-ui/components/tn-swiper/tn-swiper.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-swiper/tn-swiper.vue?a874", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-swiper/tn-swiper.vue?52d5"], "names": ["name", "props", "list", "type", "default", "current", "height", "backgroundColor", "title", "<PERSON><PERSON><PERSON>", "titleStyle", "radius", "mode", "indicatorPosition", "effect3d", "effect3dPreviousSpacing", "autoplay", "interval", "duration", "circular", "imageMode", "computed", "backgroundColorStyle", "backgroundColorClass", "swiperStyle", "style", "indicatorStyle", "swiperTitleStyle", "data", "swiperIndex", "watch", "methods", "click", "change"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAqnB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC4EzoB;EACAA;EACAC;IACA;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAJ;MACAG;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;QACA;MACA;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;EACA;EACAiB;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACAC;MACA;MACA;QACAA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACAD;QACAA;MACA;QACAA;QACAA;MACA;MACAA;MAEA;IACA;IACAE;MACA;MACA;MACA;QACAF;MACA;QACAA;MACA;QACAA;MACA;MAEAA;MACA;IACA;EACA;EACAG;IACA;MACA;MACAC;IACA;EACA;EACAC;IACA5B;MACA;MACA;IACA;IACAG;MACA;MACA;IACA;EACA;EACA0B;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC5PA;AAAA;AAAA;AAAA;AAAosC,CAAgB,0nCAAG,EAAC,C;;;;;;;;;;;ACAxtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-swiper/tn-swiper.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-swiper.vue?vue&type=template&id=4ba19b58&scoped=true&\"\nvar renderjs\nimport script from \"./tn-swiper.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-swiper.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-swiper.vue?vue&type=style&index=0&id=4ba19b58&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4ba19b58\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-swiper/tn-swiper.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-swiper.vue?vue&type=template&id=4ba19b58&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.swiperStyle])\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var s1 =\n      _vm.title && item[_vm.titleName]\n        ? _vm.__get_style([_vm.titleStyle])\n        : null\n    return {\n      $orig: $orig,\n      s1: s1,\n    }\n  })\n  var s2 = _vm.__get_style([_vm.indicatorStyle])\n  var g0 = _vm.mode === \"number\" ? _vm.list.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        l0: l0,\n        s2: s2,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-swiper.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-swiper.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"tn-swiper__wrap-class tn-swiper__wrap\" :style=\"{borderRadius: `${radius}rpx`}\">\r\n    <!-- 轮播图 -->\r\n    <swiper\r\n      class=\"tn-swiper\"\r\n      :class=\"[backgroundColorClass]\"\r\n      :style=\"[swiperStyle]\"\r\n      :current=\"current\"\r\n      :interval=\"interval\"\r\n      :circular=\"circular\"\r\n      :autoplay=\"autoplay\"\r\n      :duration=\"duration\"\r\n      :previous-margin=\"effect3d ? effect3dPreviousSpacing + 'rpx' : '0'\"\r\n      :next-margin=\"effect3d ? effect3dPreviousSpacing + 'rpx' : '0'\"\r\n      @change=\"change\"\r\n    >\r\n      <swiper-item\r\n        v-for=\"(item, index) in list\"\r\n        :key=\"index\"\r\n        class=\"tn-swiper__item\"\r\n      >\r\n        <view\r\n          class=\"tn-swiper__item__image__wrap\"\r\n          :class=\"[swiperIndex !== index ? 'tn-swiper__item__image--scale' : '']\"\r\n          :style=\"{\r\n            borderRadius: `${radius}rpx`,\r\n            transform: effect3d && swiperIndex !== index ? 'scaleY(0.9)' : 'scaleY(1)',\r\n            margin: effect3d && swiperIndex !== index ? '0 20rpx' : 0\r\n          }\"\r\n          @click=\"click(index)\"\r\n        >\r\n          <image class=\"tn-swiper__item__image\" :src=\"item[name] || item\" :mode=\"imageMode\"></image>\r\n          <view\r\n            v-if=\"title && item[titleName]\"\r\n            class=\"tn-swiper__item__title tn-text-ellipsis\"\r\n            :style=\"[titleStyle]\">\r\n            {{ item[titleName] }}\r\n          </view>\r\n        </view>\r\n      </swiper-item>\r\n    </swiper>\r\n    \r\n    <!-- 指示点 -->\r\n    <view class=\"tn-swiper__indicator\" :style=\"[indicatorStyle]\">\r\n      <block v-if=\"mode === 'rect'\">\r\n        <view\r\n          v-for=\"(item, index) in list\"\r\n          :key=\"index\"\r\n          class=\"tn-swiper__indicator__rect\"\r\n          :class=\"{'tn-swiper__indicator__rect--active': swiperIndex === index}\"\r\n        ></view>\r\n      </block>\r\n      <block v-if=\"mode === 'dot'\">\r\n        <view\r\n          v-for=\"(item, index) in list\"\r\n          :key=\"index\"\r\n          class=\"tn-swiper__indicator__dot\"\r\n          :class=\"{'tn-swiper__indicator__dot--active': swiperIndex === index}\"\r\n        ></view>\r\n      </block>\r\n      <block v-if=\"mode === 'round'\">\r\n        <view\r\n          v-for=\"(item, index) in list\"\r\n          :key=\"index\"\r\n          class=\"tn-swiper__indicator__round\"\r\n          :class=\"{'tn-swiper__indicator__round--active': swiperIndex === index}\"\r\n        ></view>\r\n      </block>\r\n      <block v-if=\"mode === 'number'\">\r\n        <view class=\"tn-swiper__indicator__number\">{{ swiperIndex + 1 }}/{{ list.length }}</view>\r\n      </block>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'tn-swiper',\r\n    props: {\r\n      // 轮播图列表数据\r\n      // [{image: xxx.jpg, title: 'xxxx'}]\r\n      list: {\r\n        type: Array,\r\n        default() {\r\n          return []\r\n        }\r\n      },\r\n      // 初始化时，默认显示第几项\r\n      current: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // 高度\r\n      height: {\r\n        type: Number,\r\n        default: 250\r\n      },\r\n      // 背景颜色\r\n      backgroundColor: {\r\n        type: String,\r\n        default: 'transparent'\r\n      },\r\n      // 图片的属性名\r\n      name: {\r\n        type: String,\r\n        default: 'image'\r\n      },\r\n      // 是否显示标题\r\n      title: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 标题的属性名\r\n      titleName: {\r\n        type: String,\r\n        default: 'title'\r\n      },\r\n      // 用户自定义标题样式\r\n      titleStyle: {\r\n        type: Object,\r\n        default() {\r\n          return {}\r\n        }\r\n      },\r\n      // 圆角的值\r\n      radius: {\r\n        type: Number,\r\n        default: 8\r\n      },\r\n      // 指示器模式\r\n      // rect -> 方形 round -> 圆角方形 dot -> 点 number -> 轮播图下标\r\n      mode: {\r\n        type: String,\r\n        default: 'round'\r\n      },\r\n      // 指示器位置\r\n      // topLeft \\ topCenter \\ topRight \\ bottomLeft \\ bottomCenter \\ bottomRight\r\n      indicatorPosition: {\r\n        type: String,\r\n        default: 'bottomCenter'\r\n      },\r\n      // 开启3D缩放效果\r\n      effect3d: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 在3D缩放模式下，item之间的间隔\r\n      effect3dPreviousSpacing: {\r\n        type: Number,\r\n        default: 50\r\n      },\r\n      // 自定播放\r\n      autoplay: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 图片之间播放间隔多久\r\n      interval: {\r\n        type: Number,\r\n        default: 3000\r\n      },\r\n      // 轮播间隔时间\r\n      duration: {\r\n        type: Number,\r\n        default: 500\r\n      },\r\n      // 是否衔接滑动\r\n      circular: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 图片裁剪模式\r\n      imageMode: {\r\n        type: String,\r\n        default: 'aspectFill'\r\n      }\r\n    },\r\n    computed: {\r\n      backgroundColorStyle() {\r\n        return this.$tn.color.getBackgroundColorStyle(this.backgroundColor)\r\n      },\r\n      backgroundColorClass() {\r\n        return this.$tn.color.getBackgroundColorInternalClass(this.backgroundColor)\r\n      },\r\n      swiperStyle() {\r\n        let style = {}\r\n        if (this.backgroundColorStyle) {\r\n          style.backgroundColor = this.backgroundColorStyle\r\n        }\r\n        if (this.height) {\r\n          style.height = this.height + 'rpx'\r\n        }\r\n        return style\r\n      },\r\n      indicatorStyle() {\r\n        let style = {}\r\n        if (this.indicatorPosition === 'topLeft' || this.indicatorPosition === 'bottomLeft') style.justifyContent = 'flex-start'\r\n        if (this.indicatorPosition === 'topCenter' || this.indicatorPosition === 'bottomCenter') style.justifyContent =  'center'\r\n        if (this.indicatorPosition === 'topRight' || this.indicatorPosition === 'bottomRight') style.justifyContent =  'flex-end'\r\n        if (['topLeft','topCenter','topRight'].indexOf(this.indicatorPosition) >= 0) {\r\n          style.top = '12rpx'\r\n          style.bottom = 'auto'\r\n        } else {\r\n          style.top = 'auto'\r\n          style.bottom = '12rpx'\r\n        }\r\n        style.padding = `0 ${this.effect3d ? '74rpx' : '24rpx'}`\r\n        \r\n        return style\r\n      },\r\n      swiperTitleStyle() {\r\n        let style = {}\r\n        if (this.mode === 'none' || this.mode === '') style.paddingBottom = '12rpx'\r\n        if (['bottomLeft','bottomCenter','bottomRight'].indexOf(this.indicatorPosition) >= 0 && this.mode === 'number') {\r\n          style.paddingBottom = '60rpx'\r\n        } else if (['bottomLeft','bottomCenter','bottomRight'].indexOf(this.indicatorPosition) >= 0 && this.mode !== 'number') {\r\n          style.paddingBottom = '40rpx'\r\n        } else {\r\n          style.paddingBottom = '12rpx'\r\n        }\r\n        \r\n        style = Object.assign(style, this.titleStyle)\r\n        return style\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        // 当前显示的item的index\r\n        swiperIndex: this.current\r\n      }\r\n    },\r\n    watch: {\r\n      list(newVal, oldVal) {\r\n        // 如果修改了list的数据，重置current的值\r\n        if (newVal.length !== oldVal.length) this.swiperIndex = 0\r\n      },\r\n      current(value) {\r\n        // 监听外部current的变化，实时修改内部依赖于此测swiperIndex值，如果更新了current，而不是更新swiperIndex，就会错乱，因为指示器是依赖于swiperIndex的\r\n        this.swiperIndex = value\r\n      }\r\n    },\r\n    methods: {\r\n      click(index) {\r\n        this.$emit('click', index)\r\n      },\r\n      // 图片自动切换时触发\r\n      change(event) {\r\n        const current = event.detail.current\r\n        this.swiperIndex = current\r\n        this.$emit('change', current)\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  \r\n  .tn-swiper {\r\n    \r\n    &__wrap {\r\n      position: relative;\r\n      overflow: hidden;\r\n      transform: translateY(0);\r\n    }\r\n    \r\n    &__item {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      overflow: hidden;\r\n      \r\n      &__image {\r\n        width: 100%;\r\n        height: 100%;\r\n        will-change: transform;\r\n        display: block;\r\n        /* #ifdef H5 */\r\n        pointer-events: none;\r\n        /* #endif */\r\n        \r\n        &__wrap {\r\n          width: 100%;\r\n          height: 100%;\r\n          flex: 1;\r\n          transition: all 0.5s;\r\n          overflow: hidden;\r\n          box-sizing: content-box;\r\n          position: relative;\r\n        }\r\n        \r\n        &--scale {\r\n          transform-origin: center center;\r\n        }\r\n      }\r\n      \r\n      &__title {\r\n        width: 100%;\r\n        position: absolute;\r\n        background-color: rgba(0, 0, 0, 0.3);\r\n        bottom: 0;\r\n        left: 0;\r\n        font-size: 28rpx;\r\n        padding: 12rpx 24rpx;\r\n        color: rgba(255, 255, 255, 0.8);\r\n      }\r\n    }\r\n    \r\n    &__indicator {\r\n      padding: 0 24rpx;\r\n      position: absolute;\r\n      display: flex;\r\n      flex-direction: row;\r\n      width: 100%;\r\n      z-index: 1;\r\n      \r\n      &__rect {\r\n        width: 26rpx;\r\n        height: 8rpx;\r\n        background-color: rgba(0, 0, 0, 0.3);\r\n        transition: all 0.5s;\r\n        \r\n        &--active {\r\n          background-color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n      \r\n      &__dot {\r\n        width: 14rpx;\r\n        height: 14rpx;\r\n        margin: 0 6rpx;\r\n        border-radius: 20rpx;\r\n        background-color: rgba(0, 0, 0, 0.3);\r\n        transition: all 0.5s;\r\n        \r\n        &--active {\r\n          background-color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n      \r\n      &__round {\r\n        width: 14rpx;\r\n        height: 14rpx;\r\n        margin: 0 6rpx;\r\n        border-radius: 20rpx;\r\n        background-color: rgba(0, 0, 0, 0.3);\r\n        transition: all 0.5s;\r\n        \r\n        &--active {\r\n          width: 34rpx;\r\n          background-color: rgba(255, 255, 255, 0.8);\r\n        }\r\n      }\r\n      \r\n      &__number {\r\n        padding: 6rpx 16rpx;\r\n        line-height: 1;\r\n        background-color: rgba(0, 0, 0, 0.3);\r\n        color: rgba(255, 255, 255, 0.8);\r\n        border-radius: 100rpx;\r\n        font-size: 26rpx;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-swiper.vue?vue&type=style&index=0&id=4ba19b58&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-swiper.vue?vue&type=style&index=0&id=4ba19b58&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980404886\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}