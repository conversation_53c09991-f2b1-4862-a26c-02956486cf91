<template>
	<view class="answer_warp tn-width-full">
		<view class="answer_mask" v-if="mask" @click.stop="mask=!mask"></view>
		<view class="answer_mask_title" v-if="mask" @click.stop="mask=!mask">点击空白处可查看选项</view>
		<view v-for="(item,index) in 4" :key="index" class="answer_item tn-flex tn-flex-col-center tn-flex-row-between"
			:class="index==1?'suc_bg':index==3?'err_bg':'nom_bg'">
			<view :class="index==1?'suc_word':'tn-width-full'">
				A.选择答案选择答案选择答案选择答案选择答案选择答案选择答案选择答案选择答案
			</view>
			<image src="../../static/icon/suc_sel.png" mode="widthFix" style="width: 44rpx;" v-if="index==1">
			</image>
		</view>
	</view>
</template>

<script>
	export default {
		name: "answer_com",
		data() {
			return {
				mask: true
			};
		}
	}
</script>

<style lang="scss" scoped>
	.answer_mask {
		position: absolute;
		top: 0rpx;
		bottom: 0rpx;
		left: 0rpx;
		right: 0rpx;
		background: linear-gradient(180deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.05) 100%);
		backdrop-filter: blur(10rpx);
		z-index: 100;
	}

	.answer_mask_title {
		position: absolute;
		top: 0rpx;
		bottom: 0rpx;
		left: 0rpx;
		right: 0rpx;
		z-index: 120;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #333333;
	}

	.answer_warp {
		position: relative;
	}

	.suc_word {
		width: 550rpx;
	}

	.nom_bg {
		background-color: #F7F7F7;
	}

	.err_bg {
		background-color: #FFD8D9;
	}

	.suc_bg {
		background-color: #DDFFF5;
	}

	.answer_item {
		width: 690rpx;
		border-radius: 20rpx;
		padding: 40rpx;
		box-sizing: border-box;
		margin-bottom: 40rpx;
		color: #333333;
		font-size: 28rpx;
	}
</style>