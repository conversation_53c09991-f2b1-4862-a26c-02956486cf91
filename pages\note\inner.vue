<template>
	<view>
		<view class="tn-width-full head">
			{{selItem.chapterTitle||''}}
		</view>
		<view class="tn-width-full note_model" v-if="noteList.chapter_list && noteList.chapter_list.length>0">
			<view class="tn-width-full tn-flex tn-flex-col-center note_top">
				<view class="line l_b"></view>
				<view class="">
					章笔记
				</view>
			</view>
			<view v-for="(item,index) in noteList.chapter_list" :key="index" class="note_item">
				<view class="tn-width-full" style="font-size: 28rpx;color: #333333;word-break: break-all;">
					{{item.content}}
				</view>
				<view class="tn-width-full" style="font-size: 24rpx;color: #666666;margin-top: 48rpx;">
					{{item.create_time}}
				</view>
			</view>
		</view>
		<view class="tn-width-full note_model" v-if="noteList.list && noteList.list.length>0">
			<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between note_top">
				<view class="tn-flex tn-flex-col-center">
					<view class="line l_r"></view>
					<view class="">
						题目笔记
					</view>
				</view>
				<view class="" style="font-size: 28rpx;color: #666666;">
					共{{noteList.total}}个错题笔记
				</view>
			</view>
			<view v-for="(item,index) in noteList.list" :key="index" class="note_item note_item2"
				@click.stop="toNoteQuest(item)">
				<view class="note_item_top tn-flex tn-flex-col-center">
					<view class="ord">
						题号{{item.topic_title_id}}
					</view>
					<view class="total">
						共 {{item.num}} 个错题笔记
					</view>
				</view>
				<view class="tn-width-full" style="font-size: 28rpx;color: #333333;word-break: break-all;">
					{{item.last_content}}
				</view>
				<view class="tn-width-full tn-width-full tn-flex tn-flex-col-center tn-flex-row-between"
					style="font-size: 24rpx;color: #666666;margin-top: 48rpx;">
					<view class="">
						{{item.create_time}}
					</view>
					<view class="tn-icon-right"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import shareMixin from "@/mixins/shareMixin.js"
	export default {
		mixins: [shareMixin],
		data() {
			return {
				noteList: {}
			};
		},
		onLoad(options) {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			if (options.item) {
				this.selItem = JSON.parse(options.item)
				this.getNote()
			}
		},

		methods: {
			toNoteQuest(item){
				this.$publicjs.toUrl("/pages/note/quest?item="+JSON.stringify(item))
			},
			getNote() {
				this.$http.post(this.$api.listBookNoteDetail, {
					chapter_id: this.selItem.chapter_id
				}).then(res => {
					if (res.code == 200) {
						this.noteList = res.data
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.total {
		margin-left: 20rpx;
		font-size: 24rpx;
		color: #999999;
	}

	.ord {
		width: 114rpx;
		height: 52rpx;
		border-radius: 20rpx 0rpx 20rpx 0rpx;
		background-color: #5552FF;
		text-align: center;
		line-height: 52rpx;
		color: #FFFFFF;
		font-size: 28rpx;
	}

	.note_item_top {
		width: 690rpx;
		position: absolute;
		top: 0rpx;
		left: 0rpx;
		right: 0rpx;
	}

	.note_item2 {
		position: relative;
		padding-top: 80rpx !important;
	}

	.note_item {
		width: 690rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		box-sizing: border-box;
		background-color: #FFFFFF;
		margin-top: 20rpx;
		overflow: hidden;
	}

	.line {
		width: 8rpx;
		height: 32rpx;
		border-radius: 4rpx;
		margin-right: 10rpx;
	}

	.l_b {
		background-color: #5552FF;
	}

	.l_r {
		background-color: #FF585F;
	}

	.note_top {
		font-size: 32rpx;
		color: #222222;
	}

	.note_model {
		padding: 30rpx;
		box-sizing: border-box;
	}

	.head {
		background-color: #FFFFFF;
		padding: 20rpx 40rpx;
		font-size: 38rpx;
		color: #222222;
		font-weight: bold;
	}
</style>