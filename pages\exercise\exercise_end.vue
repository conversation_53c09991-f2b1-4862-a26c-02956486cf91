<template>
	<view style="padding-bottom: 150rpx;">
		<view class="header">
			<view class="header_top tn-flex tn-flex-direction-column tn-flex-row-between">
				<view class="tn-width-full">
					<view class="tn-width-full" style="font-size: 26rpx;color: #666666;">{{resDetail.template_name}}
					</view>
					<view class="tn-width-full tn-text-bold"
						style="margin-top: 10rpx; font-size: 30rpx;color: #333333;">{{resDetail.title}}</view>
				</view>
				<view class="tn-width-full tn-flex tn-flex-row-between">
					<view class="tn-flex tn-flex-direction-column tn-flex-col-top">
						<view class="title">
							用时(min)
						</view>
						<view class="num">
							{{resDetail.use_time>0?$publicjs.timeMin(resDetail.use_time):0}}
						</view>
					</view>
					<view class="tn-flex tn-flex-direction-column tn-flex-col-top">
						<view class="title">
							正确率
						</view>
						<view class="num">
							{{resDetail.correct_ratio||0}}%
						</view>
					</view>
					<view class="tn-flex tn-flex-direction-column tn-flex-col-top">
						<view class="title">
							平均正确率
						</view>
						<view class="num">
							{{resDetail.avg_correct_ratio||0}}%
						</view>
					</view>
				</view>
			</view>
			<view class="header_bottom tn-flex tn-flex-direction-column tn-flex-row-between">
				<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between">
					<view class="">
						<text style="color: #666666;font-size: 26rpx;">正确率超过 <text
								style="color: #FF000A;font-weight: bold;">{{resDetail.than_other||0}}</text>
							%的研友</text>
					</view>
					<tn-circle-progress :percent="resDetail.than_other||0" :width="128" :borderWidth="12"
						activeColor="#5552FF" inactiveColor="#D8D8D8">
						<view style="color: #222222;font-size: 20rpx;">{{resDetail.than_other||0}}%</view>
					</tn-circle-progress>
				</view>
				<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-around">
					<view class="h_btn1" @click.stop="toAnalysis('1')">
						全部解析
					</view>
					<view class="h_btn2" @click.stop="toAnalysis('2')">
						错题研究
					</view>
				</view>
			</view>
		</view>
		<view class="card_warp">
			<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between">
				<view class="tn-flex tn-flex-col-center">
					<text class="tn-text-bold" style="font-size: 30rpx;color: #333333;">答题卡</text>
					<!-- <view style="margin-left: 24rpx;font-size: 24rpx;color: #999999;"
						class="tn-flex tn-flex-col-center"> <text class="tn-icon-star-fill"
							style="color: #FFBD23;font-size: 32rpx;"></text> 为重点题</view> -->
				</view>
				<view class="tn-flex tn-flex-col-center" style="font-size: 24rpx;color: #333333;">
					<view class="tn-flex tn-flex-col-center" style="margin-right: 55rpx;">
						<view
							style="margin-right: 8rpx; width: 14rpx;height: 14rpx;border-radius: 50%; border: 2rpx solid #5552FF;">
						</view>
						正确
					</view>
					<view class="tn-flex tn-flex-col-center">
						<view
							style="margin-right: 8rpx; width: 14rpx;height: 14rpx;border-radius: 50%; border: 2rpx solid #FF585F;">
						</view>
						错误
					</view>
				</view>
			</view>
			<view class="tn-width-full tn-flex tn-flex-wrap tn-flex-row-between"
				v-if="resDetail&&resDetail.topic_record&&resDetail.topic_record.length>0">
				<view v-for="(item,index) in resDetail.topic_record" :key="index"
					:class="item.result==1?'quest_suc':'quest_err'" class="quest_or"
					@click.stop="toAnalysis('1',item.topic_id)">
					{{item.title_id}}
				</view>
				<view v-for="(item,index) in 5-resDetail.topic_record.length%5"
					style="width: 100rpx;margin-left: 10rpx;margin-right: 10rpx;" :key="'00'+index">
				</view>
			</view>
		</view>
		<view class="footer_model tn-flex tn-flex-col-center tn-flex-row-between">
			<view class="f_btn1" @click.stop="backTwo()">
				返回列表
			</view>
			<view class="f_btn2" @click.stop="showAdd=true">
				章笔记
			</view>
		</view>
		<tn-popup v-model="showAdd" mode="bottom" :borderRadius="40" safeAreaInsetBottom>
			<view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				<view style="font-size: 34rpx;">
					章笔记
				</view>
			</view>
			<view class="scroll_warp2 tn-width-full">
				<tn-input v-model="note_value" placeholder="开始输入..." :clearable="false" type="textarea" :border="false"
					:height="324" :autoHeight="false" />
			</view>
			<view class="tn-flex tn-flex-col-top tn-flex-row-center" style="height: 150rpx;">
				<view class="submit" @click.stop="submitNote()">保存</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				resId: null,
				resDetail: {},
				showAdd: false,
				note_value: ""
			};
		},
		onLoad(options) {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			if (options.chapter_id) {
				this.resId = options.chapter_id
				this.getRes()
			}
		},

		methods: {
			backTwo() {
				let pages = getCurrentPages();
				let prevPage = pages[pages.length - 3];
				prevPage.$vm.getChapterList();
				uni.navigateBack({
					delta: 2
				})
			},
			toAnalysis(type, num) {
				if (type == 1) {
					if (!num) {
						this.$publicjs.toUrl("/pages/analysis/index?id=" + this.resId)
					} else {
						this.$publicjs.toUrl("/pages/analysis/index?id=" + this.resId + "&topicId=" + num)
					}
				}
				if (type == 2) {
					if (this.resDetail.correct_ratio != 100) {
						this.$publicjs.toUrl("/pages/analysis/error?id=" + this.resId)
					} else {
						uni.showToast({
							title: "暂无错题",
							icon: "none"
						})
					}
				}
			},
			submitNote() {
				this.$http.post(this.$api.addNote, {
					chapter_id: this.resId,
					type: 1,
					content: this.note_value
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							icon: "success",
							title: "添加成功"
						})
						this.showAdd = false
					}
				})
			},
			getRes() {
				this.$http.post(this.$api.answerRes, {
					chapter_id: this.resId
				}).then(res => {
					if (res.code == 200) {
						this.resDetail = res.data
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.scroll_warp2 {
		width: 690rpx;
		height: 384rpx;
		border-radius: 20rpx;
		background-color: #F8F8F8;
		margin: 0rpx auto;
		margin-bottom: 26rpx;
		padding: 30rpx;
		box-sizing: border-box;
	}

	.submit {
		width: 630rpx;
		height: 96rpx;
		border-radius: 48rpx;
		background-color: #7270FF;
		font-size: 16px;
		color: #FFFFFF;
		text-align: center;
		line-height: 96rpx;
	}

	.quest_suc {
		background-color: #5552FF;
	}

	.quest_err {
		background-color: #FF585F;
	}

	.f_btn1 {
		width: 330rpx;
		height: 80rpx;
		border-radius: 55rpx;
		box-sizing: border-box;
		border: 2rpx solid #3775F6;
		text-align: center;
		line-height: 80rpx;
		color: #5552FF;
		font-size: 32rpx;
	}

	.f_btn2 {
		width: 330rpx;
		height: 80rpx;
		border-radius: 55rpx;
		box-sizing: border-box;
		border: 2rpx solid #5552FF;
		background-color: #5552FF;
		text-align: center;
		line-height: 80rpx;
		color: #FFFFFF;
		font-size: 32rpx;
	}

	.footer_model {
		width: 750rpx;
		height: 120rpx;
		background-color: #FFFFFF;
		position: fixed;
		bottom: 0rpx;
		left: 0rpx;
		right: 0rpx;
		padding: 0rpx 30rpx;
	}

	.quest_or {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		color: #FFFFFF;
		font-size: 32rpx;
		line-height: 100rpx;
		text-align: center;
		margin-top: 40rpx;
		margin-left: 10rpx;
		margin-right: 10rpx;
	}

	.h_btn1 {
		width: 286rpx;
		height: 96rpx;
		border-radius: 50rpx;
		background-color: #F1F0FF;
		text-align: center;
		line-height: 96rpx;
		color: #7270FF;
		font-size: 32rpx;
	}

	.h_btn2 {
		width: 286rpx;
		height: 96rpx;
		border-radius: 50rpx;
		background-color: #7270FF;
		text-align: center;
		line-height: 96rpx;
		color: #FFFFFF;
		font-size: 32rpx;
	}

	.title {
		font-size: 26rpx;
		color: #666666;
	}

	.num {
		color: #333333;
		font-size: 48rpx;
		font-weight: bold;
		margin-top: 25rpx;
	}

	.card_warp {
		width: 750rpx;
		background-color: #FFFFFF;
		padding: 30rpx;
		border-radius: 20rpx;
		box-sizing: border-box;
	}

	.header_bottom {
		width: 690rpx;
		height: 320rpx;
		border-radius: 10rpx;
		background-color: #FFFFFF;
		margin-top: 4rpx;
		padding: 30rpx;
		box-sizing: border-box;
	}

	.header_top {
		width: 690rpx;
		height: 320rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		padding: 30rpx;
		box-sizing: border-box;
	}

	.header {
		width: 750rpx;
		background: linear-gradient(to bottom, #FFE8E4, #FFE8E4) no-repeat;
		background-size: 100% 274rpx;
		padding: 20rpx 30rpx;
		box-sizing: border-box;
	}
</style>