<template>
	<view class="content">
		<view class="header">
			<text class="title">分享功能测试页面</text>
		</view>
		
		<view class="test-section">
			<view class="section-title">API测试</view>
			<view class="test-item">
				<text class="label">分享图片URL:</text>
				<text class="value">{{ shareImageUrl || '加载中...' }}</text>
			</view>
			<view class="test-item">
				<text class="label">API状态:</text>
				<text class="value" :class="apiStatus">{{ apiStatusText }}</text>
			</view>
			<view class="test-item">
				<text class="label">缓存状态:</text>
				<text class="value">{{ cacheStatus }}</text>
			</view>
		</view>

		<view class="test-section">
			<view class="section-title">分享配置</view>
			<view class="test-item">
				<text class="label">分享标题:</text>
				<text class="value">{{ shareTitle }}</text>
			</view>
			<view class="test-item">
				<text class="label">分享路径:</text>
				<text class="value">{{ sharePath }}</text>
			</view>
		</view>

		<view class="button-section">
			<button class="test-btn" @click="testAPI">测试API</button>
			<button class="test-btn" @click="clearCache">清除缓存</button>
			<button class="test-btn" @click="preloadImage">预加载图片</button>
			<button class="test-btn" open-type="share">测试分享</button>
		</view>

		<view class="log-section">
			<view class="section-title">测试日志</view>
			<scroll-view class="log-content" scroll-y>
				<view v-for="(log, index) in logs" :key="index" class="log-item">
					<text class="log-time">{{ log.time }}</text>
					<text class="log-text">{{ log.text }}</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import shareMixin from "@/mixins/shareMixin.js"
	import shareHelper from "@/utils/shareHelper.js"
	
	export default {
		mixins: [shareMixin],
		data() {
			return {
				apiStatusText: '未测试',
				apiStatus: 'pending',
				cacheStatus: '无缓存',
				logs: []
			}
		},
		
		onLoad() {
			this.addLog('页面加载完成')
			this.testAPI()
		},
		
		methods: {
			async testAPI() {
				this.addLog('开始测试API...')
				this.apiStatusText = '测试中...'
				this.apiStatus = 'testing'
				
				try {
					const startTime = Date.now()
					const imageUrl = await shareHelper.getShareImage()
					const endTime = Date.now()
					
					this.shareImageUrl = imageUrl
					this.apiStatusText = `成功 (${endTime - startTime}ms)`
					this.apiStatus = 'success'
					this.updateCacheStatus()
					this.addLog(`API测试成功: ${imageUrl}`)
				} catch (error) {
					this.apiStatusText = '失败'
					this.apiStatus = 'error'
					this.addLog(`API测试失败: ${error.message}`)
				}
			},
			
			clearCache() {
				shareHelper.clearCache()
				this.updateCacheStatus()
				this.addLog('缓存已清除')
			},
			
			async preloadImage() {
				this.addLog('开始预加载图片...')
				try {
					await shareHelper.preloadShareImage()
					this.addLog('预加载完成')
					this.updateCacheStatus()
				} catch (error) {
					this.addLog(`预加载失败: ${error.message}`)
				}
			},
			
			updateCacheStatus() {
				if (shareHelper.isValidCache()) {
					this.cacheStatus = '有效缓存'
				} else {
					this.cacheStatus = '无缓存'
				}
			},
			
			addLog(text) {
				const now = new Date()
				const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
				this.logs.unshift({
					time,
					text
				})
				
				// 限制日志数量
				if (this.logs.length > 50) {
					this.logs = this.logs.slice(0, 50)
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		padding: 20rpx;
		background-color: #f7f7f7;
		min-height: 100vh;
	}
	
	.header {
		text-align: center;
		padding: 40rpx 0;
		
		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
	}
	
	.test-section {
		background-color: white;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		
		.section-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 20rpx;
		}
		
		.test-item {
			display: flex;
			margin-bottom: 15rpx;
			
			.label {
				width: 200rpx;
				font-size: 28rpx;
				color: #666;
			}
			
			.value {
				flex: 1;
				font-size: 28rpx;
				color: #333;
				word-break: break-all;
				
				&.success {
					color: #4CAF50;
				}
				
				&.error {
					color: #F44336;
				}
				
				&.testing {
					color: #FF9800;
				}
			}
		}
	}
	
	.button-section {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		margin-bottom: 20rpx;
		
		.test-btn {
			flex: 1;
			min-width: 160rpx;
			height: 80rpx;
			line-height: 80rpx;
			background-color: #3775F6;
			color: white;
			border-radius: 40rpx;
			font-size: 28rpx;
			border: none;
		}
	}
	
	.log-section {
		background-color: white;
		border-radius: 20rpx;
		padding: 30rpx;
		height: 400rpx;
		
		.section-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 20rpx;
		}
		
		.log-content {
			height: 300rpx;
			
			.log-item {
				display: flex;
				margin-bottom: 10rpx;
				
				.log-time {
					width: 120rpx;
					font-size: 24rpx;
					color: #999;
				}
				
				.log-text {
					flex: 1;
					font-size: 26rpx;
					color: #333;
				}
			}
		}
	}
</style>
