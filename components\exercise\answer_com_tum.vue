<template>
	<view class="answer_warp tn-width-full">
		<view v-for="(item,index) in allData" :key="index"
			class="answer_item tn-flex tn-flex-col-center tn-flex-row-between"
			:class="suc_index.indexOf(index)>=0?'suc_bg':sel_index.indexOf(index)>=0?'err_bg':'nom_bg'"
			@click.stop="selAns(index)">
			<view :class="suc_index.indexOf(index)>=0?'suc_word':'tn-width-full'">
				{{item.option}}
			</view>
			<image src="../../static/icon/suc_sel.png" mode="widthFix" style="width: 44rpx;height: 44rpx;"
				v-if="suc_index.indexOf(index)>=0">
			</image>
		</view>
	</view>
</template>

<script>
	export default {
		name: "answer_com_tum",
		data() {
			return {
				sel_index: [],
				suc_index: [],
				item: {},
				allData: []
			};
		},
		methods: {
			changeData(item, answer, isAns = false) {
				this.item = item
				this.allData = item.option
				this.sel_index = []
				this.suc_index = []
				if (answer[item.id] && answer[item.id].length > 0) {
					this.sel_index = answer[item.id].map(item => {
						return item - 1
					})
				} else {
					this.sel_index = []
				}
				if (isAns) {
					this.suc_index = item.solution.map(item => {
						return item -= 1
					})
				}
			},
			selAns(index) {
				if (this.sel_index.indexOf(index) >= 0) {
					let data = JSON.parse(JSON.stringify(this.sel_index))
					this.sel_index = data.filter(item => {
						return item != index
					})
				} else {
					this.sel_index.push(index)
				}
				if (this.sel_index.length > 0) {
					let data = JSON.parse(JSON.stringify(this.sel_index))
					let arr = data.map(item => {
						return item + 1
					})
					this.$emit('selAnswer', this.item, arr)
				} else {
					this.$emit('selAnswer', this.item, null)
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.answer_warp {}

	.suc_word {
		width: 550rpx;
	}

	.nom_bg {
		background-color: #F7F7F7;
	}

	.err_bg {
		background-color: #fff6e4;
	}

	.suc_bg {
		background-color: #DDFFF5;
	}

	.answer_item {
		width: 690rpx;
		border-radius: 20rpx;
		padding: 40rpx;
		box-sizing: border-box;
		margin-bottom: 40rpx;
		color: #333333;
		font-size: 28rpx;
	}
</style>