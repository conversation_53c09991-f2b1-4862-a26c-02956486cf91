{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-number-box/tn-number-box.vue?5841", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-number-box/tn-number-box.vue?9492", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-number-box/tn-number-box.vue?aadd", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-number-box/tn-number-box.vue?93bc", "uni-app:///tuniao-ui/components/tn-number-box/tn-number-box.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-number-box/tn-number-box.vue?a4fd", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-number-box/tn-number-box.vue?b8c2"], "names": ["mixins", "name", "props", "value", "type", "default", "index", "min", "max", "step", "disabled", "disabledInput", "inputWidth", "inputHeight", "cursorSpacing", "longPress", "longPressTime", "positiveInteger", "computed", "getCursorSpacing", "data", "inputValue", "longPressTimer", "changeFromInner", "innerChangeTimer", "watch", "created", "methods", "touchStart", "clearInterval", "clearTimer", "minus", "plus", "calcPlus", "baseNum1", "baseNum2", "baseNum", "calcMinus", "computeValue", "uni", "blurInput", "val", "focusInput", "updateInputValue", "handleChange", "clearTimeout"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvBA;AAAA;AAAA;AAAA;AAAynB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkE7oB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;EACA;EACAa;IACAC;MACA;IACA;EACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACAtB;MAAA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAkB;MAAA;MACA;MACA;MACA;MACA;MACA;MACA,8EACAlB;;MAEA;MACA;QACA;QACA;UACAA;UACA;UACA;YACA;UACA;QACA;MACA;MACA;IACA;IACAI;MACA;IACA;IACAC;MACA;IACA;EACA;EACAkB;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;QACA;MACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACAD;UACA;QACA;MACA;IACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QAAAC;QAAAC;MACA;QACAD;MACA;QACAA;MACA;MACA;QACAC;MACA;QACAA;MACA;MAEAC;MACA;MACA;MACA;IACA;IACAC;MACA;QAAAH;QAAAC;MACA;QACAD;MACA;QACAA;MACA;MACA;QACAC;MACA;QACAA;MACA;MAEAC;MACA;MACA;MACA;IACA;IACA;IACAE;MACAC;MACA;MACA;MAEA;QACA;QACApC;MACA;QACA;QACAA;MACA;MACA;MACA;MAEA;MACA;IACA;IACA;IACAqC;MAAA;MACA;QACArC;MACA;MACA;MACA;QACAsC;MACA;QACAA;MACA;MAEA;QACAA;MACA;QACAA;MACA;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACAxC;MACA;QACAA;MACA;MAEA;IACA;IACA;IACAyC;MAAA;MACA;MACA;MACA;QACAC;QACA;MACA;;MAEA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA1C;QACAG;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7VA;AAAA;AAAA;AAAA;AAAwsC,CAAgB,8nCAAG,EAAC,C;;;;;;;;;;;ACA5tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-number-box/tn-number-box.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-number-box.vue?vue&type=template&id=61aeb8b2&scoped=true&\"\nvar renderjs\nimport script from \"./tn-number-box.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-number-box.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-number-box.vue?vue&type=style&index=0&id=61aeb8b2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"61aeb8b2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-number-box/tn-number-box.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-number-box.vue?vue&type=template&id=61aeb8b2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.$tn.string.getLengthUnitValue(_vm.inputHeight)\n  var g1 = _vm.$tn.string.getLengthUnitValue(_vm.inputWidth)\n  var g2 = _vm.$tn.string.getLengthUnitValue(_vm.inputHeight)\n  var g3 = _vm.$tn.string.getLengthUnitValue(_vm.inputHeight)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-number-box.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-number-box.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"tn-number-box-class tn-number-box\">\r\n    <!-- 减 -->\r\n    <view\r\n      class=\"tn-number-box__btn__minus\"\r\n      :class=\"[\r\n        backgroundColorClass,\r\n        fontColorClass,\r\n        {'tn-number-box__btn--disabled': disabled || inputValue <= min}\r\n      ]\"\r\n      :style=\"{\r\n        backgroundColor: backgroundColorStyle,\r\n        height: $tn.string.getLengthUnitValue(inputHeight),\r\n        color: fontColorStyle,\r\n        fontSize: fontSizeStyle\r\n      }\"\r\n      @touchstart.stop.prevent=\"touchStart('minus')\"\r\n      @touchend.stop.prevent=\"clearTimer\"\r\n    >\r\n      <view class=\"tn-icon-reduce\"></view>\r\n    </view>\r\n    \r\n    <!-- 输入框 -->\r\n    <input\r\n      v-model=\"inputValue\"\r\n      :disabled=\"disabledInput || disabled\"\r\n      :cursor-spacing=\"getCursorSpacing\"\r\n      class=\"tn-number-box__input\"\r\n      :class=\"[\r\n        fontColorClass,\r\n        {'tn-number-box__input--disabled': disabledInput || disabled}\r\n      ]\"\r\n      :style=\"{\r\n        width: $tn.string.getLengthUnitValue(inputWidth),\r\n        height: $tn.string.getLengthUnitValue(inputHeight),\r\n        color: fontColorStyle,\r\n        fontSize: fontSizeStyle,\r\n        backgroundColor: backgroundColorStyle\r\n      }\"\r\n      @blur=\"blurInput\"\r\n      @focus=\"focusInput\"\r\n    />\r\n    \r\n    <!-- 加 -->\r\n    <view\r\n      class=\"tn-number-box__btn__plus\"\r\n      :class=\"[\r\n        backgroundColorClass,\r\n        fontColorClass,\r\n        {'tn-number-box__btn--disabled': disabled || inputValue >= max}\r\n      ]\"\r\n      :style=\"{\r\n        backgroundColor: backgroundColorStyle,\r\n        height: $tn.string.getLengthUnitValue(inputHeight),\r\n        color: fontColorStyle,\r\n        fontSize: fontSizeStyle\r\n      }\"\r\n      @touchstart.stop.prevent=\"touchStart('plus')\"\r\n      @touchend.stop.prevent=\"clearTimer\"\r\n    >\r\n      <view class=\"tn-icon-add\"></view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import componentsColor from '../../libs/mixin/components_color.js'\r\n  \r\n  export default {\r\n    mixins: [componentsColor],\r\n    name: 'tn-number-box',\r\n    props: {\r\n      value: {\r\n        type: Number,\r\n        default: 1\r\n      },\r\n      // 索引\r\n      index: {\r\n        type: [Number, String],\r\n        default: ''\r\n      },\r\n      // 最小值\r\n      min: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // 最大值\r\n      max: {\r\n        type: Number,\r\n        default: 99999\r\n      },\r\n      // 步进值\r\n      step: {\r\n        type: Number,\r\n        default: 1\r\n      },\r\n      // 禁用\r\n      disabled: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 是否禁用输入\r\n      disabledInput: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 输入框的宽度\r\n      inputWidth: {\r\n        type: Number,\r\n        default: 88\r\n      },\r\n      // 输入框的高度\r\n      inputHeight: {\r\n        type: Number,\r\n        default: 50\r\n      },\r\n      // 输入框和键盘之间的距离\r\n      cursorSpacing: {\r\n        type: Number,\r\n        default: 100\r\n      },\r\n      // 是否开启长按进行连续递增减\r\n      longPress: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 长按触发间隔\r\n      longPressTime: {\r\n        type: Number,\r\n        default: 250\r\n      },\r\n      // 是否只能输入正整数\r\n      positiveInteger: {\r\n        type: Boolean,\r\n        default: true\r\n      }\r\n    },\r\n    computed: {\r\n      getCursorSpacing() {\r\n        return Number(uni.upx2px(this.cursorSpacing))\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        // 输入框的值\r\n        inputValue: 1,\r\n        // 长按定时器\r\n        longPressTimer: null,\r\n        // 标记值的改变是来自外部还是内部\r\n        changeFromInner: false,\r\n        // 内部定时器\r\n        innerChangeTimer: null\r\n      }\r\n    },\r\n    watch: {\r\n      value(val) {\r\n        // 只有value的改变是来自外部的时候，才去同步inputValue的值，否则会造成循环错误\r\n        if (!this.changeFromInner) {\r\n          this.updateInputValue()\r\n          // 因为inputValue变化后，会触发this.handleChange()，在其中changeFromInner会再次被设置为true，\r\n          // 造成外面修改值，也导致被认为是内部修改的混乱，这里进行this.$nextTick延时，保证在运行周期的最后处\r\n          // 将changeFromInner设置为false\r\n          this.$nextTick(() => {\r\n          \tthis.changeFromInner = false\r\n          })\r\n        }\r\n      },\r\n      inputValue(newVal, oldVal) {\r\n        // 为了让用户能够删除所有输入值，重新输入内容，删除所有值后，内容为空字符串\r\n        if (newVal === '') return\r\n        let value = 0\r\n        // 首先判断是否数值，并且在min和max之间，如果不是，使用原来值\r\n        let isNumber = this.$tn.test.number(newVal)\r\n        if (isNumber && newVal >= this.min && newVal <= this.max) value = newVal\r\n        else value = oldVal\r\n        \r\n        // 判断是否只能输入大于等于0的整数\r\n        if (this.positiveInteger) {\r\n          // 小于0或者带有小数点\r\n          if (newVal < 0 || String(newVal).indexOf('.') !== -1) {\r\n            value = Math.floor(newVal)\r\n            // 双向绑定input的值，必须要使用$nextTick修改显示的值\r\n            this.$nextTick(() => {\r\n            \tthis.inputValue = value\r\n            })\r\n          }\r\n        }\r\n        this.handleChange(value, 'change')\r\n      },\r\n      min() {\r\n        this.updateInputValue()\r\n      },\r\n      max() {\r\n        this.updateInputValue()\r\n      }\r\n    },\r\n    created() {\r\n      this.updateInputValue()\r\n    },\r\n    methods: {\r\n      // 开始点击按钮\r\n      touchStart(func) {\r\n        // 先执行一遍方法，否则会造成松开手时，就执行了clearTimer，导致无法实现功能\r\n        this[func]()\r\n        // 如果没有开启长按功能，直接返回\r\n        if (!this.longPress) return\r\n        // 清空长按定时器，防止重复注册\r\n        if (this.longPressTimer) {\r\n          clearInterval(this.longPressTimer)\r\n          this.longPressTimer = null\r\n        }\r\n        this.longPressTimer = setInterval(() => {\r\n          // 执行加减操作\r\n          this[func]()\r\n        }, this.longPressTime)\r\n      },\r\n      // 清除定时器\r\n      clearTimer() {\r\n        this.$nextTick(() => {\r\n          if (this.longPressTimer) {\r\n            clearInterval(this.longPressTimer)\r\n            this.longPressTimer = null\r\n          }\r\n        })\r\n      },\r\n      // 减\r\n      minus() {\r\n        this.computeValue('minus')\r\n      },\r\n      // 加\r\n      plus() {\r\n        this.computeValue('plus')\r\n      },\r\n      // 处理小数相加减出现溢出问题\r\n      calcPlus(num1, num2) {\r\n        let baseNum = 0, baseNum1 = 0, baseNum2 = 0\r\n        try {\r\n          baseNum1 = num1.toString().split('.')[1].length\r\n        } catch(e) {\r\n          baseNum1 = 0\r\n        }\r\n        try {\r\n          baseNum2 = num2.toString().split('.')[1].length\r\n        } catch(e) {\r\n          baseNum2 = 0\r\n        }\r\n        \r\n        baseNum = Math.pow(10, Math.max(baseNum1, baseNum2))\r\n        // 精度\r\n        let precision = baseNum1 >= baseNum2 ? baseNum1 : baseNum2\r\n        return ((num1 * baseNum + num2 * baseNum) / baseNum).toFixed(precision)\r\n      },\r\n      calcMinus(num1, num2) {\r\n        let baseNum = 0, baseNum1 = 0, baseNum2 = 0\r\n        try {\r\n          baseNum1 = num1.toString().split('.')[1].length\r\n        } catch(e) {\r\n          baseNum1 = 0\r\n        }\r\n        try {\r\n          baseNum2 = num2.toString().split('.')[1].length\r\n        } catch(e) {\r\n          baseNum2 = 0\r\n        }\r\n        \r\n        baseNum = Math.pow(10, Math.max(baseNum1, baseNum2))\r\n        // 精度\r\n        let precision = baseNum1 >= baseNum2 ? baseNum1 : baseNum2\r\n        return ((num1 * baseNum - num2 * baseNum) / baseNum).toFixed(precision)\r\n      },\r\n      // 处理操作后的值\r\n      computeValue(type) {\r\n        uni.hideKeyboard()\r\n        if (this.disabled) return\r\n        let value = 0\r\n        \r\n        if (type === 'minus') {\r\n          // 减\r\n          value = this.calcMinus(this.inputValue, this.step)\r\n        } else if (type === 'plus') {\r\n          // 加\r\n          value = this.calcPlus(this.inputValue, this.step)\r\n        }\r\n        // 判断是否比最小值小和操作最大值\r\n        if (value < this.min || value > this.max) return\r\n        \r\n        this.inputValue = value\r\n        this.handleChange(value, type)\r\n      },\r\n      // 处理用户手动输入\r\n      blurInput(event) {\r\n        let val = 0,\r\n            value = event.detail.value\r\n        // 如果为非0-9数字组成，或者其第一位数值为0，直接让其等于min值\r\n        // 这里不直接判断是否正整数，是因为用户传递的props min值可能为0\r\n        if (!/(^\\d+$)/.test(value) || value[0] == 0) {\r\n          val = this.min\r\n        } else {\r\n          val = +value\r\n        }\r\n        \r\n        if (val > this.max) {\r\n          val = this.max\r\n        } else if (val < this.min) {\r\n          val = this.min\r\n        }\r\n        this.$nextTick(() => {\r\n          this.inputValue = val\r\n        })\r\n        this.handleChange(val, 'blur')\r\n      },\r\n      // 获取焦点\r\n      focusInput() {\r\n        this.$emit('focus')\r\n      },\r\n      // 初始化inputValue\r\n      updateInputValue() {\r\n        let value = this.value\r\n        if (value <= this.min) {\r\n          value = this.min\r\n        } else if (value >= this.max) {\r\n          value = this.max\r\n        }\r\n        \r\n        this.inputValue = Number(value)\r\n      },\r\n      // 处理值改变状态\r\n      handleChange(value, type) {\r\n        if (this.disabled) return\r\n        // 清除定时器，防止混乱\r\n        if (this.innerChangeTimer) {\r\n          clearTimeout(this.innerChangeTimer)\r\n          this.innerChangeTimer = null\r\n        }\r\n        \r\n        // 内部修改值\r\n        this.changeFromInner = true\r\n        // 一定时间内，清除changeFromInner标记，否则内部值改变后\r\n        // 外部通过程序修改value值，将会无效\r\n        this.innerChangeTimer = setTimeout(() => {\r\n          this.changeFromInner = false\r\n        }, 150)\r\n        this.$emit('input', Number(value))\r\n        this.$emit(type, {\r\n          value: Number(value),\r\n          index: this.index\r\n        })\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  \r\n  .tn-number-box {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    \r\n    &__btn {\r\n      &__plus,&__minus {\r\n        width: 60rpx;\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: center;\r\n        align-items: center;\r\n        background-color: $tn-font-holder-color;\r\n      }\r\n      \r\n      &__plus {\r\n        border-radius: 0 8rpx 8rpx 0;\r\n      }\r\n      \r\n      &__minus {\r\n        border-radius: 8rpx 0 0 8rpx;\r\n      }\r\n      \r\n      &--disabled {\r\n        color: $tn-font-sub-color !important;\r\n        background: $tn-font-holder-color !important;\r\n      }\r\n    }\r\n    \r\n    &__input {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      justify-content: center;\r\n      position: relative;\r\n      text-align: center;\r\n      box-sizing: border-box;\r\n      padding: 0 4rpx;\r\n      margin: 0 6rpx;\r\n      background-color: $tn-font-holder-color;\r\n      \r\n      &--disabled {\r\n        color: $tn-font-sub-color !important;\r\n        background: $tn-font-holder-color !important;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-number-box.vue?vue&type=style&index=0&id=61aeb8b2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-number-box.vue?vue&type=style&index=0&id=61aeb8b2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980405976\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}