@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.img_warp.data-v-057b20d8 {
  margin: 60rpx 0rpx 40rpx 0rpx;
  width: 300rpx;
  height: 300rpx;
  background-color: #333333;
}
.tips_word.data-v-057b20d8 {
  margin: 24rpx 0rpx;
  font-size: 24rpx;
  color: #333333;
}
.tips.data-v-057b20d8 {
  width: 256rpx;
  height: 64rpx;
  border-radius: 16rpx;
  background: linear-gradient(270deg, #FE593B 0%, #E14B56 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 32rpx;
}
.fab_btn.data-v-057b20d8 {
  width: 154rpx;
  position: fixed;
  right: 0rpx;
  bottom: 150rpx;
}
.content.data-v-057b20d8 {
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
}
.body.data-v-057b20d8 {
  margin-top: 20rpx;
}

