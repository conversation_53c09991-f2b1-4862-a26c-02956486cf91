<block wx:if="{{showNotice}}"><view class="tn-notice-bar-class tn-notice-bar data-v-b91dbbc0" style="{{'border-radius:'+(radius+'rpx')+';'}}"><block wx:if="{{mode==='horizontal'&&circular}}"><block class="data-v-b91dbbc0"><tn-row-notice vue-id="6a98ba34-1" backgroundColor="{{backgroundColor}}" fontColor="{{fontColor}}" fontSize="{{fontSize}}" fontUnit="{{fontUnit}}" list="{{valueList}}" show="{{show}}" playStatus="{{playStatus}}" leftIcon="{{leftIcon}}" leftIconName="{{leftIconName}}" leftIconSize="{{leftIconSize}}" rightIcon="{{rightIcon}}" rightIconName="{{rightIconName}}" rightIconSize="{{rightIconSize}}" closeBtn="{{closeBtn}}" autoplay="{{autoplay}}" radius="{{radius}}" padding="{{padding}}" speed="{{speed}}" data-event-opts="{{[['^click',[['click']]],['^close',[['close']]],['^clickLeft',[['clickLeftIcon']]],['^clickRight',[['clickRightIcon']]]]}}" bind:click="__e" bind:close="__e" bind:clickLeft="__e" bind:clickRight="__e" class="data-v-b91dbbc0" bind:__l="__l"></tn-row-notice></block></block><block wx:if="{{mode==='vertical'||mode==='horizontal'&&!circular}}"><block class="data-v-b91dbbc0"><tn-column-notice vue-id="6a98ba34-2" backgroundColor="{{backgroundColor}}" fontColor="{{fontColor}}" fontSize="{{fontSize}}" fontUnit="{{fontUnit}}" list="{{valueList}}" show="{{show}}" mode="{{mode}}" playStatus="{{playStatus}}" leftIcon="{{leftIcon}}" leftIconName="{{leftIconName}}" leftIconSize="{{leftIconSize}}" rightIcon="{{rightIcon}}" rightIconName="{{rightIconName}}" rightIconSize="{{rightIconSize}}" closeBtn="{{closeBtn}}" autoplay="{{autoplay}}" radius="{{radius}}" padding="{{padding}}" duration="{{duration}}" data-event-opts="{{[['^click',[['click']]],['^close',[['close']]],['^clickLeft',[['clickLeftIcon']]],['^clickRight',[['clickRightIcon']]],['^end',[['end']]]]}}" bind:click="__e" bind:close="__e" bind:clickLeft="__e" bind:clickRight="__e" bind:end="__e" class="data-v-b91dbbc0" bind:__l="__l"></tn-column-notice></block></block></view></block>