{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/project/shuati_new/pages/rember/recitation.vue?e6f1", "webpack:///D:/project/shuati_new/pages/rember/recitation.vue?ed6a", "webpack:///D:/project/shuati_new/pages/rember/recitation.vue?e030", "uni-app:///pages/rember/recitation.vue", "webpack:///D:/project/shuati_new/pages/rember/recitation.vue?e1b3", "webpack:///D:/project/shuati_new/pages/rember/recitation.vue?d75a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "components", "questItem", "data", "questTitle", "questSub", "chapter_id", "reciteExamList", "now", "total", "per", "questIndex", "nowIndex", "questIds", "isSubArr", "type", "onLoad", "withShareTicket", "menus", "onUnload", "prevPage", "methods", "<PERSON><PERSON><PERSON><PERSON>", "lastQuest", "content", "toFixed", "uni", "title", "icon", "nextQuest", "delta", "submit", "exam_id", "status", "colorWord", "arr", "replacedText", "uso", "bgColorWord", "nobgColorWord", "changebg", "getExamList", "grasp", "res"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAumB,CAAgB,onBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC8C3nB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAR;MACAS;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEArB;MAAA;MACAsB;MACAC;IACA;IAEA;MACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACA;IACAC;EACA;EACAC;IACAC;MACA;QACAlB;QACAC;MACA;IACA;IACAkB;MACA;MACA;MACA;QACA;QACA;QACA;QACA;UACA,kGACAC;UACA;QACA;UACA,oGACAA,UACA;UACA;QACA;UACA;UACA;QACA;QACA,iGACAC,QACA;MACA;QACAC;UACAC;UACAC;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;UACA;UACA;UACA;UACA;YACA,sFACAlB,YACAa;YACA;UACA;YACA,wFACAb,YACAa,UACA;YACA;UACA;YACA;YACA;UACA;UACA,iGACAC,QACA;QACA;UACA;UACA;UACAL;UACAM;YACAI;UACA;QACA;MACA;QACAJ;UACAC;UACAC;QACA;MAEA;IACA;IACAG;MAAA;MACA;QACAC;QACAC;MACA;QACA;UACA;YACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA;QACA;QACA;QACA;UACAC,8JACAC,aACA;QACA;UACAD,6JACAC,aACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACAH;MACA;QACAA;MACA;MACA;QACA;QACA;QACA;UACAC,2CACA,6HACAC,aACA;QACA;UACAD,2CACA,sJACAC,aACA;QACA;MACA;MACA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;QACAJ;MACA;QACA;MACA;MACA;QACA;QACA;QACA;UACAC,2CACA,6HACAC,aACA;QACA;UACAD,2CACA,sJACAC,aACA;QACA;MACA;MACA;MACA;IACA;IACAG;MAAA;MACA;QACA;QACArC;QACA;QACA;UACAgC;YACA;UACA;QACA;UACAhC;UACAgC;QACA;QACA;QACA;UACA,sFACAX,iBACAtB;UACA;QACA;MACA;QACA;QACA;UACA,sFACAsB,iBACAtB;UACA;QACA;MACA;IACA;IACAuC;MAAA;MACA;QACAnC;QACAoC;MACA;QACA;UACA;UACA;UACA;UACA;UACA;YACA,6FACAvC;YACA;UACA;YACA,2FACAwC;YACA;UACA;YACA;YACA;UACA;UACA,kGACA,KACAlB,QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjUA;AAAA;AAAA;AAAA;AAA0qC,CAAgB,2nCAAG,EAAC,C;;;;;;;;;;;ACA9rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/rember/recitation.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/rember/recitation.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./recitation.vue?vue&type=template&id=34f6398b&scoped=true&\"\nvar renderjs\nimport script from \"./recitation.vue?vue&type=script&lang=js&\"\nexport * from \"./recitation.vue?vue&type=script&lang=js&\"\nimport style0 from \"./recitation.vue?vue&type=style&index=0&id=34f6398b&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"34f6398b\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/rember/recitation.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recitation.vue?vue&type=template&id=34f6398b&scoped=true&\"", "var components\ntry {\n  components = {\n    tnLineProgress: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-line-progress/tn-line-progress\" */ \"@/tuniao-ui/components/tn-line-progress/tn-line-progress.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.reciteExamList.length || 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recitation.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recitation.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"tn-flex tn-flex-col-center tn-flex-row-between\">\r\n\t\t\t<view style=\"width: 600rpx;\">\r\n\t\t\t\t<tn-line-progress :percent=\"per\" activeColor=\"#5552FF\" :height=\"20\"\r\n\t\t\t\t\tinactiveColor=\"#ffffff\"></tn-line-progress>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"total\"> <text style=\"color: #FF000A;\">{{questIndex+1}}</text> /{{reciteExamList.length||0}}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"quest_body\">\r\n\t\t\t<quest-item ref=\"quertRemItem\" @changebg=\"changebg\"></quest-item>\r\n\t\t</view>\r\n\t\t<view class=\"quest_footer tn-flex tn-flex-row-between tn-flex-col-center\" v-if=\"type==1\">\r\n\t\t\t<view class=\"qf_left\" @click.stop=\"lastQuest()\">\r\n\t\t\t\t<text class=\"tn-icon-left\"></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"qf_btn\" @click.stop=\"submit('0')\">\r\n\t\t\t\t未掌握\r\n\t\t\t</view>\r\n\t\t\t<view class=\"qf_btn2\" @click.stop=\"submit('1')\">\r\n\t\t\t\t掌握\r\n\t\t\t</view>\r\n\t\t\t<view class=\"qf_right\">\r\n\t\t\t\t<text class=\"tn-icon-right\" @click.stop=\"nextQuest()\"></text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"quest_footer tn-flex tn-flex-row-between tn-flex-col-center\" v-if=\"type==0\">\r\n\t\t\t<view class=\"qf_left\" @click.stop=\"lastQuest()\">\r\n\t\t\t\t<text class=\"tn-icon-left\"></text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"qf_btn\" @click.stop=\"submit('0')\">\r\n\t\t\t\t困难\r\n\t\t\t</view>\r\n\t\t\t<view class=\"qf_btn2\" @click.stop=\"submit('1')\">\r\n\t\t\t\t简单\r\n\t\t\t</view>\r\n\t\t\t<view class=\"qf_right\">\r\n\t\t\t\t<text class=\"tn-icon-right\" @click.stop=\"nextQuest()\"></text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport questItem from \"@/components/quest_item.vue\"\r\n\timport shareMixin from \"@/mixins/shareMixin.js\"\r\n\texport default {\r\n\t\tmixins: [shareMixin],\r\n\t\tcomponents: {\r\n\t\t\tquestItem\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tquestTitle: \"\",\r\n\t\t\t\tquestSub: \"\",\r\n\t\t\t\tchapter_id: null,\r\n\t\t\t\treciteExamList: [],\r\n\t\t\t\tnow: 0,\r\n\t\t\t\ttotal: 0,\r\n\t\t\t\tper: 0,\r\n\t\t\t\tquestItem: {},\r\n\t\t\t\tquestIndex: 0,\r\n\t\t\t\tnowIndex: 0,\r\n\t\t\t\tquestIds: {},\r\n\t\t\t\tisSubArr: [],\r\n\t\t\t\ttype: null\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\tif (options.chapter_id) {\r\n\t\t\t\tthis.chapter_id = options.chapter_id\r\n\t\t\t\tthis.type = options.type\r\n\t\t\t\tthis.getExamList()\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonUnload() {\r\n\t\t\tlet pages = getCurrentPages();\r\n\t\t\tlet prevPage = pages[pages.length - 2];\r\n\t\t\tprevPage.$vm.refData();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsetContent() {\r\n\t\t\t\tthis.$refs.quertRemItem.setContent({\r\n\t\t\t\t\tquestTitle: this.questTitle,\r\n\t\t\t\t\tquestSub: this.questSub\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tlastQuest() {\r\n\t\t\t\tthis.questTitle = \"\"\r\n\t\t\t\tthis.questSub = \"\"\r\n\t\t\t\tif (this.questIndex != 0) {\r\n\t\t\t\t\tthis.questIndex -= 1\r\n\t\t\t\t\tthis.questTitle = this.reciteExamList[this.questIndex].title\r\n\t\t\t\t\tthis.questItem = this.reciteExamList[this.questIndex]\r\n\t\t\t\t\tif (this.reciteExamList[this.questIndex].type == 1) {\r\n\t\t\t\t\t\tthis.questSub = this.colorWord(this.$publicjs.formatRichText(this.reciteExamList[this.questIndex]\r\n\t\t\t\t\t\t\t.content), this.reciteExamList[this.questIndex].cover)\r\n\t\t\t\t\t\tthis.setContent()\r\n\t\t\t\t\t} else if (this.reciteExamList[this.questIndex].type == 2) {\r\n\t\t\t\t\t\tthis.questSub = this.bgColorWord(this.$publicjs.formatRichText(this.reciteExamList[this.questIndex]\r\n\t\t\t\t\t\t\t\t.content),\r\n\t\t\t\t\t\t\tthis.reciteExamList[this.questIndex].cover)\r\n\t\t\t\t\t\tthis.setContent()\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.questSub = this.$publicjs.formatRichText(this.reciteExamList[this.questIndex].content)\r\n\t\t\t\t\t\tthis.setContent()\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.per = Number(Number(Number(this.questIndex + 1) / Number(this.reciteExamList.length) * 100)\r\n\t\t\t\t\t\t.toFixed(\r\n\t\t\t\t\t\t\t2))\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"当前已是第一题啦\",\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tnextQuest() {\r\n\t\t\t\tthis.questTitle = \"\"\r\n\t\t\t\tthis.questSub = \"\"\r\n\t\t\t\tif (this.isSubArr.indexOf(this.questItem.id) >= 0) {\r\n\t\t\t\t\tif (this.questIndex != this.reciteExamList.length - 1) {\r\n\t\t\t\t\t\tthis.questIndex += 1\r\n\t\t\t\t\t\tthis.questTitle = this.reciteExamList[this.questIndex].title\r\n\t\t\t\t\t\tthis.questItem = this.reciteExamList[this.questIndex]\r\n\t\t\t\t\t\tif (this.reciteExamList[this.questIndex].type == 1) {\r\n\t\t\t\t\t\t\tthis.questSub = this.colorWord(this.$publicjs.formatRichText(this.reciteExamList[this\r\n\t\t\t\t\t\t\t\t\t.questIndex]\r\n\t\t\t\t\t\t\t\t.content), this.reciteExamList[this.questIndex].cover)\r\n\t\t\t\t\t\t\tthis.setContent()\r\n\t\t\t\t\t\t} else if (this.reciteExamList[this.questIndex].type == 2) {\r\n\t\t\t\t\t\t\tthis.questSub = this.bgColorWord(this.$publicjs.formatRichText(this.reciteExamList[this\r\n\t\t\t\t\t\t\t\t\t\t.questIndex]\r\n\t\t\t\t\t\t\t\t\t.content),\r\n\t\t\t\t\t\t\t\tthis.reciteExamList[this.questIndex].cover)\r\n\t\t\t\t\t\t\tthis.setContent()\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.questSub = this.$publicjs.formatRichText(this.reciteExamList[this.questIndex].content)\r\n\t\t\t\t\t\t\tthis.setContent()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.per = Number(Number(Number(this.questIndex + 1) / Number(this.reciteExamList.length) * 100)\r\n\t\t\t\t\t\t\t.toFixed(\r\n\t\t\t\t\t\t\t\t2))\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tlet pages = getCurrentPages();\r\n\t\t\t\t\t\tlet prevPage = pages[pages.length - 2];\r\n\t\t\t\t\t\tprevPage.$vm.refData();\r\n\t\t\t\t\t\tuni.navigateBack({\r\n\t\t\t\t\t\t\tdelta: 1\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"请选择当前题目\",\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsubmit(type) {\r\n\t\t\t\tthis.$http.post(this.$api.reciteSubmitExam, {\r\n\t\t\t\t\texam_id: this.questItem.id,\r\n\t\t\t\t\tstatus: type\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tif (this.isSubArr.indexOf(this.questItem.id) < 0) {\r\n\t\t\t\t\t\t\tthis.isSubArr.push(this.questItem.id)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.nextQuest()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tcolorWord(richText, keyword) {\r\n\t\t\t\t// 创建正则表达式，使用全局标志'g'来匹配所有实例\r\n\t\t\t\tlet us = richText.match(/<u>(.*?)<\\/u>/g)\r\n\t\t\t\tlet replacedText = richText\r\n\t\t\t\tlet arr = []\r\n\t\t\t\tif (this.questIds[this.questItem.id] && this.questIds[this.questItem.id].length) {\r\n\t\t\t\t\tarr = this.questIds[this.questItem.id]\r\n\t\t\t\t} else {\r\n\t\t\t\t\tarr = this.questIds[this.questItem.id] = []\r\n\t\t\t\t}\r\n\t\t\t\tfor (let i = 0; i < us.length; i++) {\r\n\t\t\t\t\tlet uso = us[i].match(/<u>(.*?)<\\/u>/)[1]\r\n\t\t\t\t\t// 执行替换操作\r\n\t\t\t\t\tif (arr.indexOf(String(i)) >= 0) {\r\n\t\t\t\t\t\treplacedText = replacedText.replace(us[i],\r\n\t\t\t\t\t\t\t`<a href=\"http://localhost:8081\" style=\"word-break: break-all;display: inline-block; color: #5552FF;\">${uso}</a>`\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treplacedText = replacedText.replace(us[i],\r\n\t\t\t\t\t\t\t`<a href=\"http://localhost:8081\" style=\"word-break: break-all;display: inline-block;color: #5552FF;\">${uso}</a>`\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn replacedText\r\n\t\t\t},\r\n\t\t\tbgColorWord(richText, keyword) {\r\n\t\t\t\t// 创建正则表达式，使用全局标志'g'来匹配所有实例\r\n\t\t\t\tlet us = richText.match(/<u>(.*?)<\\/u>/g)\r\n\t\t\t\tlet replacedText = richText\r\n\t\t\t\tlet arr = []\r\n\t\t\t\tif (this.questIds[this.questItem.id] && this.questIds[this.questItem.id].length > 0) {\r\n\t\t\t\t\tarr = this.questIds[this.questItem.id]\r\n\t\t\t\t} else {\r\n\t\t\t\t\tarr = this.questIds[this.questItem.id] = []\r\n\t\t\t\t}\r\n\t\t\t\tfor (let i = 0; i < us.length; i++) {\r\n\t\t\t\t\tlet uso = us[i].match(/<u>(.*?)<\\/u>/)[1]\r\n\t\t\t\t\t// 执行替换操作\r\n\t\t\t\t\tif (arr.indexOf(String(i)) >= 0) {\r\n\t\t\t\t\t\treplacedText = replacedText.replace(us[i],\r\n\t\t\t\t\t\t\t`<a href=\"http://localhost:8080?` + i +\r\n\t\t\t\t\t\t\t`\" style=\"word-break: break-all;display: inline-block; color: #5552FF;\">${uso}</a>`\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treplacedText = replacedText.replace(us[i],\r\n\t\t\t\t\t\t\t`<a href=\"http://localhost:8080?` + i +\r\n\t\t\t\t\t\t\t`\" style=\"word-break: break-all;display: inline-block;background-color:#E8E7FF; color: #E8E7FF;\">${uso}</a>`\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 输出替换后的富文本内容\r\n\t\t\t\treturn replacedText\r\n\t\t\t},\r\n\t\t\tnobgColorWord(richText, keyword) {\r\n\t\t\t\t// 创建正则表达式，使用全局标志'g'来匹配所有实例\r\n\t\t\t\tlet us = richText.match(/<u>(.*?)<\\/u>/g)\r\n\t\t\t\tlet replacedText = richText\r\n\t\t\t\tlet arr = []\r\n\t\t\t\tif (this.questIds[this.questItem.id] && this.questIds[this.questItem.id].length) {\r\n\t\t\t\t\tarr = this.questIds[this.questItem.id]\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.questIds[this.questItem.id] = []\r\n\t\t\t\t}\r\n\t\t\t\tfor (let i = 0; i < us.length; i++) {\r\n\t\t\t\t\tlet uso = us[i].match(/<u>(.*?)<\\/u>/)[1]\r\n\t\t\t\t\t// 执行替换操作\r\n\t\t\t\t\tif (arr.indexOf(String(i)) >= 0) {\r\n\t\t\t\t\t\treplacedText = replacedText.replace(us[i],\r\n\t\t\t\t\t\t\t`<a href=\"http://localhost:8080?` + i +\r\n\t\t\t\t\t\t\t`\" style=\"word-break: break-all;display: inline-block; color: #5552FF;\">${uso}</a>`\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treplacedText = replacedText.replace(us[i],\r\n\t\t\t\t\t\t\t`<a href=\"http://localhost:8080?` + i +\r\n\t\t\t\t\t\t\t`\" style=\"word-break: break-all;display: inline-block;background-color:#E8E7FF; color: #E8E7FF;\">${uso}</a>`\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 输出替换后的富文本内容\r\n\t\t\t\treturn replacedText\r\n\t\t\t},\r\n\t\t\tchangebg(e) {\r\n\t\t\t\tif (this.questIds[this.questItem.id] && this.questIds[this.questItem.id].length > 0) {\r\n\t\t\t\t\tlet data = []\r\n\t\t\t\t\tdata = JSON.parse(JSON.stringify(this.questIds))[this.questItem.id]\r\n\t\t\t\t\tlet arr = []\r\n\t\t\t\t\tif (data.indexOf(e) >= 0) {\r\n\t\t\t\t\t\tarr = data.filter(item => {\r\n\t\t\t\t\t\t\treturn item != e\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tdata.push(String(e))\r\n\t\t\t\t\t\tarr = data\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.questIds[this.questItem.id] = arr\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.questSub = this.bgColorWord(this.$publicjs.formatRichText(this.questItem\r\n\t\t\t\t\t\t\t\t.content), this\r\n\t\t\t\t\t\t\t.questItem.cover)\r\n\t\t\t\t\t\tthis.setContent()\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.questIds[this.questItem.id] = [String(e)]\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.questSub = this.bgColorWord(this.$publicjs.formatRichText(this.questItem\r\n\t\t\t\t\t\t\t\t.content), this\r\n\t\t\t\t\t\t\t.questItem.cover)\r\n\t\t\t\t\t\tthis.setContent()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetExamList() {\r\n\t\t\t\tthis.$http.post(this.$api.reciteExamList, {\r\n\t\t\t\t\tchapter_id: this.chapter_id,\r\n\t\t\t\t\tgrasp: 0\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.reciteExamList = res.data\r\n\t\t\t\t\t\tthis.questTitle = res.data[0].title\r\n\t\t\t\t\t\tthis.questItem = res.data[0]\r\n\t\t\t\t\t\tthis.questIndex = 0\r\n\t\t\t\t\t\tif (res.data[0].type == 1) {\r\n\t\t\t\t\t\t\tthis.questSub = this.colorWord(this.$publicjs.formatRichText(res.data[0].content), res\r\n\t\t\t\t\t\t\t\t.data[0].cover)\r\n\t\t\t\t\t\t\tthis.setContent()\r\n\t\t\t\t\t\t} else if (res.data[0].type == 2) {\r\n\t\t\t\t\t\t\tthis.questSub = this.bgColorWord(this.$publicjs.formatRichText(res.data[0].content),\r\n\t\t\t\t\t\t\t\tres.data[0].cover)\r\n\t\t\t\t\t\t\tthis.setContent()\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.questSub = this.$publicjs.formatRichText(res.data[0].content)\r\n\t\t\t\t\t\t\tthis.setContent()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.per = Number(Number(Number(this.questIndex + 1) / Number(this.reciteExamList.length) *\r\n\t\t\t\t\t\t\t\t100)\r\n\t\t\t\t\t\t\t.toFixed(\r\n\t\t\t\t\t\t\t\t2))\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.quest_body {\r\n\t\tmargin-top: 30rpx;\r\n\t\tpadding-bottom: calc(env(safe-area-inset-bottom) + 140rpx);\r\n\t}\r\n\r\n\t.quest_footer {\r\n\t\twidth: 750rpx;\r\n\t\tposition: fixed;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\theight: calc(env(safe-area-inset-bottom) + 120rpx);\r\n\t\tbottom: 0rpx;\r\n\t\tleft: 0rpx;\r\n\t\tright: 0rpx;\r\n\t\tpadding: 0rpx 30rpx;\r\n\t}\r\n\r\n\t.qf_left {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\tbackground: #EAEAEA;\r\n\t\tfont-size: 45rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 60rpx;\r\n\t\tcolor: #979797;\r\n\t}\r\n\r\n\t.qf_right {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\tbackground: #EAEAEA;\r\n\t\tfont-size: 45rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 60rpx;\r\n\t\tcolor: #979797;\r\n\t}\r\n\r\n\t.qf_btn {\r\n\t\twidth: 250rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 100rpx;\r\n\t\tbackground-color: #E8E7FF;\r\n\t\tcolor: #5552FF;\r\n\t\tfont-size: 28rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t}\r\n\r\n\t.qf_btn2 {\r\n\t\twidth: 250rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 100rpx;\r\n\t\tbackground-color: #5552FF;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 28rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t}\r\n\r\n\t.total {\r\n\t\tcolor: #999999;\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n\r\n\t.content {\r\n\t\twidth: 100%;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recitation.vue?vue&type=style&index=0&id=34f6398b&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./recitation.vue?vue&type=style&index=0&id=34f6398b&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753982130641\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}