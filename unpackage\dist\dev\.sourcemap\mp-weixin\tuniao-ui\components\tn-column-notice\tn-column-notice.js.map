{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-column-notice/tn-column-notice.vue?d5df", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-column-notice/tn-column-notice.vue?9ce0", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-column-notice/tn-column-notice.vue?1d5b", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-column-notice/tn-column-notice.vue?e724", "uni-app:///tuniao-ui/components/tn-column-notice/tn-column-notice.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-column-notice/tn-column-notice.vue?3f3b", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-column-notice/tn-column-notice.vue?ad3f"], "names": ["name", "mixins", "props", "list", "type", "default", "show", "playStatus", "mode", "leftIcon", "leftIconName", "leftIconSize", "rightIcon", "rightIconName", "rightIconSize", "closeBtn", "radius", "padding", "autoplay", "duration", "computed", "fontStyle", "style", "noticeStyle", "swiperStyle", "vertical", "data", "watch", "methods", "click", "close", "clickLeftIcon", "clickRightIcon", "change"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyI;AACzI;AACoE;AACL;AACsC;;;AAGrG;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,uGAAM;AACR,EAAE,gHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3BA;AAAA;AAAA;AAAA;AAA4nB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+ChpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;EACAA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;EACA;EACAe;IACAC;MAAA;MACA;QACA;QACAC;QACAA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QAEA;MACA;IACA;IACAC;MACA;MACAD;MACA;MACA;IACA;IACAE;MACA;MACAF;MACAA;MAEA;IACA;IACA;IACAG;MACA,kDACA;IACA;EACA;EACAC;IACA,QAEA;EACA;EACAC,QAEA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC3MA;AAAA;AAAA;AAAA;AAA2sC,CAAgB,ioCAAG,EAAC,C;;;;;;;;;;;ACA/tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-column-notice/tn-column-notice.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-column-notice.vue?vue&type=template&id=114031fc&scoped=true&\"\nvar renderjs\nimport script from \"./tn-column-notice.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-column-notice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-column-notice.vue?vue&type=style&index=0&id=114031fc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"114031fc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-column-notice/tn-column-notice.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-column-notice.vue?vue&type=template&id=114031fc&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.noticeStyle])\n  var s1 = _vm.leftIcon ? _vm.__get_style([_vm.fontStyle(\"leftIcon\")]) : null\n  var s2 = _vm.__get_style([_vm.swiperStyle])\n  var s3 = _vm.__get_style([_vm.fontStyle()])\n  var s4 = _vm.rightIcon ? _vm.__get_style([_vm.fontStyle(\"rightIcon\")]) : null\n  var s5 = _vm.closeBtn ? _vm.__get_style([_vm.fontStyle(\"close\")]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n        s4: s4,\n        s5: s5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-column-notice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-column-notice.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view\r\n    class=\"tn-column-notice-class tn-column-notice\"\r\n    :class=\"[backgroundColorClass]\"\r\n    :style=\"[noticeStyle]\"\r\n  >\r\n    <!-- 左图标 -->\r\n    <view class=\"tn-column-notice__icon\">\r\n      <view\r\n        v-if=\"leftIcon\"\r\n        class=\"tn-column-notice__icon--left\" \r\n        :class=\"[`tn-icon-${leftIconName}`,fontColorClass]\"\r\n        :style=\"[fontStyle('leftIcon')]\"\r\n        @tap=\"clickLeftIcon\"></view>\r\n    </view>\r\n    \r\n    <!-- 滚动显示内容 -->\r\n    <swiper class=\"tn-column-notice__swiper\" :style=\"[swiperStyle]\" :vertical=\"vertical\" circular :autoplay=\"autoplay && playStatus === 'play'\" :interval=\"duration\" @change=\"change\">\r\n      <swiper-item v-for=\"(item, index) in list\" :key=\"index\" class=\"tn-column-notice__swiper--item\">\r\n        <view\r\n          class=\"tn-column-notice__swiper--content tn-text-ellipsis\"\r\n          :class=\"[fontColorClass]\"\r\n          :style=\"[fontStyle()]\"\r\n          @tap=\"click(index)\"\r\n        >{{ item }}</view>\r\n      </swiper-item>\r\n    </swiper>\r\n    \r\n    <!-- 右图标 -->\r\n    <view class=\"tn-column-notice__icon\">\r\n      <view\r\n        v-if=\"rightIcon\"\r\n        class=\"tn-column-notice__icon--right\" \r\n        :class=\"[`tn-icon-${rightIconName}`,fontColorClass]\"\r\n        :style=\"[fontStyle('rightIcon')]\"\r\n        @tap=\"clickRightIcon\"></view>\r\n      <view\r\n        v-if=\"closeBtn\"\r\n        class=\"tn-column-notice__icon--right\" \r\n        :class=\"[`tn-icon-close`,fontColorClass]\"\r\n        :style=\"[fontStyle('close')]\"\r\n        @tap=\"close\"></view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import componentsColorMixin from '../../libs/mixin/components_color.js'\r\n  export default {\r\n    name: 'tn-column-notice',\r\n    mixins: [componentsColorMixin],\r\n    props: {\r\n      // 显示的内容\r\n      list: {\r\n        type: Array,\r\n        default() {\r\n          return []\r\n        }\r\n      },\r\n      // 是否显示\r\n      show: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 播放状态\r\n      // play -> 播放 paused -> 暂停\r\n      playStatus: {\r\n        type: String,\r\n        default: 'play'\r\n      },\r\n      // 滚动方向\r\n      // horizontal -> 水平滚动 vertical -> 垂直滚动\r\n      mode: {\r\n        type: String,\r\n        default: 'horizontal'\r\n      },\r\n      // 是否显示左边图标\r\n      leftIcon: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 左边图标的名称\r\n      leftIconName: {\r\n        type: String,\r\n        default: 'sound'\r\n      },\r\n      // 左边图标的大小\r\n      leftIconSize: {\r\n        type: Number,\r\n        default: 34\r\n      },\r\n      // 是否显示右边的图标\r\n      rightIcon: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 右边图标的名称\r\n      rightIconName: {\r\n        type: String,\r\n        default: 'right'\r\n      },\r\n      // 右边图标的大小\r\n      rightIconSize: {\r\n        type: Number,\r\n        default: 26\r\n      },\r\n      // 是否显示关闭按钮\r\n      closeBtn: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 圆角\r\n      radius: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // 内边距\r\n      padding: {\r\n        type: String,\r\n        default: '18rpx 24rpx'\r\n      },\r\n      // 自动播放\r\n      autoplay: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 滚动周期\r\n      duration: {\r\n        type: Number,\r\n        default: 2000\r\n      }\r\n    },\r\n    computed: {\r\n      fontStyle() {\r\n        return (type) => {\r\n          let style = {}\r\n          style.color = this.fontColorStyle ? this.fontColorStyle : ''\r\n          style.fontSize = this.fontSizeStyle ? this.fontSizeStyle : ''\r\n          if (type === 'leftIcon' && this.leftIconSize) {\r\n            style.fontSize = this.leftIconSize + 'rpx'\r\n          }\r\n          if (type === 'rightIcon' && this.rightIconSize) {\r\n            style.fontSize = this.rightIconSize + 'rpx'\r\n          }\r\n          if (type === 'close') {\r\n            style.fontSize = '24rpx'\r\n          }\r\n          \r\n          return style\r\n        }\r\n      },\r\n      noticeStyle() {\r\n        let style = {}\r\n        style.backgroundColor = this.backgroundColorStyle ? this.backgroundColorStyle : 'transparent'\r\n        if (this.padding) style.padding = this.padding\r\n        return style\r\n      },\r\n      swiperStyle() {\r\n        let style = {}\r\n        style.height = this.fontSize ? this.fontSize + 6 + this.fontUnit : '32rpx'\r\n        style.lineHeight = style.height\r\n        \r\n        return style\r\n      },\r\n      // 标记是否为垂直\r\n      vertical() {\r\n        if (this.mode === 'horizontal') return false\r\n        else return true\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        \r\n      }\r\n    },\r\n    watch: {\r\n      \r\n    },\r\n    methods: {\r\n      // 点击了通知栏\r\n      click(index) {\r\n        this.$emit('click', index)\r\n      },\r\n      // 点击了关闭按钮\r\n      close() {\r\n        this.$emit('close')\r\n      },\r\n      // 点击了左边图标\r\n      clickLeftIcon() {\r\n        this.$emit('clickLeft')\r\n      },\r\n      // 点击了右边图标\r\n      clickRightIcon() {\r\n        this.$emit('clickRight')\r\n      },\r\n      // 切换消息时间\r\n      change(event) {\r\n        let index = event.detail.current\r\n        if (index === this.list.length - 1) {\r\n          this.$emit('end')\r\n        }\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  \r\n  .tn-column-notice {\r\n    width: 100%;\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: center;\r\n    flex-wrap: nowrap;\r\n    overflow: hidden;\r\n    \r\n    &__swiper {\r\n      height: auto;\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      margin-left: 12rpx;\r\n      \r\n      &--item {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        overflow: hidden;\r\n      }\r\n      \r\n      &--content {\r\n        overflow: hidden;\r\n      }\r\n    }\r\n    \r\n    &__icon {\r\n      &--left {\r\n        display: inline-flex;\r\n        align-items: center;\r\n      }\r\n      \r\n      &--right {\r\n        margin-left: 12rpx;\r\n        display: inline-flex;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-column-notice.vue?vue&type=style&index=0&id=114031fc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-column-notice.vue?vue&type=style&index=0&id=114031fc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980406062\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}