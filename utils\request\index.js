/* eslint-disable */
import Request from './request';
import {
	refreshToken
} from '@/api/api.js';
import indexConfig from '@/config/index.js';
import tools from '../common.js'

const http = new Request();

// request全局参数设置
http.setConfig(config => {
	/* 设置全局配置 */
	config.baseUrl = indexConfig.baseUrl; /* 根域名不同 */
	const systemInfo = uni.getSystemInfoSync();
	const systemInfoHeaders = {
		'device-name': systemInfo.brand, // 设备名称
		width: systemInfo.screenWidth, // 屏幕宽度
		height: systemInfo.screenHeight, // 屏幕高度
		os: systemInfo.platform, // 客户端平台
		'os-version': systemInfo.system // 操作系统版本
	};
	config.header = {
		...config.header,
		//...systemInfoHeaders
	};
	return config;
});

let isRefreshing = false;
let requests = [];
http.interceptor.request(
	config => {
		let header = {
			Authorization: uni.getStorageSync('TOKEN') || ''
			// Authorization: '123456789'
			// clientId: 'default'
		}
		if (config.dataType === 'formData') {
			header['Content-Type'] = 'application/x-www-form-urlencoded'
		} else {
			header['Content-Type'] = 'application/json'
		}
		/* 请求之前拦截器 */
		config.header = header;
		return config;
	},
	error => {
		return Promise.reject(error);
	}
);
http.interceptor.response(
	response => {
		/* 请求之后拦截器 */
		switch (response.data.code) {
			case 0:
				tools.toast(response.data.msg);
				return response.data;
				break;
			case 1:
				return response.data;
				break;
			case 200:
				return response.data;
				break;
			case 400:
				return Promise.reject(response.data.msg);
				break;
			case 401:
				if (uni.getStorageSync('TOKEN')) {
					uni.clearStorage()
					uni.showModal({
						content: "会话已过期，是否跳转登录页面？",
						cancelColor: '#4FAE5A',
						confirmColor: '#DFBD9D',
						success: confirmRes => {
							if (confirmRes.confirm) {
								uni.clearStorage()
								uni.switchTab({
									url: "/pages/quest/quest"
								})
							}
						}
					});
				} else {
					uni.clearStorage()
					uni.switchTab({
						url: "/pages/quest/quest"
					})
				}
				break;
			case 405:
				tools.toast('当前操作不被允许');
				return Promise.reject(response.data.msg);
			case 404:
				tools.toast(response.data.msg);
				return Promise.reject(response.data.msg);
			case 429:
				tools.toast('请求过多，先休息一下吧');
				return Promise.reject(response.data.msg);
			case 500:
				if (!response.data.msg) {
					uni.showModal({
						content: "会话已过期，是否跳转登录页面？",
						cancelColor: '#FF5656',
						confirmColor: '#DFBD9D',
						success: confirmRes => {
							if (confirmRes.confirm) {
								uni.clearStorage()
								uni.switchTab({
									url: "/pages/quest/quest"
								})
							}
						}
					});
				} else {
					tools.toast(response.data.msg);
				}
				return Promise.reject(response.data.msg);
			default:
				tools.toast(response.data.msg);
				return Promise.reject(response.data.msg);
		}
	},
	error => {
		switch (error.data.code) {
			case 401:
				if (uni.getStorageSync('TOKEN')) {
					uni.clearStorage()
					uni.showModal({
						content: "会话已过期，是否跳转登录页面？",
						cancelColor: '#999999',
						confirmColor: '#4FAE5A',
						success: confirmRes => {
							if (confirmRes.confirm) {
								uni.clearStorage()
								uni.switchTab({
									url: "/pages/quest/quest"
								})
							}
						}
					});
				} else {
					uni.clearStorage()
					uni.switchTab({
						url: "/pages/quest/quest"
					})
				}
				break;
			default:
				return Promise.reject(error);
		}
	}
);

export {
	http
};