<view style="padding-bottom:140rpx;" class="data-v-67f73f95"><view class="quest_primary tn-bg-white data-v-67f73f95"><view class="qp_top tn-flex tn-flex-row-between tn-flex-col-center data-v-67f73f95"><view class="qpt_left tn-flex tn-flex-col-center data-v-67f73f95"><view class="type_tips data-v-67f73f95"><block wx:if="{{questItem.type==0}}"><text class="data-v-67f73f95">单选题</text></block><block wx:if="{{questItem.type==1}}"><text class="data-v-67f73f95">多选题</text></block><block wx:if="{{questItem.type==2}}"><text class="data-v-67f73f95">不定项</text></block></view><text style="margin-left:0rpx;font-size:28rpx;color:#999999;" class="data-v-67f73f95"><text style="color:#FF000A;" class="data-v-67f73f95">{{questIndex+1}}</text><text style="margin:0rpx 5rpx;" class="data-v-67f73f95">/</text>{{''+$root.g0+''}}</text></view><view class="tn-flex tn-flex-col-center data-v-67f73f95"><block wx:if="{{questItem.is_collect==1}}"><view data-event-opts="{{[['tap',[['toColl']]]]}}" style="font-size:28rpx;color:#A1A1A1;" catchtap="__e" class="data-v-67f73f95"><text class="tn-icon-star-fill data-v-67f73f95" style="color:#FFBD23;"></text>已收藏</view></block><block wx:if="{{questItem.is_collect==0}}"><view data-event-opts="{{[['tap',[['toColl']]]]}}" style="font-size:28rpx;color:#A1A1A1;" catchtap="__e" class="data-v-67f73f95"><text class="tn-icon-star-fill data-v-67f73f95" style="color:#A1A1A1;"></text>收藏</view></block><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="correction data-v-67f73f95" catchtap="__e"><text class="tn-icon-help-fill data-v-67f73f95" style="margin-right:4rpx;"></text>纠错</view></view></view><view class="quest_warp data-v-67f73f95"><view class="quest_title data-v-67f73f95">{{''+(questItem.title_id+"、")+(questItem.title||'')+''}}</view><block wx:if="{{$root.g1}}"><view class="tn-width-full data-v-67f73f95" style="margin-top:20rpx;margin-bottom:20rpx;"><block wx:for="{{questItem.title_img}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tn-width-full data-v-67f73f95"><image style="width:100%;" src="{{baseUrl+item}}" mode="widthFix" class="data-v-67f73f95"></image></view></block></view></block><view class="answer_warp tn-width-full data-v-67f73f95"><block wx:if="{{$root.g2<0}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="answer_mask data-v-67f73f95" catchtap="__e"></view></block><block wx:if="{{$root.g3<0}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="answer_mask_title data-v-67f73f95" catchtap="__e">点击空白处可查看选项</view></block><block wx:for="{{questItem.option}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['answer_item','tn-flex','tn-flex-col-center','tn-flex-row-between','data-v-67f73f95',item.answer?'suc_bg':'nom_bg']}}"><view class="{{['data-v-67f73f95',item.answer?'suc_word':'tn-width-full']}}">{{''+item.option+''}}</view><block wx:if="{{item.answer}}"><image style="width:44rpx;height:44rpx;" src="../../static/icon/suc_sel.png" mode="widthFix" class="data-v-67f73f95"></image></block></view></block></view></view></view><view class="answer_history data-v-67f73f95"><view class="tn-width-full tn-flex tn-flex-col-center data-v-67f73f95"><image style="width:32rpx;" src="../../static/icon/his.png" mode="widthFix" class="data-v-67f73f95"></image><view style="font-size:32rpx;color:#333333;margin-left:5rpx;" class="data-v-67f73f95">答题记录</view></view><view class="tn-width-full tn-flex tn-flex-wrap data-v-67f73f95"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view style="width:20%;text-align:center;margin-top:20rpx;" class="data-v-67f73f95"><view style="font-size:28rpx;color:#999999;" class="data-v-67f73f95">{{index+1+"、"}}<text style="color:#FF000A;" class="data-v-67f73f95">{{item.g4}}</text></view></view></block></view></view><view class="answer_history data-v-67f73f95"><tn-tabs vue-id="d21d97de-1" list="{{list_type}}" bold="{{true}}" isScroll="{{false}}" activeColor="#333333" inactiveColor="#333333" current="{{current}}" name="name" data-event-opts="{{[['^change',[['change']]]]}}" bind:change="__e" class="data-v-67f73f95" bind:__l="__l"></tn-tabs><block wx:if="{{$root.g5}}"><view class="tn-width-full data-v-67f73f95" style="margin-top:20rpx;margin-bottom:20rpx;"><block wx:for="{{questItem.analysis_img}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tn-width-full data-v-67f73f95"><image style="width:100%;" src="{{baseUrl+item}}" mode="widthFix" class="data-v-67f73f95"></image></view></block></view></block><block wx:if="{{current==0}}"><view style="margin-top:40rpx;" class="data-v-67f73f95"><text user-select="{{true}}" class="data-v-67f73f95">{{questItem.analysis}}</text></view></block><block wx:if="{{current==1}}"><view style="margin-top:30rpx;" class="data-v-67f73f95"><view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center data-v-67f73f95"><view class="tn-flex tn-flex-col-center data-v-67f73f95"><view data-event-opts="{{[['tap',[['changeSort',['0']]]]]}}" class="{{['data-v-67f73f95',is_sort==0?'is_sel':'no_sel']}}" catchtap="__e">采纳数排序</view><view style="margin:0rpx 10rpx;color:#D8D8D8;" class="data-v-67f73f95">|</view><view data-event-opts="{{[['tap',[['changeSort',['1']]]]]}}" class="{{['data-v-67f73f95',is_sort==1?'is_sel':'no_sel']}}" catchtap="__e">时间排序</view></view><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="add_note tn-flex tn-flex-col-center tn-flex-row-center data-v-67f73f95" catchtap="__e"><image style="width:30rpx;margin-right:2rpx;" src="../../static/icon/add_note.png" mode="widthFix" class="data-v-67f73f95"></image><view class="data-v-67f73f95">做笔记</view></view></view><view class="tn-width-full data-v-67f73f95"><block wx:for="{{noteList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tn-width-full data-v-67f73f95"><note-other vue-id="{{'d21d97de-2-'+index}}" item="{{item}}" data-event-opts="{{[['^toCollect',[['toCollectOther']]]]}}" bind:toCollect="__e" class="data-v-67f73f95" bind:__l="__l"></note-other></view></block></view></view></block><block wx:if="{{current==2}}"><view style="margin-top:30rpx;" class="data-v-67f73f95"><view class="mine_note_top tn-width-full tn-flex tn-flex-row-between tn-flex-col-center data-v-67f73f95"><view class="tn-flex tn-flex-col-center data-v-67f73f95" style="font-size:24rpx;color:#333333;">好记性不如烂笔头~</view><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="tn-flex tn-flex-col-center tn-flex-row-center data-v-67f73f95" catchtap="__e"><image style="width:30rpx;margin-right:2rpx;" src="../../static/icon/add_note.png" mode="widthFix" class="data-v-67f73f95"></image><view style="color:#5552FF;font-size:24rpx;" class="data-v-67f73f95">做笔记</view></view></view><view class="tn-width-full data-v-67f73f95"><block wx:for="{{noteList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tn-width-full data-v-67f73f95"><note-mine vue-id="{{'d21d97de-3-'+index}}" item="{{item}}" data-event-opts="{{[['^todel',[['toMineDel']]],['^toedit',[['toMineEdit']]]]}}" bind:todel="__e" bind:toedit="__e" class="data-v-67f73f95" bind:__l="__l"></note-mine></view></block></view></view></block></view><quest-fixed-rem vue-id="d21d97de-4" end="{{isEnd}}" data-event-opts="{{[['^nextQuest',[['nextQuestBefore']]],['^lastQuest',[['lastQuestBefore']]],['^submitNow',[['submitNow']]],['^submitEnd',[['submitEnd']]],['^showNote',[['e5']]]]}}" bind:nextQuest="__e" bind:lastQuest="__e" bind:submitNow="__e" bind:submitEnd="__e" bind:showNote="__e" class="data-v-67f73f95" bind:__l="__l"></quest-fixed-rem><tn-popup bind:input="__e" vue-id="d21d97de-5" mode="bottom" borderRadius="{{40}}" safeAreaInsetBottom="{{true}}" value="{{showError}}" data-event-opts="{{[['^input',[['__set_model',['','showError','$event',[]]]]]]}}" class="data-v-67f73f95" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-67f73f95" style="width:750rpx;height:120rpx;padding:30rpx;box-sizing:border-box;background:url('../../static/pop_head.png') no-repeat;background-size:100% 100%;"><text class="tn-text-bold data-v-67f73f95">反馈</text></view><view class="tn-width-full tn-flex tn-flex-direction-column data-v-67f73f95" style="padding:0rpx 30rpx 30rpx 30rpx;box-sizing:border-box;"><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-67f73f95"><view data-event-opts="{{[['tap',[['e6',['$event']]]]]}}" class="{{['data-v-67f73f95',selError=='有错别字'?'sel_is':'sel_no']}}" catchtap="__e">有错别字</view><view data-event-opts="{{[['tap',[['e7',['$event']]]]]}}" class="{{['data-v-67f73f95',selError=='题干有误'?'sel_is':'sel_no']}}" catchtap="__e">题干有误</view><view data-event-opts="{{[['tap',[['e8',['$event']]]]]}}" class="{{['data-v-67f73f95',selError=='答案有误'?'sel_is':'sel_no']}}" catchtap="__e">答案有误</view></view><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-67f73f95" style="margin-top:30rpx;"><view data-event-opts="{{[['tap',[['e9',['$event']]]]]}}" class="{{['data-v-67f73f95',selError=='解析有误'?'sel_is':'sel_no']}}" catchtap="__e">解析有误</view><view data-event-opts="{{[['tap',[['e10',['$event']]]]]}}" class="{{['data-v-67f73f95',selError=='解析缺失'?'sel_is':'sel_no']}}" catchtap="__e">解析缺失</view><view data-event-opts="{{[['tap',[['e11',['$event']]]]]}}" class="{{['data-v-67f73f95',selError=='选择有误'?'sel_is':'sel_no']}}" catchtap="__e">选择有误</view></view></view><view class="scroll_warp2 tn-width-full data-v-67f73f95"><tn-input bind:input="__e" vue-id="{{('d21d97de-6')+','+('d21d97de-5')}}" placeholder="开始输入..." clearable="{{false}}" type="textarea" border="{{false}}" height="{{324}}" autoHeight="{{false}}" value="{{err_value}}" data-event-opts="{{[['^input',[['__set_model',['','err_value','$event',[]]]]]]}}" class="data-v-67f73f95" bind:__l="__l"></tn-input></view><view class="tn-flex tn-flex-col-top tn-flex-row-center data-v-67f73f95" style="height:150rpx;"><view data-event-opts="{{[['tap',[['submitErrorQuest']]]]}}" class="submit data-v-67f73f95" catchtap="__e">立即反馈</view></view></tn-popup><tn-popup bind:input="__e" vue-id="d21d97de-7" mode="bottom" borderRadius="{{40}}" safeAreaInsetBottom="{{true}}" value="{{showAdd}}" data-event-opts="{{[['^input',[['__set_model',['','showAdd','$event',[]]]]]]}}" class="data-v-67f73f95" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center tn-text-bold data-v-67f73f95" style="width:750rpx;height:120rpx;padding:30rpx;box-sizing:border-box;background:url('../../static/pop_head.png') no-repeat;background-size:100% 100%;"><block wx:if="{{noteType==1}}"><view style="font-size:34rpx;" class="data-v-67f73f95">章笔记</view></block><block wx:if="{{noteType==0}}"><view style="font-size:34rpx;" class="data-v-67f73f95">试题笔记</view></block><block wx:if="{{noteType==1}}"><view data-event-opts="{{[['tap',[['e12',['$event']]]]]}}" class="tn-flex tn-flex-row-center tn-flex-col-center data-v-67f73f95" style="width:180rpx;height:48rpx;border-radius:55rpx;background-color:#E3E3FF;color:#5552FF;font-size:24rpx;" catchtap="__e"><text class="tn-icon-edit data-v-67f73f95" style="font-size:32rpx;margin-right:5rpx;"></text>试题笔记</view></block><block wx:if="{{noteType==0}}"><view data-event-opts="{{[['tap',[['e13',['$event']]]]]}}" class="tn-flex tn-flex-row-center tn-flex-col-center data-v-67f73f95" style="width:180rpx;height:48rpx;border-radius:55rpx;background-color:#E3E3FF;color:#5552FF;font-size:24rpx;" catchtap="__e"><text class="tn-icon-edit data-v-67f73f95" style="font-size:32rpx;margin-right:5rpx;"></text>章笔记</view></block></view><view class="scroll_warp2 tn-width-full data-v-67f73f95"><tn-input bind:input="__e" vue-id="{{('d21d97de-8')+','+('d21d97de-7')}}" placeholder="开始输入..." clearable="{{false}}" type="textarea" border="{{false}}" height="{{324}}" autoHeight="{{false}}" value="{{note_value}}" data-event-opts="{{[['^input',[['__set_model',['','note_value','$event',[]]]]]]}}" class="data-v-67f73f95" bind:__l="__l"></tn-input></view><view class="tn-flex tn-flex-col-top tn-flex-row-center data-v-67f73f95" style="height:150rpx;"><view data-event-opts="{{[['tap',[['submitNote']]]]}}" class="submit data-v-67f73f95" catchtap="__e">保存</view></view></tn-popup><tn-popup bind:input="__e" vue-id="d21d97de-9" mode="bottom" borderRadius="{{40}}" safeAreaInsetBottom="{{true}}" value="{{showEdit}}" data-event-opts="{{[['^input',[['__set_model',['','showEdit','$event',[]]]]]]}}" class="data-v-67f73f95" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center tn-text-bold data-v-67f73f95" style="width:750rpx;height:120rpx;padding:30rpx;box-sizing:border-box;background:url('../../static/pop_head.png') no-repeat;background-size:100% 100%;"><view style="font-size:34rpx;" class="data-v-67f73f95">修改笔记</view></view><view class="scroll_warp2 tn-width-full data-v-67f73f95"><tn-input bind:input="__e" vue-id="{{('d21d97de-10')+','+('d21d97de-9')}}" placeholder="开始输入..." clearable="{{false}}" type="textarea" border="{{false}}" height="{{324}}" autoHeight="{{false}}" value="{{note_value}}" data-event-opts="{{[['^input',[['__set_model',['','note_value','$event',[]]]]]]}}" class="data-v-67f73f95" bind:__l="__l"></tn-input></view><view class="tn-flex tn-flex-col-top tn-flex-row-center data-v-67f73f95" style="height:150rpx;"><view data-event-opts="{{[['tap',[['submitNoteEdit']]]]}}" class="submit data-v-67f73f95" catchtap="__e">修改</view></view></tn-popup><tn-popup bind:input="__e" vue-id="d21d97de-11" mode="bottom" borderRadius="{{40}}" safeAreaInsetBottom="{{true}}" value="{{showColl}}" data-event-opts="{{[['^input',[['__set_model',['','showColl','$event',[]]]]]]}}" class="data-v-67f73f95" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center tn-text-bold data-v-67f73f95" style="width:750rpx;height:120rpx;padding:30rpx;box-sizing:border-box;background:url('../../static/pop_head.png') no-repeat;background-size:100% 100%;"><view style="font-size:34rpx;" class="data-v-67f73f95">采纳笔记</view></view><view class="scroll_warp2 tn-width-full data-v-67f73f95"><tn-input bind:input="__e" vue-id="{{('d21d97de-12')+','+('d21d97de-11')}}" placeholder="开始输入..." clearable="{{false}}" type="textarea" border="{{false}}" height="{{324}}" autoHeight="{{false}}" value="{{note_value}}" data-event-opts="{{[['^input',[['__set_model',['','note_value','$event',[]]]]]]}}" class="data-v-67f73f95" bind:__l="__l"></tn-input></view><view class="tn-flex tn-flex-col-top tn-flex-row-center data-v-67f73f95" style="height:150rpx;"><view data-event-opts="{{[['tap',[['submitNoteColl']]]]}}" class="submit data-v-67f73f95" catchtap="__e">修改</view></view></tn-popup></view>