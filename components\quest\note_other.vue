<template>
	<view class="note_model tn-width-full">
		<view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
			<view class="name">
				学员：{{item.user_nick}}
			</view>
			<view class="time">
				{{item.create_time}}
			</view>
		</view>
		<view class="tn-width-full note_inner">
			{{item.content}}
		</view>
		<view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
			<view class="note_f_l tn-flex tn-flex-col-center" v-if="item.type==0">
				<view class="r_dot"></view> 题笔记
			</view>
			<view class="note_f_l tn-flex tn-flex-col-center" style="color: #5552FF;" v-if="item.type==1">
				<view class="r_dot" style="background-color: #5552FF;"></view> 章笔记
			</view>
			<view class="note_f_r tn-flex tn-flex-col-center" @click.stop="toCollect()">
				<image src="../../static/icon/no_coll.png" mode="widthFix" style="width: 32rpx;margin-right: 10rpx;">
				</image>
				<view style="color: #666666;font-size: 28rpx;">
					采纳<text v-if="item.accept_num>0">-{{item.accept_num}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "note_other",
		props: {
			item: {
				type: Object,
				default: () => {}
			},
		},
		data() {
			return {

			};
		},
		methods: {
			toCollect() {
				this.$emit("toCollect", this.item)
			},
		}
	}
</script>

<style lang="scss" scoped>
	.note_f_l {
		color: #5552FF;
		font-size: 28rpx;
	}

	.b_dot {
		width: 14rpx;
		height: 14rpx;
		border-radius: 50%;
		background-color: #5552FF;
		margin-right: 8rpx;
	}

	.note_inner {
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.name {
		font-size: 28rpx;
		color: #999999;
	}

	.time {
		font-size: 24rpx;
		color: #999999;
	}

	.note_model {
		padding: 30rpx 0rpx;
		box-sizing: border-box;
		border-bottom: 2rpx solid #F7F7F7;
	}
</style>