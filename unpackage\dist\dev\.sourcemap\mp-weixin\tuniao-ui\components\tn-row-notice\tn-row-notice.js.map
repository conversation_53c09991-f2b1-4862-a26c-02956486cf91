{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-row-notice/tn-row-notice.vue?745d", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-row-notice/tn-row-notice.vue?f31e", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-row-notice/tn-row-notice.vue?b3fa", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-row-notice/tn-row-notice.vue?aa8c", "uni-app:///tuniao-ui/components/tn-row-notice/tn-row-notice.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-row-notice/tn-row-notice.vue?53ce", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-row-notice/tn-row-notice.vue?b1f1"], "names": ["name", "mixins", "props", "list", "type", "default", "show", "playStatus", "leftIcon", "leftIconName", "leftIconSize", "rightIcon", "rightIconName", "rightIconSize", "closeBtn", "radius", "padding", "autoplay", "speed", "computed", "fontStyle", "style", "noticeStyle", "data", "textWidth", "textBoxWidth", "animationDuration", "animationPlayState", "showText", "watch", "handler", "immediate", "methods", "initNotice", "uni", "in", "select", "boundingClientRect", "exec", "resolve", "query", "Promise", "setTimeout", "click", "close", "clickLeftIcon", "clickRightIcon"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChCA;AAAA;AAAA;AAAA;AAAynB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACyD7oB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;EACAA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;EACA;EACAc;IACAC;MAAA;MACA;QACA;QACAC;QACAA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QAEA;MACA;IACA;IACAC;MACA;MACAD;MACA;MACA;IACA;EACA;EACAE;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA1B;MACA2B;QAAA;QACA;QACA;UACA;QACA;MACA;MACAC;IACA;IACAxB;MACA,+DACA;IACA;IACAW;MACA;IACA;EACA;EACAc;IACA;IACAC;MAAA;MACA;QACAR;QACAD;MACA;QACAU,0BACAC,WACAC,kCACAC,qBACAC;UACA;UACAC;QACA;MACA;MACAC;MAEAC;QACA;QACA;QACA;QACA;QACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC/OA;AAAA;AAAA;AAAA;AAAwsC,CAAgB,8nCAAG,EAAC,C;;;;;;;;;;;ACA5tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-row-notice/tn-row-notice.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-row-notice.vue?vue&type=template&id=770a6f7a&scoped=true&\"\nvar renderjs\nimport script from \"./tn-row-notice.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-row-notice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-row-notice.vue?vue&type=style&index=0&id=770a6f7a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"770a6f7a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-row-notice/tn-row-notice.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-row-notice.vue?vue&type=template&id=770a6f7a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.show ? _vm.__get_style([_vm.noticeStyle]) : null\n  var s1 =\n    _vm.show && _vm.leftIcon\n      ? _vm.__get_style([_vm.fontStyle(\"leftIcon\")])\n      : null\n  var s2 = _vm.show ? _vm.__get_style([_vm.fontStyle()]) : null\n  var s3 =\n    _vm.show && _vm.rightIcon\n      ? _vm.__get_style([_vm.fontStyle(\"rightIcon\")])\n      : null\n  var s4 =\n    _vm.show && _vm.closeBtn ? _vm.__get_style([_vm.fontStyle(\"close\")]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n        s4: s4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-row-notice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-row-notice.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view\r\n    v-if=\"show\"\r\n    class=\"tn-row-notice-class tn-row-notice\"\r\n    :class=\"[backgroundColorClass]\"\r\n    :style=\"[noticeStyle]\"\r\n  >\r\n    <view class=\"tn-row-notice__wrap\">\r\n      <!-- 左图标 -->\r\n      <view class=\"tn-row-notice__icon\">\r\n        <view\r\n          v-if=\"leftIcon\"\r\n          class=\"tn-row-notice__icon--left\" \r\n          :class=\"[`tn-icon-${leftIconName}`,fontColorClass]\"\r\n          :style=\"[fontStyle('leftIcon')]\"\r\n          @tap=\"clickLeftIcon\"></view>\r\n      </view>\r\n      \r\n      <!-- 消息体 -->\r\n      <view class=\"tn-row-notice__content-box\" id=\"tn-row-notice__content-box\">\r\n        <view\r\n          class=\"tn-row-notice__content\"\r\n          id=\"tn-row-notice__content\"\r\n          :style=\"{\r\n            animationDuration: animationDuration,\r\n            animationPlayState: animationPlayState\r\n          }\"\r\n        >\r\n          <text\r\n            class=\"tn-row-notice__content--text\"\r\n            :class=\"[fontColorClass]\"\r\n            :style=\"[fontStyle()]\"\r\n            @tap=\"click\"\r\n          >{{ showText }}</text>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 右图标 -->\r\n      <view class=\"tn-row-notice__icon\">\r\n        <view\r\n          v-if=\"rightIcon\"\r\n          class=\"tn-row-notice__icon--right\" \r\n          :class=\"[`tn-icon-${rightIconName}`,fontColorClass]\"\r\n          :style=\"[fontStyle('rightIcon')]\"\r\n          @tap=\"clickRightIcon\"></view>\r\n        <view\r\n          v-if=\"closeBtn\"\r\n          class=\"tn-row-notice__icon--right\" \r\n          :class=\"[`tn-icon-close`,fontColorClass]\"\r\n          :style=\"[fontStyle('close')]\"\r\n          @tap=\"close\"></view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import componentsColorMixin from '../../libs/mixin/components_color.js'\r\n  export default {\r\n    name: 'tn-row-notice',\r\n    mixins: [componentsColorMixin],\r\n    props: {\r\n      // 显示的内容\r\n      list: {\r\n        type: Array,\r\n        default() {\r\n          return []\r\n        }\r\n      },\r\n      // 是否显示\r\n      show: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 播放状态\r\n      // play -> 播放 paused -> 暂停\r\n      playStatus: {\r\n        type: String,\r\n        default: 'play'\r\n      },\r\n      // 是否显示左边图标\r\n      leftIcon: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 左边图标的名称\r\n      leftIconName: {\r\n        type: String,\r\n        default: 'sound'\r\n      },\r\n      // 左边图标的大小\r\n      leftIconSize: {\r\n        type: Number,\r\n        default: 34\r\n      },\r\n      // 是否显示右边的图标\r\n      rightIcon: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 右边图标的名称\r\n      rightIconName: {\r\n        type: String,\r\n        default: 'right'\r\n      },\r\n      // 右边图标的大小\r\n      rightIconSize: {\r\n        type: Number,\r\n        default: 26\r\n      },\r\n      // 是否显示关闭按钮\r\n      closeBtn: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 圆角\r\n      radius: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // 内边距\r\n      padding: {\r\n        type: String,\r\n        default: '18rpx 24rpx'\r\n      },\r\n      // 自动播放\r\n      autoplay: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 水平滚动时的速度，即每秒滚动多少rpx\r\n      speed: {\r\n        type: Number,\r\n        default: 160\r\n      }\r\n    },\r\n    computed: {\r\n      fontStyle() {\r\n        return (type) => {\r\n          let style = {}\r\n          style.color = this.fontColorStyle ? this.fontColorStyle : ''\r\n          style.fontSize = this.fontSizeStyle ? this.fontSizeStyle : ''\r\n          if (type === 'leftIcon' && this.leftIconSize) {\r\n            style.fontSize = this.leftIconSize + 'rpx'\r\n          }\r\n          if (type === 'rightIcon' && this.rightIconSize) {\r\n            style.fontSize = this.rightIconSize + 'rpx'\r\n          }\r\n          if (type === 'close') {\r\n            style.fontSize = '24rpx'\r\n          }\r\n          \r\n          return style\r\n        }\r\n      },\r\n      noticeStyle() {\r\n        let style = {}\r\n        style.backgroundColor = this.backgroundColorStyle ? this.backgroundColorStyle : 'transparent'\r\n        if (this.padding) style.padding = this.padding\r\n        return style\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        // 滚动文字的宽度\r\n        textWidth: 0,\r\n        // 存放滚动文字的盒子的宽度\r\n        textBoxWidth: 0,\r\n        // 动画执行的时间\r\n        animationDuration: '10s',\r\n        // 动画执行状态\r\n        animationPlayState: 'paused',\r\n        // 当前显示的文本\r\n        showText: ''\r\n      }\r\n    },\r\n    watch: {\r\n      list: {\r\n        handler(value) {\r\n          this.showText = value.join('，')\r\n          this.$nextTick(() => {\r\n            this.initNotice()\r\n          })\r\n        },\r\n        immediate: true\r\n      },\r\n      playStatus(value) {\r\n        if (value === 'play') this.animationPlayState = 'running'\r\n        else this.animationPlayState = 'paused'\r\n      },\r\n      speed(value) {\r\n        this.initNotice()\r\n      }\r\n    },\r\n    methods: {\r\n      // 初始化通知栏\r\n      initNotice() {\r\n        let query = [],\r\n          textBoxWidth = 0,\r\n          textWidth = 0;\r\n        let textQuery = new Promise((resolve, reject) => {\r\n          uni.createSelectorQuery()\r\n            .in(this)\r\n            .select(`#tn-row-notice__content`)\r\n            .boundingClientRect()\r\n            .exec(ret => {\r\n              this.textWidth = ret[0].width\r\n              resolve()\r\n            })\r\n        })\r\n        query.push(textQuery)\r\n        \r\n        Promise.all(query).then(() => {\r\n          // 根据t=s/v(时间=路程/速度)，这里为何不需要加上#tn-row-notice__content-box的宽度，因为设置了.tn-row-notice__content样式中设置了padding-left: 100%\r\n          this.animationDuration = `${this.textWidth / uni.upx2px(this.speed)}s`\r\n          // 这里必须这样开始动画，否则在APP上动画速度不会改变(HX版本2.4.6，IOS13)\r\n          this.animationPlayState = 'paused'\r\n          setTimeout(() => {\r\n            if (this.autoplay && this.playStatus === 'play') this.animationPlayState = 'running'\r\n          }, 10)\r\n        })\r\n      },\r\n      // 点击了通知栏\r\n      click() {\r\n        this.$emit('click')\r\n      },\r\n      // 点击了关闭按钮\r\n      close() {\r\n        this.$emit('close')\r\n      },\r\n      // 点击了左边图标\r\n      clickLeftIcon() {\r\n        this.$emit('clickLeft')\r\n      },\r\n      // 点击了右边图标\r\n      clickRightIcon() {\r\n        this.$emit('clickRight')\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  \r\n  .tn-row-notice {\r\n    width: 100%;\r\n    overflow: hidden;\r\n    \r\n    &__wrap {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n    }\r\n    \r\n    &__content {\r\n      &-box {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: row;\r\n        overflow: hidden;\r\n        margin-left: 12rpx;\r\n      }\r\n      \r\n      display: flex;\r\n      flex-direction: row;\r\n      flex-wrap: nowrap;\r\n      text-align: right;\r\n      // 为了能滚动起来\r\n      padding-left: 100%;\r\n      animation: tn-notice-loop-animation 10s linear infinite both;\r\n      \r\n      &--text {\r\n        word-break: keep-all;\r\n        white-space: nowrap;\r\n      }\r\n    }\r\n    \r\n    &__icon {\r\n      &--left {\r\n        display: inline-flex;\r\n        align-items: center;\r\n      }\r\n      \r\n      &--right {\r\n        margin-left: 12rpx;\r\n        display: inline-flex;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n  \r\n  @keyframes tn-notice-loop-animation {\r\n    0% {\r\n      transform: translateX(0);\r\n    }\r\n    100% {\r\n      transform: translateX(-100%);\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-row-notice.vue?vue&type=style&index=0&id=770a6f7a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-row-notice.vue?vue&type=style&index=0&id=770a6f7a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980406026\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}