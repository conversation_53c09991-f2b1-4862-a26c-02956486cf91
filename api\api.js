export const login = "/api/user/login"
export const setUser = "/api/user/setUserNickAndAvatar" //设置头像
export const systemData = "/api/index/index" //系统数据
export const getUserInfo = "/api/user/getUserInfo" //系统数据
export const activeCode = "/api/user/active" //解锁code
export const shareActive = "/api/user/share" //分享解锁
export const uploadExam = "/api/index/uploadExam" //上传试题
export const cancelVipRea = "/api/user/getCancelReason" //会员取消原因
// 书籍题目=============================
export const bookList = "/api/topic/getBookList" //书籍列表
export const bookCate = "/api/topic/getTrueTopicCateList" //分类
export const bookTem = "/api/topic/getBookTemplateList" //书籍模板
export const bookDetail = "/api/topic/getBookCoverInfo" //书籍详情
export const bookNum = "/api/topic/setBookNumber" //设置书籍几刷
export const chapterList = "/api/topic/getChapterList" //章节目录
export const questList = "/api/topic/getTopicList" //题目列表
export const submitAnswer = "/api/topic/submitTopic" //答题提交
export const answerRes = "/api/topic/getChapterRecordInfo" //答题信息
export const getSolution = "/api/topic/getSolution" //答案速查
export const bookRateList = "/api/topic/getChapterCorrectRatio" //章节正确率
export const bookTemRateList = "/api/topic/getTemplateCorrectRatio" //章节模板率
export const analysisTopicList = "/api/record/getAnalysisTopicList" //题目解析
export const errorCorrection = "/api/topic/errorCorrection" //题目反馈
// 笔记=================================
export const addNote = "/api/note/writeNotes" //添加笔记
export const editNote = "/api/note/editNotes" //修改笔记
export const delNote = "/api/note/delNotes" //删除笔记
export const acceptNote = "/api/note/acceptNotes" //采纳笔记
export const listNote = "/api/note/getTopicNotes" //题目笔记
export const listBookNote = "/api/note/getBookNotes" //书籍笔记
export const listBookNoteDetail = "/api/note/getChapterNotes" //章节笔记
export const getTopicList = "/api/note/getTopicList" //章节笔记
export const topicRecord = "/api/topic/getTopicRecord" //回答记录
// 错题=================================
export const errorCateList = "/api/record/errorRecord" //错题模块
export const errorQuestList = "/api/record/getErrorTopicList" //错题列表
export const errorQuestSub = "/api/record/redoTopic" //错题重做
export const getAllErrorTopicList = "/api/record/getAllErrorTopicList" //错题重做乱序
export const outOrderRedoTopic = "/api/record/outOrderRedoTopic" //错题重做乱序
export const errorQuestExport = "/api/record/exportPdf" //错题导出
export const errorQuestAnalysis = "/api/topic/getErrorTopicList" //错题解析

// 背诵=================================
export const reciteList = "/api/recite/reciteList" //背诵列表
export const reciteTemList = "/api/recite/getTemplateInfo" //背诵模板列表
export const reciteChapterList = "/api/recite/getChapterList" //背诵章节列表
export const reciteExamList = "/api/recite/getExamList" //背诵考点列表
export const reciteSubmitExam = "/api/recite/submitExam" //掌握考点列表
// 发现=================================
export const goodsList = "/api/goods/getGoodsList"
// 收藏=================================
export const addColl = "/api/collect/collect" //添加收藏
export const bookCollect = "/api/collect/getCollectChapterList" //书籍收藏题目所在章节列表
export const questCollect = "/api/collect/getCollectTopicList" //章节收藏题目列表
// 分享=================================
export const getPublicConfig = "/api/index/getPublicConfig" //获取公共配置