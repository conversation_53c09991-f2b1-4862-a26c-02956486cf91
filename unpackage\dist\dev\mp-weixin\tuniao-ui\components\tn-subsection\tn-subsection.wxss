@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.tn-subsection.data-v-d12b2624 {
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;
  position: relative;
}
.tn-subsection__item.data-v-d12b2624 {
  display: flex;
  flex-direction: row;
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  height: 100%;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  padding: 0 6rpx;
}
.tn-subsection__item--text.data-v-d12b2624 {
  transition: all 0.3s;
  color: #FFFFFF;
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  z-index: 3;
}
.tn-subsection__item--first.data-v-d12b2624 {
  border-top-left-radius: 8rpx;
  border-bottom-left-radius: 8rpx;
}
.tn-subsection__item--last.data-v-d12b2624 {
  border-top-right-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
}
.tn-subsection__item--none-border-right.data-v-d12b2624 {
  border-right: none !important;
}
.tn-subsection__bg.data-v-d12b2624 {
  background-color: #01BEFF;
  position: absolute;
  z-index: -1;
  transition-property: all;
  transition-duration: 0s;
  transition-timing-function: linear;
}
.tn-subsection__bg__animation.data-v-d12b2624 {
  transition-duration: 0.25s !important;
}
.tn-subsection__bg__animation--cubic-bezier.data-v-d12b2624 {
  transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
}

