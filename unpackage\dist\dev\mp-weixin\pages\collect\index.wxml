<view class="data-v-4f224584"><view class="top_model tn-width-full data-v-4f224584"><view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center data-v-4f224584"><view class="exercise_info tn-height-full tn-flex tn-flex-direction-column tn-flex-row-between data-v-4f224584"><view class="name tn-text-ellipsis-2 data-v-4f224584" style="width:380rpx;">{{''+name+''}}</view></view><view class="subsect data-v-4f224584"><tn-subsection vue-id="d5b227fc-1" list="{{list}}" height="{{72}}" inactiveColor="#5552FF" buttonColor="#5552FF" current="{{current}}" data-event-opts="{{[['^change',[['changeSub']]]]}}" bind:change="__e" class="data-v-4f224584" bind:__l="__l"></tn-subsection></view></view><view class="tn-width-full data-v-4f224584" style="margin-top:30rpx;"><scroll-view class="scroll-view-x data-v-4f224584" scroll-x="true"><block wx:for="{{list_type}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['changeTem',['$0'],[[['list_type','',index]]]]]]]}}" class="{{['scroll-view-item','data-v-4f224584',item.template_id==templateId?'sel_tem':'']}}" catchtap="__e">{{''+item.template_name+''}}</view></block></scroll-view></view></view><view class="body data-v-4f224584"><block wx:for="{{collectList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tn-width-full data-v-4f224584" style="margin-bottom:20rpx;"><collect-item vue-id="{{'d5b227fc-2-'+index}}" item="{{item}}" data-event-opts="{{[['^toQuest',[['toQuest']]]]}}" bind:toQuest="__e" class="data-v-4f224584" bind:__l="__l"></collect-item></view></block></view></view>