<template>
	<view style="padding-bottom: 140rpx;">
		<view class="quest_primary tn-bg-white">
			<view class="tn-width-full tn-flex" style="margin-bottom: 30rpx;">
				<view style="width: 600rpx;">
					<tn-line-progress :percent="percent" :height="20" activeColor="#5552FF"
						inactiveColor="#F5F5F5"></tn-line-progress>
				</view>
				<text style="margin-left: 40rpx; font-size: 28rpx;color: #999999;"> <text
						style="color: #FF000A;">{{questIndex+1}}</text> <text style="margin: 0rpx 5rpx;">/</text>
					{{questList.length}} </text>
			</view>
			<view class="qp_top tn-flex tn-flex-row-between tn-flex-col-center">
				<view class="qpt_left tn-flex tn-flex-col-center">
					<view class="type_tips">
						<text v-if="questItem.type==0">单选题</text>
						<text v-if="questItem.type==1">多选题</text>
						<text v-if="questItem.type==2">不定项</text>
					</view>
				</view>
				<view class="tn-flex tn-flex-col-center">
					<view style="font-size: 28rpx;color: #A1A1A1;" v-if="questItem.is_collect==1"
						@click.stop="toColl()">
						<text class="tn-icon-star-fill" style="color: #FFBD23;"></text> 已收藏
					</view>
					<view style="font-size: 28rpx;color: #A1A1A1;" v-if="questItem.is_collect==0"
						@click.stop="toColl()">
						<text class="tn-icon-star-fill" style="color: #A1A1A1;"></text> 收藏
					</view>
					<view class="correction" @click.stop="showError=true">
						<text class="tn-icon-help-fill" style="margin-right: 4rpx;"></text> 纠错
					</view>
				</view>
			</view>
			<view class="quest_warp">
				<view class="quest_title">
					{{questItem.title_id+"、"}}{{questItem.title||''}}
				</view>
				<view class="tn-width-full" v-if="questItem.title_img&&questItem.title_img.length>0"
					style="margin-top: 20rpx;margin-bottom: 20rpx;">
					<view v-for="(item,index) in questItem.title_img" :key="index" class="tn-width-full">
						<image :src="baseUrl+item" mode="widthFix" style="width: 100%;"></image>
					</view>
				</view>
				<view class="tn-width-full" v-if="questItem.type==0">
					<answer-com v-if="questItem.type==0" ref="exeAnsCom" @selAnswer="nextQuestBe"></answer-com>
				</view>
				<view class="tn-width-full" v-if="questItem.type==1||questItem.type==2">
					<answer-com-tum ref="exeAnsComTum" @selAnswer="nextQuestTumBe"></answer-com-tum>
				</view>
			</view>
		</view>

		<view class="answer_history" v-if="answerAll[questItem.id]!=null">
			<tn-tabs :list="list_type" bold :isScroll="false" activeColor="#333333" inactiveColor="#333333"
				:current="current" name="name" @change="change"></tn-tabs>
			<view class="tn-width-full" v-if="current==0&&questItem.analysis_img&&questItem.analysis_img.length>0"
				style="margin-top: 20rpx;margin-bottom: 20rpx;">
				<view v-for="(item,index) in questItem.analysis_img" :key="index" class="tn-width-full">
					<image :src="baseUrl+item" mode="widthFix" style="width: 100%;"></image>
				</view>
			</view>
			<view style="margin-top: 40rpx;" v-if="current==0">
				<text user-select>{{questItem.analysis}}</text>
			</view>
			<view style="margin-top: 30rpx;" class="" v-if="current==1">
				<view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
					<view class="tn-flex tn-flex-col-center">
						<view :class="is_sort==0?'is_sel':'no_sel'" @click.stop="changeSort('0')">
							采纳数排序
						</view>
						<view style="margin: 0rpx 10rpx;color: #D8D8D8;">
							|
						</view>
						<view :class="is_sort==1?'is_sel':'no_sel'" @click.stop="changeSort('1')">
							时间排序
						</view>
					</view>
					<view class="add_note tn-flex tn-flex-col-center tn-flex-row-center" @click.stop="showAdd=true">
						<image src="../../static/icon/add_note.png" mode="widthFix"
							style="width: 30rpx;margin-right: 2rpx;"></image>
						<view class="">
							做笔记
						</view>
					</view>
				</view>
				<view class="tn-width-full">
					<view v-for="(item,index) in noteList" :key="index" class="tn-width-full">
						<note-other :item="item" @toCollect="toCollectOther"></note-other>
					</view>
				</view>
			</view>
			<view style="margin-top: 30rpx;" class="" v-if="current==2">
				<view class="mine_note_top tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
					<view class="tn-flex tn-flex-col-center" style="font-size: 24rpx;color: #333333;">
						好记性不如烂笔头~
					</view>
					<view class="tn-flex tn-flex-col-center tn-flex-row-center" @click.stop="showAdd=true">
						<image src="../../static/icon/add_note.png" mode="widthFix"
							style="width: 30rpx;margin-right: 2rpx;"></image>
						<view style="color: #5552FF;font-size: 24rpx;">
							做笔记
						</view>
					</view>
				</view>
				<view class="tn-width-full">
					<view v-for="(item,index) in noteList" :key="index" class="tn-width-full">
						<note-mine :item="item" @todel="toMineDel" @toedit="toMineEdit"></note-mine>
					</view>
				</view>
			</view>
		</view>
		<quest-fixed-rem :end="isEnd" @nextQuest="nextQuestBefore" @lastQuest="lastQuestBefore"
			@showCard="showCard=true" @submitNow="submitNow" @submitEnd="submitEnd"></quest-fixed-rem>
		<tn-popup v-model="showCard" mode="bottom" :borderRadius="40" safeAreaInsetBottom>
			<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				<text class="tn-text-bold">
					答题卡
				</text>
				<view class="tn-flex tn-flex-col-center tn-flex-row-between" style="width: 300rpx;">
					<view class="tn-flex tn-flex-col-center">
						<view class="cri_red">
						</view>
						<view style="font-size: 24rpx;color: #333333;">
							已交
						</view>
					</view>
					<view class="tn-flex tn-flex-col-center">
						<view class="cri_blue">
						</view>
						<view style="font-size: 24rpx;color: #333333;">
							当前
						</view>
					</view>
					<view class="tn-flex tn-flex-col-center">
						<view class="cri_gary">
						</view>
						<view style="font-size: 24rpx;color: #333333;">
							未答
						</view>
					</view>
				</view>
			</view>
			<view class="scroll_warp">
				<scroll-view scroll-y="true" style="width: 100%;height: 100%;">
					<view class="tn-flex tn-flex-wrap tn-flex-row-between">
						<view v-for="(item,index) in questList" :key="index"
							:class="questIndex!=index?answerAll[item.topic_info.id]!==null?'card_sub':'card_no':'card_now'"
							@click.stop="jumpQuest(index)">
							{{item.topic_info.title_id}}
						</view>
						<view v-for="(item,index) in 5-questList.length%5" :key="'a'+index"
							style="width: 100rpx;margin-bottom: 40rpx;margin-left: 20rpx;">
						</view>
					</view>
				</scroll-view>
			</view>
		</tn-popup>
		<tn-popup v-model="showError" mode="bottom" :borderRadius="40" safeAreaInsetBottom>
			<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				<text class="tn-text-bold">
					反馈
				</text>
			</view>
			<view class="tn-width-full tn-flex tn-flex-direction-column"
				style="padding:0rpx 30rpx 30rpx 30rpx;box-sizing: border-box;">
				<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between">
					<view :class="selError=='有错别字'?'sel_is':'sel_no'" @click.stop="selError='有错别字'">
						有错别字
					</view>
					<view :class="selError=='题干有误'?'sel_is':'sel_no'" @click.stop="selError='题干有误'">
						题干有误
					</view>
					<view :class="selError=='答案有误'?'sel_is':'sel_no'" @click.stop="selError='答案有误'">
						答案有误
					</view>
				</view>
				<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between" style="margin-top: 30rpx;">
					<view :class="selError=='解析有误'?'sel_is':'sel_no'" @click.stop="selError='解析有误'">
						解析有误
					</view>
					<view :class="selError=='解析缺失'?'sel_is':'sel_no'" @click.stop="selError='解析缺失'">
						解析缺失
					</view>
					<view :class="selError=='选择有误'?'sel_is':'sel_no'" @click.stop="selError='选择有误'">
						选择有误
					</view>
				</view>
			</view>
			<view class="scroll_warp2 tn-width-full">
				<tn-input v-model="err_value" placeholder="开始输入..." :clearable="false" type="textarea" :border="false"
					:height="324" :autoHeight="false" />
			</view>
			<view class="tn-flex tn-flex-col-top tn-flex-row-center" style="height: 150rpx;">
				<view class="submit" @click.stop="submitErrorQuest()">立即反馈</view>
			</view>
		</tn-popup>
		<tn-popup v-model="showAdd" mode="bottom" :borderRadius="40" safeAreaInsetBottom>
			<view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				<view style="font-size: 34rpx;" v-if="noteType==1">
					章笔记
				</view>
				<view style="font-size: 34rpx;" v-if="noteType==0">
					试题笔记
				</view>
				<view class="tn-flex tn-flex-row-center tn-flex-col-center"
					style="width: 180rpx;height: 48rpx;border-radius: 55rpx;background-color: #E3E3FF; color: #5552FF;font-size: 24rpx;"
					v-if="noteType==1" @click.stop="noteType=0">
					<text class="tn-icon-edit" style="font-size: 32rpx;margin-right: 5rpx;"></text>
					试题笔记
				</view>
				<view class="tn-flex tn-flex-row-center tn-flex-col-center"
					style="width: 180rpx;height: 48rpx;border-radius: 55rpx;background-color: #E3E3FF; color: #5552FF;font-size: 24rpx;"
					v-if="noteType==0" @click.stop="noteType=1">
					<text class="tn-icon-edit" style="font-size: 32rpx;margin-right: 5rpx;"></text>
					章笔记
				</view>
			</view>
			<view class="scroll_warp2 tn-width-full">
				<tn-input v-model="note_value" placeholder="开始输入..." :clearable="false" type="textarea" :border="false"
					:height="324" :autoHeight="false" />
			</view>
			<view class="tn-flex tn-flex-col-top tn-flex-row-center" style="height: 150rpx;">
				<view class="submit" @click.stop="submitNote()">保存</view>
			</view>
		</tn-popup>
		<tn-popup v-model="showEdit" mode="bottom" :borderRadius="40" safeAreaInsetBottom>
			<view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				<view style="font-size: 34rpx;">
					修改笔记
				</view>
			</view>
			<view class="scroll_warp2 tn-width-full">
				<tn-input v-model="note_value" placeholder="开始输入..." :clearable="false" type="textarea" :border="false"
					:height="324" :autoHeight="false" />
			</view>
			<view class="tn-flex tn-flex-col-top tn-flex-row-center" style="height: 150rpx;">
				<view class="submit" @click.stop="submitNoteEdit()">修改</view>
			</view>
		</tn-popup>
		<tn-popup v-model="showColl" mode="bottom" :borderRadius="40" safeAreaInsetBottom>
			<view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				<view style="font-size: 34rpx;">
					采纳笔记
				</view>
			</view>
			<view class="scroll_warp2 tn-width-full">
				<tn-input v-model="note_value" placeholder="开始输入..." :clearable="false" type="textarea" :border="false"
					:height="324" :autoHeight="false" />
			</view>
			<view class="tn-flex tn-flex-col-top tn-flex-row-center" style="height: 150rpx;">
				<view class="submit" @click.stop="submitNoteColl()">修改</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	import answerCom from "@/components/exercise/answer_com.vue"
	import answerComTum from "@/components/exercise/answer_com_tum.vue"
	import questFixedRem from "@/components/exercise/quest_fixed_rem.vue"
	import noteOther from "@/components/quest/note_other.vue"
	import noteMine from "@/components/quest/note_mine.vue"
	export default {
		components: {
			answerCom,
			questFixedRem,
			answerComTum,
			noteOther,
			noteMine,
		},
		data() {
			return {
				exerId: null,
				questList: [],
				questItem: {},
				questIndex: -1,
				cardIndex: null,
				answerAll: {},
				percent: 0,
				showCard: false,
				showError: false,
				showAdd: false,
				showEdit: false,
				showColl: false,
				noteType: 0,
				note_value: "",
				list_type: [{
					name: '解析'
				}, {
					name: '研友笔记'
				}, {
					name: '我的笔记',
				}],
				current: 0,
				isEnd: 0,
				baseUrl: this.$config.baseUrl,
				noteList: [],
				page: 1,
				more: true,
				is_sort: '0',
				editItem: {},
				collItem: {},
				selError: '',
				err_value: ''
			};
		},
		onLoad(options) {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			if (options.id) {
				this.exerId = options.id
				this.toQuest()
			}
		},

		onReachBottom() {
			if (this.more) {
				this.page += 1
				this.getListNote()
			}
		},
		methods: {
			toColl() {
				this.$http.post(this.$api.addColl, {
					topic_id: this.questItem.id
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: "操作成功",
							icon: "none"
						})
						if (this.questItem.is_collect == 1) {
							this.questItem.is_collect = 0
						} else {
							this.questItem.is_collect = 1
						}
					}
				})
			},
			submitErrorQuest() {
				if (!this.selError) {
					uni.showToast({
						title: "请选择反馈类目",
						icon: "none"
					})
					return false
				}
				if (!this.err_value) {
					uni.showToast({
						title: "请输入反馈内容",
						icon: "none"
					})
					return false
				}
				let data = {
					title: this.selError,
					content: this.err_value,
					topic_id: this.questItem.id
				}
				this.$http.post(this.$api.errorCorrection, data).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: "提交成功",
							icon: "success"
						})
						this.selError = ''
						this.err_value = ''
						this.showError = false
					}
				})
			},
			submitNoteColl() {
				let data = {
					content: this.note_value,
					id: this.collItem.id,
				}
				this.$http.post(this.$api.acceptNote, data).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: "采纳成功",
							icon: "none"
						})
						this.note_value = ""
						this.collItem = {}
						this.showColl = false
						this.getNoteBefore()
					}
				})
			},
			toCollectOther(item) {
				this.collItem = item
				this.note_value = item.content
				this.showColl = true
			},
			getNoteBefore() {
				this.page = 1
				this.noteList = []
				this.more = true
				this.getListNote()
			},
			toMineDel(item) {
				let that = this
				uni.showModal({
					title: "确认删除当前笔记吗？",
					success: (suc) => {
						if (suc.confirm) {
							that.$http.post(that.$api.delNote, {
								id: item.id
							}).then(res => {
								if (res.code == 200) {
									uni.showToast({
										title: "已删除",
										icon: "none"
									})
									that.getNoteBefore()
								}
							})
						}
					}
				})
			},
			toMineEdit(item) {
				this.editItem = item
				this.note_value = item.content
				this.showEdit = true
			},
			changeSort(num) {
				if (num != this.is_sort) {
					this.is_sort = num
					this.getNoteBefore()
				}
			},
			submitNoteEdit() {
				let data = {
					content: this.note_value,
					id: this.editItem.id,
				}
				this.$http.post(this.$api.editNote, data).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: "已修改",
							icon: "none"
						})
						this.note_value = ""
						this.editItem = {}
						this.showEdit = false
						this.getNoteBefore()
					}
				})
			},
			submitNote() {
				let data = {
					content: this.note_value,
					type: this.noteType,
				}
				if (this.noteType == 0) {
					data.topic_id = this.questItem.id
				}
				if (this.noteType == 1) {
					data.chapter_id = this.exerId
				}
				this.$http.post(this.$api.addNote, data).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: "已新增",
							icon: "none"
						})
						this.note_value = ""
						this.showAdd = false
						this.getNoteBefore()
					}
				})
			},
			getListNote() {
				let data = {
					page: this.page,
					limit: 10,
					topic_id: this.questItem.id,
					sort: this.is_sort
				}
				if (this.current == 1) {
					data.type = "0"
				}
				if (this.current == 2) {
					data.type = "1"
				}
				this.$http.post(this.$api.listNote, data).then(res => {
					if (res.code == 200) {
						if (this.page == 1) {
							this.noteList = res.data.data
						} else {
							if (res.data.data.length == 0) {
								this.more = false
							} else {
								this.noteList = this.noteList.concat(res.data.data)
							}
						}
					}
				})
			},
			submitNow() {
				if (this.answerAll[this.questItem.id]) {
					this.changeQuest(true)
					if (this.questIndex == this.questList.length - 1) {
						this.isEnd = 2
					} else {
						this.isEnd = 1
					}
				} else {
					uni.showToast({
						title: "当前题目未完成",
						icon: "none"
					})
				}
			},
			submitEnd() {
				this.submitBefore()
			},
			change(index) {
				if (this.current != index) {
					this.current = index;
					if (this.current != 0) {
						this.getNoteBefore()
					}
				}
			},
			nextQuestTumBe(item, index) {
				this.answerAll[item.id] = index
			},
			nextQuestBe(item, index) {
				this.answerAll[item.id] = index
			},
			submitBefore() {
				uni.showModal({
					title: "小贴士",
					content: "请认真做好“错题笔记，章节笔记”，对冲刺阶段的查漏补缺有极大帮助",
					showCancel: false,
					confirmText: "确定",
					success: (suc) => {
						if (suc.confirm) {
							uni.navigateBack()
						}
					}
				})
				// let data = []
				// for (let key in this.answerAll) {
				// 	data.push({
				// 		topic_id: key,
				// 		answer: this.answerAll[key]
				// 	})
				// }
				// this.$http.post(this.$api.submitAnswer, {
				// 	chapter_id: this.exerId,
				// 	answers: data
				// }).then(res => {
				// 	if (res.code == 200) {
				// 		this.$publicjs.toUrl("/pages/exercise/exercise_end?chapter_id=" + this.exerId)
				// 	}
				// })
			},
			jumpQuest(index) {
				if (index <= this.cardIndex) {
					this.questIndex = index
					this.questItem = this.questList[this.questIndex].topic_info
					if (this.answerAll[this.questItem.id]) {
						this.changeQuest(true)
						if (this.questIndex == this.questList.length - 1) {
							this.isEnd = 2
						} else {
							this.isEnd = 1
						}
					} else {
						this.changeQuest(false)
						this.isEnd = 0
					}
				} else {
					uni.showToast({
						title: "当前题目未完成",
						icon: "none"
					})
				}
				this.showCard = false
			},
			nextQuestBefore() {
				if (this.answerAll[this.questItem.id] === null) {
					uni.showToast({
						title: "当前题目未完成",
						icon: "none"
					})
				} else {
					if (this.questList.length - 1 == this.questIndex) {} else {
						if (this.isEnd == 1) {
							if (this.questIndex < this.cardIndex) {
								this.isEnd = 1
								this.nextQuest(this.questItem, this.answerAll[this.questItem.id], false)
							} else if (this.questIndex == this.cardIndex) {
								this.isEnd = 1
								this.nextQuest(this.questItem, this.answerAll[this.questItem.id], true)
							}
							if (this.questIndex == this.questList.length - 1) {
								this.isEnd = 2
							}
						} else {
							this.isEnd = 1
							this.nextQuest(this.questItem, this.answerAll[this.questItem.id], false)
						}
					}
				}
			},
			lastQuestBefore() {
				if (this.questIndex == 0) {
					uni.showToast({
						title: "当前已是第一题",
						icon: "none"
					})
				} else {
					this.questIndex -= 1
					this.questItem = this.questList[this.questIndex].topic_info
					this.isEnd = 1
					this.changeQuest(true)
				}
			},
			nextQuest(item, index, isPer = true) {
				this.answerAll[item.id] = index
				if (this.questIndex == 0) {
					this.questIndex += 1
					this.cardIndex = this.questIndex
					this.questItem = this.questList[this.questIndex].topic_info
					if (isPer) {
						let a = Number(Number(Number(this.questIndex + 1) / Number(this.questList.length) * 100)
							.toFixed(
								2))
						if (a > this.percent) {
							this.percent = a
						}
					}
					if (this.answerAll[this.questItem.id]) {
						this.changeQuest(true)
					} else {
						this.changeQuest()
						this.isEnd = 0
					}
				} else if (this.questIndex > 0 && this.questIndex < this.questList.length - 1) {
					this.questIndex += 1
					this.cardIndex = this.questIndex
					this.questItem = this.questList[this.questIndex].topic_info
					if (isPer) {
						let a = Number(Number(Number(this.questIndex + 1) / Number(this.questList.length) * 100)
							.toFixed(
								2))
						if (a > this.percent) {
							this.percent = a
						}
					}
					if (this.answerAll[this.questItem.id]) {
						this.changeQuest(true)
					} else {
						this.changeQuest()
						this.isEnd = 0
					}
				} else if (this.questIndex == this.questList.length - 1) {}

			},
			changeQuest(bal = false) {
				this.current = 0
				this.$nextTick(() => {
					if (this.questItem.type == 0) {
						this.$refs.exeAnsCom.changeData(this.questItem, this.answerAll, bal)
					}
					if (this.questItem.type == 1 || this.questItem.type == 2) {
						this.$refs.exeAnsComTum.changeData(this.questItem, this.answerAll, bal)
					}
				})
			},
			toQuest() {
				this.$http.post(this.$api.questCollect, {
					chapter_id: this.exerId
				}).then(res => {
					if (res.code == 200) {
						this.questList = res.data
						this.questItem = res.data[0].topic_info
						this.questIndex = 0
						this.cardIndex = this.questIndex
						this.changeQuest()
						res.data.forEach(item => {
							this.answerAll[item.id] = null
						})
						this.percent = Number(Number(Number(this.questIndex + 1) / Number(this
								.questList
								.length) *
							100).toFixed(
							2))
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.sel_no {
		width: 200rpx;
		height: 80rpx;
		border-radius: 55rpx;
		background-color: #F1F1F1;
		text-align: center;
		line-height: 80rpx;
		color: #666666;
		font-size: 28rpx;
	}

	.sel_is {
		width: 200rpx;
		height: 80rpx;
		border-radius: 55rpx;
		background-color: #5552FF;
		text-align: center;
		line-height: 80rpx;
		color: #FFFFFF;
		font-size: 28rpx;
	}

	.scroll_warp2 {
		width: 690rpx;
		height: 384rpx;
		border-radius: 20rpx;
		background-color: #F8F8F8;
		margin: 0rpx auto;
		margin-bottom: 26rpx;
		padding: 30rpx;
		box-sizing: border-box;
	}

	.submit {
		width: 630rpx;
		height: 96rpx;
		border-radius: 48rpx;
		background-color: #7270FF;
		font-size: 16px;
		color: #FFFFFF;
		text-align: center;
		line-height: 96rpx;
	}

	.mine_note_top {
		width: 690rpx;
		height: 66rpx;
		border-radius: 55rpx;
		background-color: #E3E3FF;
		padding: 0rpx 30rpx;
	}

	.add_note {
		width: 146rpx;
		height: 50rpx;
		border-radius: 55rpx;
		background-color: #E3E3FF;
		font-size: 24rpx;
		color: #5552FF;
	}

	.no_sel {
		color: #333333;
		font-size: 24rpx;
	}

	.is_sel {
		color: #5552FF;
		font-size: 24rpx;
	}

	.answer_history {
		width: 750rpx;
		background-color: #FFFFFF;
		margin: 20rpx 0rpx;
		padding: 30rpx;
		box-sizing: border-box;
	}

	.cri_red {
		width: 14rpx;
		height: 14rpx;
		box-sizing: border-box;
		border-radius: 50%;
		border: 2rpx solid #FF585F;
		margin-right: 10rpx;
	}

	.cri_blue {
		width: 14rpx;
		height: 14rpx;
		box-sizing: border-box;
		border-radius: 50%;
		border: 2rpx solid #5552FF;
		margin-right: 10rpx;
	}

	.cri_gary {
		width: 14rpx;
		height: 14rpx;
		box-sizing: border-box;
		border-radius: 50%;
		border: 2rpx solid #C8C8C8;
		margin-right: 10rpx;
	}

	.card_no {
		width: 100rpx;
		height: 100rpx;
		border-radius: 55rpx;
		background-color: #C8C8C8;
		text-align: center;
		line-height: 100rpx;
		font-size: 32rpx;
		color: #FFFFFF;
		margin-bottom: 40rpx;
		margin-left: 20rpx;
	}

	.card_now {
		width: 100rpx;
		height: 100rpx;
		border-radius: 55rpx;
		text-align: center;
		line-height: 100rpx;
		font-size: 32rpx;
		color: #5552FF;
		margin-bottom: 40rpx;
		margin-left: 20rpx;
		border: 2rpx solid #5552FF;
	}

	.card_sub {
		width: 100rpx;
		height: 100rpx;
		border-radius: 55rpx;
		text-align: center;
		line-height: 100rpx;
		font-size: 32rpx;
		color: #FF585F;
		margin-bottom: 40rpx;
		margin-left: 20rpx;
		border: 2rpx solid #FF585F;
	}

	.scroll_warp {
		height: 450rpx;
		padding: 0rpx 40rpx 40rpx 40rpx;
		box-sizing: border-box;
	}

	.quest_title {
		font-size: 30rpx;
		color: #333333;
		margin-bottom: 50rpx;
	}

	.quest_warp {
		margin-top: 50rpx;
	}

	.correction {
		width: 122rpx;
		height: 44rpx;
		border-radius: 10rpx;
		background-color: #EEEEEE;
		text-align: center;
		line-height: 44rpx;
		color: #A1A1A1;
		font-size: 26rpx;
		margin-left: 48rpx;
	}

	.type_tips {
		width: 114rpx;
		height: 52rpx;
		border-radius: 26rpx 0rpx 26rpx 0rpx;
		background-color: #5552FF;
		text-align: center;
		line-height: 52rpx;
		color: #ffffff;
		font-size: 28rpx;
		margin-right: 30rpx;
	}

	.quest_primary {
		width: 750rpx;
		padding: 20rpx 30rpx;
		box-sizing: border-box;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		overflow: hidden;
	}
</style>