<template>
	<view class="quest_foot_fixed tn-flex tn-flex-col-center tn-flex-row-between">
		<view class="qff_left tn-flex tn-flex-col-center tn-flex-row-between">
			<view class="tn-flex tn-flex-direction-column tn-flex-col-center">
				<image src="../../static/icon/card.png" mode="widthFix" style="width: 50rpx;"></image>
				<view style="color: #666666;font-size: 22rpx;">
					答题卡
				</view>
			</view>
			<view class="tn-flex tn-flex-direction-column tn-flex-col-center">
				<image src="../../static/icon/notes.png" mode="widthFix" style="width: 50rpx;"></image>
				<view style="color: #666666;font-size: 22rpx;">
					笔记
				</view>
			</view>
		</view>
		<view class="qff_right tn-flex tn-flex-col-center tn-flex-row-between">
			<view class="last_btn">
				上一题
			</view>
			<view class="next_btn">
				下一题
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "quest_fixed",
		data() {
			return {

			};
		}
	}
</script>

<style lang="scss" scoped>
	.qff_right {
		width: 460rpx;
	}

	.next_btn {
		width: 220rpx;
		height: 80rpx;
		border-radius: 55rpx;
		box-sizing: border-box;
		border: 2rpx solid #5552FF;
		background-color: #5552FF;
		text-align: center;
		line-height: 80rpx;
		color: #ffffff;
		font-size: 32rpx;
	}

	.last_btn {
		width: 220rpx;
		height: 80rpx;
		border-radius: 55rpx;
		box-sizing: border-box;
		border: 2rpx solid #5552FF;
		text-align: center;
		line-height: 80rpx;
		color: #5552FF;
		font-size: 32rpx;
	}

	.qff_left {
		width: 170rpx;
	}

	.quest_foot_fixed {
		position: fixed;
		bottom: 0rpx;
		left: 0rpx;
		right: 0rpx;
		height: 100rpx;
		background-color: #FFFFFF;
		padding: 0rpx 30rpx;
		box-sizing: border-box;
	}
</style>