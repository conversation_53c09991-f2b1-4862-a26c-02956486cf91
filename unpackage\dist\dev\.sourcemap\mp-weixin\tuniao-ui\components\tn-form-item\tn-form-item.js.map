{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-form-item/tn-form-item.vue?6bd5", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-form-item/tn-form-item.vue?b022", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-form-item/tn-form-item.vue?187c", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-form-item/tn-form-item.vue?14ca", "uni-app:///tuniao-ui/components/tn-form-item/tn-form-item.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-form-item/tn-form-item.vue?f9f0", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-form-item/tn-form-item.vue?631f"], "names": ["schema", "mixins", "name", "inject", "tnForm", "default", "props", "label", "type", "prop", "borderBottom", "labelPosition", "labelWidth", "labelAlign", "labelStyle", "leftIcon", "rightIcon", "leftIconStyle", "rightIconStyle", "required", "computed", "w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showError", "<PERSON><PERSON><PERSON><PERSON>", "elLabelStyle", "elLabelPosition", "elLabelAlign", "elBorderBottom", "leftContentStyle", "style", "data", "initialValue", "validateState", "validateMessage", "errorType", "fieldValue", "parentData", "watch", "mounted", "Object", "<PERSON><PERSON><PERSON><PERSON>", "methods", "broadcastInputError", "setRules", "getRules", "rules", "onFieldBlur", "onFieldChange", "getFilterRule", "validation", "validator", "firstFields", "callback", "reset<PERSON>ield"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7BA;AAAA;AAAA;AAAA;AAAwnB,CAAgB,snBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC2E5oB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACAA;AAAA,gBAEA;EACAC;EACAC;EACAC;IACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAH;IACA;IACA;IACAI;MACAD;MACAH;IACA;IACA;IACAK;MACAF;MACAH;IACA;IACA;IACA;IACA;IACAM;MACAH;MACAH;IACA;IACA;IACAO;MACAJ;MACAH;IACA;IACA;IACA;IACA;IACA;IACA;IACAQ;MACAL;MACAH;IACA;IACA;IACAS;MACAN;MACAH;QACA;MACA;IACA;IACA;IACAU;MACAP;MACAH;IACA;IACA;IACAW;MACAR;MACAH;IACA;IACA;IACAY;MACAT;MACAH;QACA;MACA;IACA;IACA;IACAa;MACAV;MACAH;QACA;MACA;IACA;IACA;IACAc;MACAX;MACAH;IACA;EACA;EACAe;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA,4DACA,yDACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;UACA;YACAC;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;QAAA;MAEA;MAEA;IACA;EACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACA;MACAC;QACA1B;QACAE;QACAD;QACAE;QACAC;MACA;IACA;EACA;EACAuB;IACAL;MACA;IACA;IACA;MACA;MACA;IACA;EACA;EACAM;IAAA;IACA;IACA;IACA;IACA;MACA;MACAC;QACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACAC;;MAEA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA,8EACA,kBACA;MACAC,qDACA,6BACA;QACAC;MACA;QACA;QACA;QACA;QAEAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC/WA;AAAA;AAAA;AAAA;AAAusC,CAAgB,6nCAAG,EAAC,C;;;;;;;;;;;ACA3tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-form-item/tn-form-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-form-item.vue?vue&type=template&id=3a5efd9c&scoped=true&\"\nvar renderjs\nimport script from \"./tn-form-item.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-form-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-form-item.vue?vue&type=style&index=0&id=3a5efd9c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3a5efd9c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-form-item/tn-form-item.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-form-item.vue?vue&type=template&id=3a5efd9c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.validateState === \"error\" && _vm.showError(\"border-bottom\")\n  var s0 =\n    _vm.required || _vm.leftIcon || _vm.label\n      ? _vm.__get_style([_vm.leftContentStyle])\n      : null\n  var s1 =\n    _vm.required || _vm.leftIcon || _vm.label\n      ? _vm.__get_style([_vm.elLabelStyle])\n      : null\n  var m1 = _vm.validateState === \"error\" && _vm.showError(\"message\")\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        s0: s0,\n        s1: s1,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-form-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-form-item.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view\r\n    class=\"tn-form-item-class tn-form-item\"\r\n    :class=\"{\r\n      'tn-border-solid-bottom': elBorderBottom,\r\n      'tn-form-item__border-bottom--error': validateState === 'error' && showError('border-bottom')\r\n    }\"\r\n  >\r\n    <view\r\n      class=\"tn-form-item__body\"\r\n      :style=\"{\r\n        flexDirection: elLabelPosition == 'left' ? 'row' : 'column'\r\n      }\"\r\n    >\r\n      <!-- 处理微信小程序中设置属性的问题，不设置值的时候会变成true -->\r\n      <view\r\n        class=\"tn-form-item--left\"\r\n        :style=\"{\r\n          width: wLabelWidth,\r\n          flex: `0 0 ${wLabelWidth}`,\r\n          marginBottom: elLabelPosition == 'left' ? 0 : '10rpx'\r\n        }\"\r\n      >\r\n        <!-- 块对齐 -->\r\n        <view v-if=\"required || leftIcon || label\" class=\"tn-form-item--left__content\"\r\n          :style=\"[leftContentStyle]\"\r\n        >\r\n          <!-- nvue不支持伪元素before -->\r\n          <view v-if=\"leftIcon\" class=\"tn-form-item--left__content__icon\">\r\n            <view :class=\"[`tn-icon-${leftIcon}`]\" :style=\"leftIconStyle\"></view>\r\n          </view>\r\n          <!-- <view\r\n            class=\"tn-form-item--left__content__label\"\r\n            :style=\"[elLabelStyle, {\r\n              'justify-content': elLabelAlign === 'left' ? 'flex-satrt' : elLabelAlign === 'center' ? 'center' : 'flex-end'\r\n            }]\"\r\n          >\r\n            {{label}}\r\n          </view> -->\r\n\t\t  <text v-if=\"required\" class=\"tn-form-item--left__content--required\">*</text>\r\n          <view\r\n            class=\"tn-form-item--left__content__label\"\r\n            :style=\"[elLabelStyle]\"\r\n          >\r\n            {{label}}\r\n          </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <view class=\"tn-form-item--right tn-flex\">\r\n        <view class=\"tn-form-item--right__content\">\r\n          <view class=\"tn-form-item--right__content__slot\">\r\n            <slot></slot>\r\n          </view>\r\n          <view v-if=\"$slots.right || rightIcon\" class=\"tn-form-item--right__content__icon tn-flex\">\r\n            <view v-if=\"rightIcon\" :class=\"[`tn-icon-${rightIcon}`]\" :style=\"rightIconStyle\"></view>\r\n            <slot name=\"right\"></slot>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <view\r\n      v-if=\"validateState === 'error' && showError('message')\"\r\n      class=\"tn-form-item__message\"\r\n      :style=\"{\r\n        paddingLeft: elLabelPosition === 'left' ? elLabelWidth + 'rpx' : '0'\r\n      }\"\r\n    >\r\n      {{validateMessage}}\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import Emitter from '../../libs/utils/emitter.js'\r\n  import schema from '../../libs/utils/async-validator.js'\r\n  // 去除警告信息\r\n  schema.warning = function() {}\r\n  \r\n  export default {\r\n    mixins: [Emitter],\r\n    name: 'tn-form-item',\r\n    inject: {\r\n      tnForm: {\r\n        default() {\r\n          return null\r\n        }\r\n      }\r\n    },\r\n    props: {\r\n      // label提示语\r\n      label: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // 绑定的值\r\n      prop: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // 是否显示表单域的下划线边框\r\n      borderBottom: {\r\n        type:Boolean,\r\n        default: true\r\n      },\r\n      // label(标签名称)的位置\r\n      // left - 左边\r\n      // top - 上边\r\n      labelPosition: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // label的宽度\r\n      labelWidth: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // label的对齐方式\r\n      // left - 左对齐\r\n      // top - 上对齐\r\n      // right - 右对齐\r\n      // bottom - 下对齐\r\n      labelAlign: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // label 的样式\r\n      labelStyle: {\r\n        type: Object,\r\n        default() {\r\n          return {}\r\n        }\r\n      },\r\n      // 左侧图标\r\n      leftIcon: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // 右侧图标\r\n      rightIcon: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // 左侧图标样式\r\n      leftIconStyle: {\r\n        type: Object,\r\n        default() {\r\n          return {}\r\n        }\r\n      },\r\n      // 右侧图标样式\r\n      rightIconStyle: {\r\n        type: Object,\r\n        default() {\r\n          return {}\r\n        }\r\n      },\r\n      // 是否显示必填项的*，不做校验用途\r\n      required: {\r\n        type: Boolean,\r\n        default: false\r\n      }\r\n    },\r\n    computed: {\r\n      // 处理微信小程序label的宽度\r\n      wLabelWidth() {\r\n        // 如果用户设置label为空字符串(微信小程序空字符串最终会变成字符串的'true')，意味着要将label的位置宽度设置为auto\r\n        return this.elLabelPosition === 'left' ? (this.label === 'true' || this.label === '' ? 'auto' : this.elLabelWidth + 'rpx') : '100%'\r\n      },\r\n      // 是否显示错误提示\r\n      showError() {\r\n        return type => {\r\n          if (this.errorType.indexOf('none') >= 0) return false\r\n          else if (this.errorType.indexOf(type) >= 0) return true\r\n          else return false\r\n        }\r\n      },\r\n      // label的宽度(默认值为90)\r\n      elLabelWidth() {\r\n        return this.labelWidth != 0 ? this.labelWidth : (this.parentData.labelWidth != 0 ? this.parentData.labelWidth : 90)\r\n      },\r\n      // label的样式\r\n      elLabelStyle() {\r\n        return Object.keys(this.labelStyle).length ? this.labelStyle : (Object.keys(this.parentData.labelStyle).length ? this.parentData.labelStyle : {})\r\n      },\r\n      // label显示位置\r\n      elLabelPosition() {\r\n        return this.labelPosition ? this.labelPosition : (this.parentData.labelPosition ? this.parentData.labelPosition : 'left')\r\n      },\r\n      // label对齐方式\r\n      elLabelAlign() {\r\n        return this.labelAlign ? this.labelAlign : (this.parentData.labelAlign ? this.parentData.labelAlign : 'left')\r\n      },\r\n      // label下划线\r\n      elBorderBottom() {\r\n        return this.borderBottom !== '' ? this.borderBottom : (this.parentData.borderBottom !== '' ? this.parentData.borderBottom : true)\r\n      },\r\n      leftContentStyle() {\r\n        let style = {}\r\n        if (this.elLabelPosition === 'left') {\r\n          switch(this.elLabelAlign) {\r\n            case 'left':\r\n              style.justifyContent = 'flex-start'\r\n              break\r\n            case 'center':\r\n              style.justifyContent = 'center'\r\n              break\r\n            default:\r\n              style.justifyContent = 'flex-end'\r\n              break\r\n          }\r\n        }\r\n        \r\n        return style\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        // 默认值\r\n        initialValue: '',\r\n        // 是否校验成功\r\n        validateState: '',\r\n        // 校验失败提示信息\r\n        validateMessage: '',\r\n        // 错误的提示方式（参考form组件）\r\n        errorType: ['message'],\r\n        // 当前子组件输入的值\r\n        fieldValue: '',\r\n        // 父组件的参数\r\n        // 由于再computed中无法得知this.parent的变化，所以放在data中\r\n        parentData: {\r\n          borderBottom: true,\r\n          labelWidth: 90,\r\n          labelPosition: 'left',\r\n          labelAlign: 'left',\r\n          labelStyle: {},\r\n        }\r\n      }\r\n    },\r\n    watch: {\r\n      validateState(val) {\r\n        this.broadcastInputError()\r\n      },\r\n      \"tnForm.errorType\"(val) {\r\n        this.errorType = val\r\n        this.broadcastInputError()\r\n      }\r\n    },\r\n    mounted() {\r\n      // 组件创建完成后，保存当前实例到form组件中\r\n      // 支付宝、头条小程序不支持provide/inject，所以使用这个方法获取整个父组件，在created定义，避免循环应用\\\r\n      this.parent = this.$tn.$parent.call(this, 'tn-form')\r\n      if (this.parent) {\r\n        // 遍历parentData属性，将parent中同名的属性赋值给parentData\r\n        Object.keys(this.parentData).map(key => {\r\n          this.parentData[key] = this.parent[key]\r\n        })\r\n        // 如果没有传入prop或者tnForm为空（单独使用form-item组件的时候），就不进行校验\r\n        if (this.prop) {\r\n          // 将本实例添加到父组件中\r\n          this.parent.fields.push(this)\r\n          this.errorType = this.parent.errorType\r\n          // 设置初始值\r\n          this.initialValue = this.fieldValue\r\n          // 添加表单校验，这里必须要写在$nextTick中，因为tn-form的rules是通过ref手动传入的\r\n          // 不在$nextTick中的话，可能会造成执行此处代码时，父组件还没通过ref把规则给tn-form，导致规则为空\r\n          this.$nextTick(() => {\r\n            this.setRules()\r\n          })\r\n        }\r\n      }\r\n    },\r\n    beforeDestroy() {\r\n      // 组件销毁前，将实例从tn-form的缓存中移除\r\n      // 如果当前没有prop的话表示当前不进行删除\r\n      if (this.parent && this.prop) {\r\n        this.parent.fields.map((item, index) => {\r\n          if (item === this) this.parent.fields.splice(index, 1)\r\n        })\r\n      }\r\n    },\r\n    methods: {\r\n      // 向input组件发出错误事件\r\n      broadcastInputError() {\r\n        this.broadcast('tn-input', 'on-form-item-error', this.validateState === 'error' && this.showError('border'))\r\n      },\r\n      // 设置校验规则\r\n      setRules() {\r\n        let that = this\r\n        // 从父组件tn-form拿到当前tn-form-item需要验证 的规则\r\n        // let rules = this.getRules()\r\n        // if (rules.length) {\r\n        // \tthis.isRequired = rules.some(rule => {\r\n        // \t\t// 如果有必填项，就返回，没有的话，就是undefined\r\n        // \t\treturn rule.required\r\n        // \t})\r\n        // }\r\n        \r\n        // blur事件\r\n        this.$on('on-form-blur', that.onFieldBlur)\r\n        // change事件\r\n        this.$on('on-form-change', that.onFieldChange)\r\n      },\r\n      // 从form的rules属性中取出当前form-item的校验规则\r\n      getRules() {\r\n        let rules = this.parent.rules\r\n        rules = rules ? rules[this.prop] : []\r\n        \r\n        // 返回数值形式的值\r\n        return [].concat(rules || [])\r\n      },\r\n      // blur事件时进行表单认证\r\n      onFieldBlur() {\r\n        this.validation('blur')\r\n      },\r\n      // change事件时进行表单认证\r\n      onFieldChange() {\r\n        this.validation('change')\r\n      },\r\n      // 过滤出符合要求的rule规则\r\n      getFilterRule(triggerType = '') {\r\n        let rules = this.getRules()\r\n        // 整体验证表单时，triggerType为空字符串，此时返回所有规则进行验证\r\n        if (!triggerType) return rules\r\n        // 某些场景可能的判断规则，可能不存在trigger属性，故先判断是否存在此属性\r\n        // 历遍判断规则是否有对应的事件，比如blur，change触发等的事件\r\n        // 使用indexOf判断，是因为某些时候设置的验证规则的trigger属性可能为多个，比如['blur','change']\r\n        return rules.filter(rule => rule.trigger && rule.trigger.indexOf(triggerType) !== -1)\r\n      },\r\n      // 校验数据\r\n      validation(trigger, callback = ()=>{}) {\r\n        // 校验之前先获取需要校验的值\r\n        this.fieldValue = this.parent.model[this.prop]\r\n        // blur和change是否有当前方式的校验规则\r\n        let rules = this.getFilterRule(trigger)\r\n        // 判断是否有验证规则，如果没有规则，也调用回调方法，否则父组件tn-form会因为\r\n        // 对count变量的统计错误而无法进入上一层的回调 \r\n        if (!rules || rules.length === 0) {\r\n          return callback('')\r\n        }\r\n        // 设置当前为校验中\r\n        this.validateState = 'validating'\r\n        // 调用async-validator的方法\r\n        let validator = new schema({\r\n          [this.prop]: rules\r\n        })\r\n        validator.validate({\r\n          [this.prop]: this.fieldValue\r\n        }, {\r\n          firstFields: true\r\n        }, (errors, fields) => {\r\n          // 记录状态和报错信息\r\n          this.validateState = !errors ? 'success' : 'error'\r\n          this.validateMessage = errors ? errors[0].message : ''\r\n          \r\n          callback(this.validateMessage)\r\n        })\r\n      },\r\n      \r\n      // 清空当前item信息\r\n      resetField() {\r\n        this.parent.model[this.prop] = this.initialValue\r\n        // 清空错误标记\r\n        this.validateState = 'success'\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .tn-form-item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    padding: 20rpx 0;\r\n    font-size: 28rpx;\r\n    color: $tn-font-color;\r\n    box-sizing: border-box;\r\n    line-height: $tn-form-item-height;\r\n    \r\n    &__border-bottom--error:after {\r\n      border-color: $tn-color-red;\r\n    }\r\n    \r\n    &__body {\r\n      display: flex;\r\n      flex-direction: row;\r\n    }\r\n    \r\n    &--left {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      \r\n      &__content {\r\n        display: flex;\r\n        flex-direction: row;\r\n        position: relative;\r\n        align-items: center;\r\n        padding-right: 18rpx;\r\n        flex: 1;\r\n        \r\n        &--required {\r\n          position: relative;\r\n          right: 0;\r\n          vertical-align: middle;\r\n          color: $tn-color-red;\r\n\t\t  font-size: 40rpx;\r\n\t\t  margin-right: 5rpx;\r\n        }\r\n        \r\n        &__icon {\r\n          color: $tn-font-sub-color;\r\n          margin-right: 8rpx;\r\n        }\r\n        \r\n        &__label {\r\n          // display: flex;\r\n          // flex-direction: row;\r\n          // align-items: center;\r\n          // flex: 1;\r\n        }\r\n      }\r\n    }\r\n    \r\n    &--right {\r\n      flex: 1;\r\n      \r\n      &__content {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        flex: 1;\r\n        \r\n        &__slot {\r\n          flex: 1;\r\n          /* #ifndef MP */\r\n          display: flex;\r\n          flex-direction: row;\r\n          align-items: center;\r\n          /* #endif */\r\n        }\r\n        \r\n        &__icon {\r\n          margin-left: 10rpx;\r\n          color: $tn-font-sub-color;\r\n          font-size: 30rpx;\r\n        }\r\n      }\r\n    }\r\n    \r\n    &__message {\r\n      font-size: 24rpx;\r\n      line-height: 24rpx;\r\n      color: $tn-color-red;\r\n      margin-top: 12rpx;\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-form-item.vue?vue&type=style&index=0&id=3a5efd9c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-form-item.vue?vue&type=style&index=0&id=3a5efd9c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980405899\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}