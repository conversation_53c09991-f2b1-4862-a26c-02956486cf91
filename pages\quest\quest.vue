<template>
	<view style="padding-bottom: 30rpx;">
		<view class="top">
			<view class="top_tips tn-flex tn-flex-wrap tn-flex-col-center tn-flex-row-between">
				<view class="t_tips">
					上新推荐
				</view>
				<view class="t_word">
					<tn-notice-bar :fontSize="26" fontColor="#666666" :leftIcon="false" padding="0rpx 0rpx" :list="list"
						mode="horizontal"></tn-notice-bar>
				</view>
			</view>
		</view>
		<view class="model_warp">
			<view class="tn-width-full tn-flex tn-flex-col-center">
				<view class="m_title" :class="category_id==1?'selTitle':''" @click.stop="changeCate(1)">
					<text>强化题库</text>
					<image v-if="userInfo.exam_status == 0" class="m_titleicon" src="@/static/icon/lock.png" mode="">
					</image>
				</view>
				<!-- <view class="m_title" :class="category_id==4?'selTitle':''" @click.stop="changeCate(4)">
					<text>重点易错题库</text>
					<image v-if="userInfo.exam_status == 0" class="m_titleicon" src="@/static/icon/lock.png" mode="">
					</image>
				</view> -->
				<view class="m_title" :class="category_id==2?'selTitle':''" @click.stop="changeCate(2)">
					<text>真题题库</text>
					<image v-if="userInfo.exam_status == 0" class="m_titleicon" src="@/static/icon/lock.png" mode="">
					</image>
				</view>
				<view class="m_title" :class="category_id==3?'selTitle':''" @click.stop="changeCate(3)">
					<text>名师押题</text>
					<image v-if="userInfo.exam_status == 0" class="m_titleicon" src="@/static/icon/lock.png" mode="">
					</image>
				</view>
				<view class="tn-icon-add tn-text-bold" style="margin-left: 10rpx; font-size: 36rpx;color: #D8D8D8;"
					@click.stop="toAdd()"></view>
			</view>
			<view class="tn-width-full" v-if="category_id==1">
				<quest-model ref="quest1model" @changeTem="changeTem1" @openSelBook="showQuest=true"
					@toExercise="toExercise" @setBookNum="showType=true" @toError="toError"></quest-model>
			</view>
			<view class="tn-width-full" v-if="category_id==2">
				<quest-model-tw ref="quest2model" @changeTem="changeTem2" @openSelBook="showQuest=true"
					@toExercise="toExercise" @toNote="toNote2" @toColl="toColl2" @toHistory="toHistory"
					@toError="toError"></quest-model-tw>
			</view>
			<view class="tn-width-full" v-if="category_id==3">
				<quest-model-th ref="quest3model" @openSelBook="showQuest=true" @toExercise="toExercise"
					@toError="toError" @changeTem="changeTem3"></quest-model-th>
			</view>
			<view class="tn-width-full" v-if="category_id==4">
				<quest-model-fr ref="quest4model" @changeTem="changeTem1" @openSelBook="showQuest=true"
					@toExercise="toExercise" @setBookNum="showType=true" @toError="toError"></quest-model-fr>
			</view>
		</view>
		<view class="chart_warp">
			<view class="tn-width-full">
				<chart-line :item="chart_line" :chartData="chart_line.chartData"></chart-line>
			</view>
			<view class="tn-width-full" style="margin-top: 50rpx;" v-if="category_id!=2 && category_id!=3">
				<chart-radar :item="chart_radar" :chartData="chart_radar.chartData"></chart-radar>
			</view>
		</view>
		<view class="quest_footer" v-if="category_id!=2">
			<quest-btn :type="category_id" @toNote="toNote" @toColl="toColl" @toCheck="toCheck"></quest-btn>
		</view>
		<tn-popup v-model="showType" mode="bottom" :borderRadius="40" safeAreaInsetBottom>
			<view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; position:relative;z-index:999;background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				切换刷题</view>
			<view class="scroll_warp2 tn-width-full">
				<view @click.stop="bookType=1" style="margin-bottom: 40rpx;"
					class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
					<view class="q_pop_left">
						一刷
					</view>
					<view class="">
						<image src="../../static/icon/nosel_icon.png" mode="widthFix"
							style="width: 44rpx;height: 44rpx;" v-if="bookType!=1"></image>
						<image src="../../static/icon/sel_icon.png" mode="widthFix" style="width: 44rpx;height: 44rpx;"
							v-if="bookType==1"></image>
					</view>
				</view>
				<view @click.stop="bookType=2" style="margin-bottom: 40rpx;"
					class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
					<view class="q_pop_left">
						二刷
					</view>
					<view class="">
						<image src="../../static/icon/nosel_icon.png" mode="widthFix"
							style="width: 44rpx;height: 44rpx;" v-if="bookType!=2"></image>
						<image src="../../static/icon/sel_icon.png" mode="widthFix" style="width: 44rpx;height: 44rpx;"
							v-if="bookType==2"></image>
					</view>
				</view>
				<view @click.stop="bookType=3" style="margin-bottom: 40rpx;"
					class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
					<view class="">
						三刷
					</view>
					<view class="">
						<image src="../../static/icon/nosel_icon.png" mode="widthFix"
							style="width: 44rpx;height: 44rpx;" v-if="bookType!=3"></image>
						<image src="../../static/icon/sel_icon.png" mode="widthFix" style="width: 44rpx;height: 44rpx;"
							v-if="bookType==3"></image>
					</view>
				</view>
			</view>
			<view class="tn-flex tn-flex-col-top tn-flex-row-center" style="height: 150rpx;">
				<view class="submit" @click="submitBookNum()">保存设置</view>
			</view>
		</tn-popup>
		<tn-popup v-model="showQuest" mode="bottom" :borderRadius="40" safeAreaInsetBottom>
			<view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box;position:relative;z-index:999; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				选择书籍</view>
			<view class="scroll_warp tn-width-full">
				<scroll-view scroll-y="true" style="width: 100%;height: 100%;">
					<view v-for="(item,index) in bookList" :key="index" style="margin-bottom: 40rpx;"
						class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center"
						@click.stop="selBook(item)">
						<view class="q_pop_left tn-text-ellipsis">
							{{item.title}}
						</view>
						<view class="" v-if="item.id!=selBookId">
							<image src="../../static/icon/nosel_icon.png" mode="widthFix"
								style="width: 44rpx;height: 44rpx;"></image>
						</view>
						<view class="" v-if="item.id==selBookId">
							<image src="../../static/icon/sel_icon.png" mode="widthFix"
								style="width: 44rpx;height: 44rpx;"></image>
						</view>
					</view>
				</scroll-view>
			</view>
			<view class="tn-flex tn-flex-col-top tn-flex-row-center" style="height: 150rpx;">
				<view class="submit" @click="subBook()">保存设置</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	import questModel from "@/components/quest/quest_model.vue"
	import questModelTw from "@/components/quest/quest_model_tw.vue"
	import questModelTh from "@/components/quest/quest_model_th.vue"
	import questModelFr from "@/components/quest/quest_model_fr.vue"
	import chartLine from "@/components/quest/chart_line.vue"
	import chartRadar from "@/components/quest/chart_radar.vue"
	import questBtn from "@/components/quest/quest_btn.vue"
	import shareMixin from "@/mixins/shareMixin.js"
	export default {
		mixins: [shareMixin],
		components: {
			questModel,
			questModelTw,
			questModelTh,
			questModelFr,
			chartLine,
			chartRadar,
			questBtn
		},
		data() {
			return {
				chart_line: {},
				chart_radar: {},
				showQuest: false,
				showType: false,
				// ========
				systemData: {},
				list: [],
				category_id: 1,
				bookList: [],
				selBookId: null,
				selBookItem: {},
				selTemId: null,
				bookType: null,
				userInfo: {},
			}
		},
		onShow() {
			if (uni.getStorageSync('TOKEN')) {
				this.changeCate(this.category_id)
				this.getUserInfo();
			}
		},

		onLoad() {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			if (!uni.getStorageSync('TOKEN')) {
				// #ifdef MP-WEIXIN
				uni.login({
					provider: 'weixin',
					success: (suc) => {
						this.$http.post(this.$api.login, {
							code: suc.code
						}).then(res => {
							if (res.code == 200) {
								uni.setStorage({
									key: "TOKEN",
									// data:"2203"
									data: res.data
									// data: "oyU6A7UHYmTsNOMmVB95qwXgArks"
								})

								this.getSystemData()
								this.getBookList()
								this.getUserInfo()
							}
						})
					}
				});
				// #endif
			} else {
				this.getSystemData()
				this.getBookList()
				this.getUserInfo();
			}
		},
		methods: {
			toAdd() {
				uni.navigateTo({
					url: "./addQuest"
				})
			},
			toNote2(item) {
				let data = {
					id: item.book_id,
					title: item.cover_title
				}
				this.$publicjs.toUrl("/pages/note/index?item=" + JSON.stringify(data))
			},
			toColl2(item) {
				this.$publicjs.toUrl("/pages/collect/index?book_id=" + item.book_id + "&name=" + item.cover_title)
			},
			toHistory(item) {
				this.$publicjs.toUrl("/pages/exercise/exercise_res?chapter_id=" + item.chapter_id)
			},
			toColl() {
				this.$publicjs.toUrl("/pages/collect/index?book_id=" + this.selBookItem.id + "&name=" + this
					.selBookItem.title)
			},
			toCheck() {
				this.$publicjs.toUrl("/pages/exercise/check_index?item=" + JSON.stringify(this.selBookItem))
			},
			toNote() {
				this.$publicjs.toUrl("/pages/note/index?item=" + JSON.stringify(this.selBookItem))
			},
			submitBookNum() {
				let that = this
				uni.showModal({
					title: "提示",
					content: "切换后“错题本，笔记”数据会清空，如需要请自行导出PDF,是否确认切换？",
					success: (res) => {
						if (res.confirm) {
							that.$http.post(that.$api.bookNum, {
								book_id: that.selBookId,
								number: that.bookType
							}).then(res => {
								if (res.code == 200) {
									that.bookType = null
									that.getBookList()
									that.showType = false
								}
							})
						} else {
							that.bookType = null
							that.showType = false
						}
					}
				})

			},
			changeTem1(id) {
				this.selTemId = id
				this.getBookRateList()
				this.getBookTemRateList()
			},
			changeTem3(id) {
				this.selTemId = id
				this.getBookRateList()
			},
			changeTem2(item) {
				this.$http.post(this.$api.bookRateList, item).then(res => {
					if (res.code == 200) {
						this.chart_line = {
							chartData: {
								categories: res.data.chapter_list,
								series: [{
									name: "我的",
									type: "line",
									color: "#5552FF",
									data: res.data.correct_list,
									legendShape: "diamond",
								}, {
									name: "平均",
									type: "line",
									color: "#FF000A",
									data: res.data.avg_list,
									legendShape: "diamond",
								}],
							},
						}
					}
				})


			},
			getBookRateList() {
				this.$http.post(this.$api.bookRateList, {
					book_id: this.selBookId,
					template_id: this.selTemId
				}).then(res => {
					if (res.code == 200) {
						this.chart_line = {
							chartData: {
								categories: res.data.chapter_list,
								series: [{
									name: "我的",
									type: "line",
									color: "#5552FF",
									data: res.data.correct_list,
									legendShape: "diamond",
								}, {
									name: "平均",
									type: "line",
									color: "#FF000A",
									data: res.data.avg_list,
									legendShape: "diamond",
								}],
							},
						}
					}
				})
			},
			getBookTemRateList() {
				this.$http.post(this.$api.bookTemRateList, {
					book_id: this.selBookId,
				}).then(res => {
					if (res.code == 200) {
						let data = res.data.map(item => {
							return item.template_name
						})
						let avg_data = res.data.map(item => {
							return item.avg_list
						})
						let mine_data = res.data.map(item => {
							return item.correct_list
						})
						this.chart_radar = {
							chartData: {
								categories: data,
								series: [{
									name: "我的",
									data: mine_data,
								}, {
									name: "平均",
									data: avg_data,
								}],
							},
						}
					}
				})
			},
			toError(item) {
				let params = 'book_id=' + item.book_id + "&template_id=" + item.template_id + "&name=" + item.name +
					'&isselect=' + item.isselect
				this.$publicjs.toUrl("/pages/error_quest/index?" + params)
			},
			toExercise(item) {
				this.$publicjs.toUrl("/pages/exercise/index?book_id=" + item.book_id + "&template_id=" + item
					.template_id +
					"&name=" + item.name)
			},
			selBook(item) { //切换书籍
				this.selBookId = item.id
				this.selBookItem = item
			},
			subBook() { //提交书籍
				if (this.category_id == 1) {
					this.$refs.quest1model.changeBook(this.selBookItem)
				}
				if (this.category_id == 2) {
					this.$refs.quest2model.changeBook(this.selBookItem, this.bookList)
				}
				if (this.category_id == 3) {
					this.$refs.quest3model.changeBook(this.selBookItem)
				}
				if (this.category_id == 4) {
					this.$refs.quest4model.changeBook(this.selBookItem)
				}
				this.showQuest = false
			},
			changeCate(num) { //切换类目
				this.category_id = num || this.category_id
				this.selBookId = null
				this.selBookItem = {}
				this.bookList = []
				if (this.category_id != 2) {
					this.$nextTick(() => {
						this.getBookList()
					})
				} else {
					this.$nextTick(() => {
						this.getBookCate()
					})
				}
			},
			getBookList() { //书籍列表
				this.$http.post(this.$api.bookList, {
					category_id: this.category_id
				}).then(res => {
					uni.stopPullDownRefresh();
					if (res.code == 200) {
						this.bookList = res.data
						this.$nextTick(() => {
							this.getBookTemBefore()
						})
					}
				})
			},
			getBookTemBefore() { //书籍模板获取之前
				if (this.bookList.length > 0) {
					if (this.category_id == 1) {
						if (uni.getStorageSync('category_one')) {
							let data = this.bookList.filter(item => {
								return item.id == uni.getStorageSync('category_one')
							})
							if (data.length > 0) {
								this.selBookId = data[0].id
								this.selBookItem = data[0]
							} else {
								this.selBookId = this.bookList[0].id
								this.selBookItem = this.bookList[0]
							}
						} else {
							this.selBookId = this.bookList[0].id
							this.selBookItem = this.bookList[0]
						}
						this.$refs.quest1model.changeBook(this.selBookItem)
					}
					if (this.category_id == 2) {
						if (uni.getStorageSync('category_two')) {
							let data = this.bookList.filter(item => {
								return item.id == uni.getStorageSync('category_two')
							})
							if (data.length > 0) {
								this.selBookId = data[0].id
								this.selBookItem = data[0]
							} else {
								this.selBookId = this.bookList[0].id
								this.selBookItem = this.bookList[0]
							}
						} else {
							this.selBookId = this.bookList[0].id
							this.selBookItem = this.bookList[0]
						}
						this.$refs.quest2model.changeBook(this.selBookItem, this.bookList)
					}
					if (this.category_id == 3) {
						if (uni.getStorageSync('category_three')) {
							let data = this.bookList.filter(item => {
								return item.id == uni.getStorageSync('category_three')
							})
							if (data.length > 0) {
								this.selBookId = data[0].id
								this.selBookItem = data[0]
							} else {
								this.selBookId = this.bookList[0].id
								this.selBookItem = this.bookList[0]
							}
						} else {
							this.selBookId = this.bookList[0].id
							this.selBookItem = this.bookList[0]
						}
						this.$refs.quest3model.changeBook(this.selBookItem)
					}
					if (this.category_id == 4) {
						if (uni.getStorageSync('category_four')) {
							let data = this.bookList.filter(item => {
								return item.id == uni.getStorageSync('category_four')
							})
							if (data.length > 0) {
								this.selBookId = data[0].id
								this.selBookItem = data[0]
							} else {
								this.selBookId = this.bookList[0].id
								this.selBookItem = this.bookList[0]
							}
						} else {
							this.selBookId = this.bookList[0].id
							this.selBookItem = this.bookList[0]
						}
						this.$refs.quest4model.changeBook(this.selBookItem)
					}
				}
			},
			getBookCate() { //真题题库分类
				this.$http.post(this.$api.bookCate).then(res => {
					uni.stopPullDownRefresh();
					if (res.code == 200) {
						this.bookList = res.data
						this.$nextTick(() => {
							this.getBookTemBefore()
						})
					}
				})
			},
			getSystemData() { //系统数据
				this.$http.post(this.$api.systemData).then(res => {
					uni.stopPullDownRefresh();
					if (res.code == 200) {
						this.systemData = res.data
						this.list = [res.data.notice.title]
						uni.setStorage({
							key: "systemData",
							data: JSON.stringify(res.data)
						})
					}
				})
			},
			getUserInfo() {
				this.$http.post(this.$api.getUserInfo, {}).then(res => {
					if (res.code == 200) {
						this.userInfo = res.data;
						uni.setStorageSync('userinfo', res.data)
					}
				})
			}
		},
		onPullDownRefresh() {
			this.getSystemData();
			this.getBookList();
			this.changeCate(this.category_id);
			this.getUserInfo();
		}
	}
</script>

<style lang="scss" scoped>
	.selTitle {
		font-size: 36rpx !important;
		font-weight: bold;
	}

	.submit {
		width: 630rpx;
		height: 96rpx;
		border-radius: 48rpx;
		background: #5552FF;
		font-size: 32rpx;
		color: #FFFFFF;
		text-align: center;
		line-height: 96rpx;
	}

	.q_pop_left {
		width: 550rpx;
	}

	.scroll_warp {
		height: 450rpx;
		padding: 0rpx 40rpx 40rpx 40rpx;
		box-sizing: border-box;
	}

	.scroll_warp2 {
		height: 300rpx;
		padding: 0rpx 40rpx 40rpx 40rpx;
		box-sizing: border-box;
	}

	.quest_footer {
		width: 100%;
		height: 170rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
	}

	.chart_warp {
		width: 100%;
		background-color: #FFFFFF;
		margin-bottom: 20rpx;
		padding: 30rpx;
		box-sizing: border-box;
	}

	page {
		background-color: #f5f5f5 !important;
	}

	.m_title {
		min-width: 170rpx;
		display: flex;
		align-items: center;
		color: #333333;
		font-size: 33rpx;
	}

	.m_titleicon {
		margin-left: 5rpx;
		width: 28rpx;
		height: 28rpx;
	}

	.model_warp {
		width: 100%;
		padding: 30rpx;
		box-sizing: border-box;
	}

	.top {
		width: 100%;
		padding: 10rpx 0rpx 30rpx 0rpx;
		background: url("../../static/top_bg02.png") no-repeat;
		background-size: 100% 100%;
	}

	.t_word {
		width: 540rpx;
	}

	.t_tips {
		width: 137.5rpx;
		height: 50rpx;
		background: url("../../static/tips_bg.png") no-repeat;
		background-size: 100% 100%;
		color: #FFFFFF;
		font-size: 24rpx;
		text-align: center;
		line-height: 50rpx;
	}

	.top_tips {
		width: 750rpx;
		height: 70rpx;
		background-color: #FFFFFF;
		padding: 0rpx 36rpx;
		box-sizing: border-box;
	}
</style>