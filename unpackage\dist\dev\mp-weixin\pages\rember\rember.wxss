@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.fab_btn.data-v-43034fb4 {
  width: 154rpx;
  position: fixed;
  right: 0rpx;
  bottom: 150rpx;
}
.list_body.data-v-43034fb4 {
  margin-top: 20rpx;
}
.vw_right.data-v-43034fb4 {
  width: 150rpx;
  height: 56rpx;
  border-radius: 50rpx;
  background-color: #FFF6F0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #A45505;
  font-size: 24rpx;
}
.vw_bottom.data-v-43034fb4 {
  font-size: 20rpx;
  color: #C4690D;
}
.vwt_tip.data-v-43034fb4 {
  width: 110rpx;
  height: 34rpx;
  border-radius: 8rpx;
  background: linear-gradient(180deg, #FF9D4B 0%, #FFB200 100%);
  font-size: 24rpx;
  color: #FFFFFF;
  text-align: center;
  line-height: 36rpx;
  margin-left: 12rpx;
}
.vwt_word.data-v-43034fb4 {
  font-size: 28rpx;
  font-weight: bold;
  color: #A45505;
  margin-right: 4rpx;
}
.vip_warp.data-v-43034fb4 {
  width: 690rpx;
  height: 130rpx;
  background: url(data:image/png;base64,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) no-repeat;
  background-size: 100% 100%;
  padding: 20rpx 48rpx 28rpx 34rpx;
  box-sizing: border-box;
}
.content.data-v-43034fb4 {
  width: 100%;
  padding: 20rpx 30rpx 30rpx 30rpx;
  box-sizing: border-box;
}

