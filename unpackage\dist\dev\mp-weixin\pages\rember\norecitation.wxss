@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.quest_body.data-v-3e028f4c {
  margin-top: 30rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 140rpx);
}
.quest_footer.data-v-3e028f4c {
  width: 750rpx;
  position: fixed;
  background-color: #FFFFFF;
  height: calc(env(safe-area-inset-bottom) + 120rpx);
  bottom: 0rpx;
  left: 0rpx;
  right: 0rpx;
  padding: 0rpx 30rpx;
}
.qf_left.data-v-3e028f4c {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background: #EAEAEA;
  font-size: 45rpx;
  text-align: center;
  line-height: 60rpx;
  color: #979797;
}
.qf_right.data-v-3e028f4c {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  background: #EAEAEA;
  font-size: 45rpx;
  text-align: center;
  line-height: 60rpx;
  color: #979797;
}
.qf_btn.data-v-3e028f4c {
  width: 250rpx;
  height: 80rpx;
  border-radius: 100rpx;
  background-color: #E8E7FF;
  color: #5552FF;
  font-size: 28rpx;
  text-align: center;
  line-height: 80rpx;
}
.qf_btn2.data-v-3e028f4c {
  width: 250rpx;
  height: 80rpx;
  border-radius: 100rpx;
  background-color: #5552FF;
  color: #FFFFFF;
  font-size: 28rpx;
  text-align: center;
  line-height: 80rpx;
}
.total.data-v-3e028f4c {
  color: #999999;
  font-size: 28rpx;
  margin-left: 10rpx;
}
.content.data-v-3e028f4c {
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
}

