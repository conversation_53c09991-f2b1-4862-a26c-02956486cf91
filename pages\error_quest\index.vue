<template>
	<view>
		<view class="top tn-flex tn-flex-row-between tn-flex-col-center">
			<view class="top_left tn-text-ellipsis tn-text-bold">{{name}}</view>
			<view class="top_right tn-flex tn-flex-col-center" @click.stop="showQuestfun()">
				<view class="tr_word tn-text-ellipsis tn-text-right">
					{{selTemItem.template_name || ''}}
				</view>
				<view class="tn-icon-right"></view>
			</view>
		</view>
		<view class="tips_warp tn-flex tn-flex-row-between tn-flex-col-center">
			<view style="width: 500rpx;">
				<tn-tabs :list="list_type" bold :isScroll="false" activeColor="#333333" inactiveColor="#9E9E9E"
					:current="current" name="name" @change="change"></tn-tabs>
			</view>
			<view class="export_btn" @click.stop="showType=true">
				导出PDF
			</view>
		</view>
		<view class="body">
			<view class="tn-width-full tn-flex tn-flex-row-around" style="margin-top: 20rpx;margin-bottom: 20rpx;">
				<image src="../../static/vip_btn.png" mode="widthFix" style="width: 326rpx;height: 148rpx;"
					@click.stop="toRember()"></image>
				<image src="../../static/err_btn.png" mode="widthFix" style="width: 326rpx;height: 148rpx;"
					@click.stop="toRember()"></image>
			</view>
			<view class="tn-width-full tn-flex tn-flex-row-center" v-if="errorList.length==0">
				<image src="../../static/empty.png" mode="widthFix" style="width: 404rpx;"></image>
			</view>
			<view v-for="(item,index) in errorList" :key="index" class="tn-width-full" style="margin-bottom: 20rpx;">
				<error-model :item="item" :foldId="foldId" @changeFold="changeFold" @toQuest="toQuest"></error-model>
			</view>
		</view>
		<view class="error_footer tn-flex tn-flex-direction-column tn-flex-row-between" v-if="current==2">
			<view class="tn-flex tn-flex-row-between">
				<view class="ef_left">
					<view class="efl_top">
						右边可设置每组刷题道数
					</view>
					<view class="efl_bottom">
						共错 <text style="color: #FF000A;">{{total}}</text> 道，已解决 <text
							style="color: #5552FF;">{{res_total}}</text> 道
					</view>
				</view>
				<tn-number-box v-model="value" :min="10" :max="9999" :positiveInteger="true" disabledInput :step="5"
					:inputWidth="68"></tn-number-box>
			</view>
			<view class="ef_sub" @click.stop="toSort()">
				乱序刷题
			</view>
		</view>
		<tn-popup v-model="showQuest" mode="bottom" :borderRadius="40" safeAreaInsetBottom @close="close">
			<view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				请选择</view>
			<view class="scroll_warp tn-width-full">
				<scroll-view scroll-y="true" style="width: 100%;height: 100%;">
					<view v-for="(item,index) in temList" :key="index" style="margin-bottom: 40rpx;"
						class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center" @click.stop="selTem(item)">
						<view class="q_pop_left tn-text-ellipsis">
							{{item.template_name}}
						</view>
						<view class="" v-if="item.template_id!=selTemItem1.template_id">
							<image src="../../static/icon/nosel_icon.png" mode="widthFix"
								style="width: 44rpx;height: 44rpx;"></image>
						</view>
						<view class="" v-if="item.template_id==selTemItem1.template_id">
							<image src="../../static/icon/sel_icon.png" mode="widthFix"
								style="width: 44rpx;height: 44rpx;"></image>
						</view>
					</view>
				</scroll-view>
			</view>
			<view class="tn-flex tn-flex-col-top tn-flex-row-center" style="height: 150rpx;">
				<view class="submit" @click="subTem()">保存设置</view>
			</view>
		</tn-popup>
		<tn-select v-model="showType" mode="single" :list="typeData" @confirm="confirmPdf"></tn-select>
	</view>
</template>

<script>
	import errorModel from "@/components/quest/error_model.vue"
	export default {
		components: {
			errorModel
		},
		data() {
			return {
				list_type: [{
					name: '复习错题'
				}, {
					name: '错题重做'
				}, {
					name: '乱序刷题',
				}],
				current: 0,
				showType: false,
				typeData: [{
						value: '0',
						label: '题目+答案'
					},
					{
						value: '1',
						label: '题目+答案+笔记'
					},
					{
						value: '2',
						label: '题目+答案+解析'
					}
				],
				value: 10,
				bookId: null,
				templateId: null,
				name: null,
				isselect: true,
				showQuest: false,
				temList: [],
				selTemItem1: {},
				selTemItem: {},
				errorList: [],
				foldId: null,
				total: 0,
				res_total: 0
			};
		},
		onLoad(options) {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			if (options.book_id) {
				this.bookId = options.book_id;
				this.templateId = options.template_id || 0;
				this.name = options.name;
				this.isselect = options.isselect;
				this.getBookTem()
			}
		},

		onShow() {
			if (this.bookId) {
				this.getChapterList()
			}
		},
		methods: {
			toSort() {
				if (this.current == 2) {
					this.$publicjs.toUrl("/pages/error_quest/quest_o?id=" + this.bookId + "&template_id=" + this
						.templateId +
						"&num=" + this.value)
				}
			},
			showQuestfun() {
				if (this.isselect == 1) {
					this.showQuest = true;
				}
			},
			confirmPdf(e) {
				uni.showLoading({
					title: "生成中"
				})
				let data = {
					type: e[0].value,
					book_id: this.bookId,
					template_id: this.templateId
				}
				this.$http.post(this.$api.errorQuestExport, data).then(res => {
					if (res.code == 200) {
						let url = this.$config.baseUrl + res.data
						uni.downloadFile({
							url: url,
							success: res => {
								uni.hideLoading()
								if (res.statusCode === 200) {
									// 预览pdf文件
									uni.openDocument({
										filePath: res.tempFilePath,
										showMenu: true, // 右上角菜单，可以进行分享保存pdf
										success: function(file) {}
									})
								}
							},
							fail: () => {
								uni.hideLoading()
							}
						})
					} else {
						uni.hideLoading()
					}
				}).catch(err => {
					uni.hideLoading()
				})
			},
			toRember() {
				this.$publicjs.toTab("/pages/rember/rember")
			},
			toQuest(item) {
				if (this.current == 0) {
					this.$publicjs.toUrl("/pages/error_quest/quest?item=" + JSON.stringify(item))
				}
				if (this.current == 1) {
					this.$publicjs.toUrl("/pages/error_quest/quest_rem?id=" + item.chapter_id + "&orderSn=" + item
						.order_sn + "&template_id=" + this
						.templateId)
				}
			},
			changeFold(item) {
				if (item.chapter_id != this.foldId) {
					this.foldId = item.chapter_id
				} else {
					this.foldId = null
				}
			},
			getBookTem() { //获取书籍模板
				this.$http.post(this.$api.bookTem, {
					book_id: this.bookId
				}).then(res => {
					if (res.code == 200) {
						if (this.templateId && res.data.length > 0) {
							let data = res.data.filter(item => {
								return item.template_id == this.templateId
							})
							this.selTemItem = data[0]
							this.selTemItem1 = data[0]
						}
						this.temList = res.data
						this.getErrorCateList()
					}
				})
			},
			getChapterList() {
				this.getErrorCateList()
			},
			getErrorCateList() {
				this.$http.post(this.$api.errorCateList, {
					book_id: this.bookId,
					template_id: this.selTemItem.template_id || 0
				}).then(res => {
					if (res.code == 200) {
						this.errorList = res.data
						if (res.data.length > 0) {
							this.total = 0
							let a = res.data.map(item => {
								this.total += Number(item.total_error)
							})
							this.res_total = 0
							let b = res.data.map(item => {
								this.resolved_error += Number(item.resolved_error)
							})
							this.foldId = res.data[0].chapter_id
						}
					}
				})
			},
			close() {
				this.selTemItem1 = this.selTemItem
			},
			subTem() {
				this.showQuest = false
				this.selTemItem = this.selTemItem1
				this.getErrorCateList()
			},
			selTem(item) {
				this.selTemItem1 = item
			},
			change(index) {
				this.current = index;
			}
		}
	}
</script>

<style lang="scss" scoped>
	/deep/.uni-input-input:disabled {
		color: #3D3D3D;
	}

	.ef_sub {
		width: 690rpx;
		height: 80rpx;
		border-radius: 40rpx;
		background-color: #5552FF;
		color: #FFFFFF;
		font-size: 32rpx;
		text-align: center;
		line-height: 80rpx;
	}

	.efl_top {
		color: #D0D0D0;
		font-size: 24rpx;
	}

	.efl_bottom {
		font-size: 28rpx;
		color: #999999;
	}

	.error_footer {
		position: fixed;
		bottom: 0rpx;
		left: 0rpx;
		right: 0rpx;
		width: 750rpx;
		height: 212rpx;
		background-color: #FFFFFF;
		border-top: 1rpx solid #dfdfdf;
		padding: 20rpx 30rpx;
		box-sizing: border-box;
	}

	.submit {
		width: 630rpx;
		height: 96rpx;
		border-radius: 48rpx;
		background: #5552FF;
		font-size: 16px;
		color: #FFFFFF;
		text-align: center;
		line-height: 96rpx;
	}

	.q_pop_left {
		width: 550rpx;
	}

	.scroll_warp {
		height: 450rpx;
		padding: 0rpx 40rpx 40rpx 40rpx;
		box-sizing: border-box;
	}

	.scroll_warp2 {
		height: 300rpx;
		padding: 0rpx 40rpx 40rpx 40rpx;
		box-sizing: border-box;
	}

	.body {
		width: 750rpx;
		padding: 0rpx 30rpx;
		padding-bottom: 250rpx;
	}

	.export_btn {
		width: 132rpx;
		height: 54rpx;
		border-radius: 27rpx;
		background: linear-gradient(127deg, #FF9C4C 29%, #FFB200 90%);
		font-size: 24rpx;
		color: #FFFFFF;
		text-align: center;
		line-height: 54rpx;
	}

	.tips_warp {
		width: 750rpx;
		height: 100rpx;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		background-color: #FFFFFF;
		padding: 0rpx 40rpx 0rpx 10rpx;
		box-sizing: border-box;
	}

	.top {
		width: 750rpx;
		height: 100rpx;
		background-color: #FFFFFF;
		padding: 0rpx 40rpx;
		box-sizing: border-box;

		.top_left {
			color: #222222;
			font-size: 30rpx;
			width: 350rpx;
		}

		.top_right {
			color: #666666;
			font-size: 28rpx;

			.tr_word {
				width: 250rpx;
			}
		}
	}
</style>