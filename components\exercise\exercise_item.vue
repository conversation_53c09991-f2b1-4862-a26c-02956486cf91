<template>
	<view class="exercise_item tn-flex tn-flex-direction-column tn-flex-row-between" @click.stop="toQuest()">
		<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between">
			<view class="tn-text-ellipsis tn-text-bold" style="width: 520rpx;font-size: 30rpx;">
				{{item.title}}
			</view>
			<!-- <tn-circle-progress :percent="item.complete_ratio" :width="84" :borderWidth="4" activeColor="#5552FF">
				<view style="color: #222222;font-size: 20rpx;">{{item.complete_ratio||0}}%</view>
			</tn-circle-progress> -->
			<chatProSmall :opts="getOpt(item)" :chartData="getRatio(item)"></chatProSmall>
		</view>
		<view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
			<view class="tn-flex tn-flex-col-center" style="width: 520rpx;font-size: 28rpx;color: #666666;">
				<text>共{{item.total}}题</text>
				<text style="margin-left: 46rpx;">正确率: <text
						style="color: #5552FF;font-weight: bold;">{{item.correct_ratio||0}}%</text>
				</text>
				<view class="his_btn tn-flex tn-flex-row-center tn-flex-col-center" v-if="item.max_title_id>0"
					@click.stop="toEnd()">
					<image src="../../static/icon/ex_his.png" mode="widthFix"
						style="width: 28rpx;height: 28rpx; margin-right: 2rpx;">
					</image> 答题记录
				</view>
			</view>
			<view class="tn-icon-right" style="font-size: 28rpx;color: #666666;" v-if="getShow()"></view>
			<image src="../../static/lockicon.png" mode="widthFix" style="width: 30rpx;height: 30rpx;"
				v-if="!getShow()">
			</image>
		</view>
	</view>
</template>

<script>
	import chatProSmall from "@/components/chat_pro_small.vue"
	export default {
		components: {
			chatProSmall
		},
		name: "exercise_item",
		props: {
			item: {
				type: Object,
				default: () => {}
			},
		},
		data() {
			return {

			};
		},
		methods: {
			getOpt(bookDetail) {
				let ab = bookDetail.complete_ratio ? bookDetail.complete_ratio + "%" : "0%"
				let a = {
					title: {
						name: ab,
						fontSize: 10,
						color: "#222222",
						offsetX: 0,
						offsetY: 0
					},
					subtitle: {
						name: "",
						fontSize: 8,
						color: "#222222",
						offsetX: 0,
						offsetY: 0
					},
					extra: {
						arcbar: {
							type: "circle",
							width: 2,
							backgroundColor: "#f0f0f0",
							startAngle: 1.5,
							endAngle: 1.5,
							gap: 2,
							direction: "cw",
							lineCap: "butt",
							centerX: 0,
							centerY: 0,
							linearType: "none"
						}
					}
				}
				return a
			},
			getRatio(bookDetail) {
				let a = bookDetail.complete_ratio ? Number(bookDetail.complete_ratio / 100).toFixed(2) : 0
				return {
					series: [{
						color: "#5552FF",
						data: a
					}]
				}
			},
			getShow() {
				if (Object.keys(this.item).length > 0) {
					let userinfo = uni.getStorageSync('userinfo')
					if (userinfo.exam_status == 0) {
						if (this.item.is_unlock == 1) {
							return false
						} else {
							return true
						}
					} else {
						return true
					}
				}
			},
			toEnd() {
				this.$emit("toEnd", this.item)
			},
			toQuest() {
				this.$emit("toQuest", this.item)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.his_btn {
		margin-left: 32rpx;
		width: 142rpx;
		height: 35rpx;
		border-radius: 8rpx;
		background-color: #FFEEEF;
		color: #FF000A;
		font-size: 20rpx;
	}

	.exercise_item {
		width: 690rpx;
		height: 200rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		padding: 30rpx;
		box-sizing: border-box;
	}
</style>