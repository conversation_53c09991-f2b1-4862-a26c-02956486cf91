<template>
	<view class="charts-box">
		<qiun-data-charts type="arcbar" :canvas2d="true" :opts="opts" :chartData="chartData" :animation="false" />
	</view>
</template>

<script>
	export default {
		props: {
			chartData: {
				type: Object,
				default: () => {}
			},
			opts: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {
				//这里的 opts 是图表类型 type="arcbar" 的全部配置参数，您可以将此配置复制到 config-ucharts.js 文件中下标为 ['arcbar'] 的节点中来覆盖全局默认参数。实际应用过程中 opts 只需传入与全局默认参数中不一致的【某一个属性】即可实现同类型的图表显示不同的样式，达到页面简洁的需求。

			};
		},
		onReady() {},
		methods: {}
	};
</script>

<style scoped>
	/* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
	.charts-box {
		width: 150rpx;
		height: 150rpx;
	}
</style>