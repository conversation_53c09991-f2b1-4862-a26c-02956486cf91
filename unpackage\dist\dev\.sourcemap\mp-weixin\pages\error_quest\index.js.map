{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/project/shuati_new/pages/error_quest/index.vue?b351", "webpack:///D:/project/shuati_new/pages/error_quest/index.vue?15cb", "webpack:///D:/project/shuati_new/pages/error_quest/index.vue?72d6", "webpack:///D:/project/shuati_new/pages/error_quest/index.vue?bec0", "uni-app:///pages/error_quest/index.vue", "webpack:///D:/project/shuati_new/pages/error_quest/index.vue?ca65", "webpack:///D:/project/shuati_new/pages/error_quest/index.vue?5f3b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "errorModel", "data", "list_type", "name", "current", "showType", "typeData", "value", "label", "bookId", "templateId", "isselect", "showQuest", "temList", "selTemItem1", "selTemItem", "errorList", "foldId", "total", "res_total", "onLoad", "withShareTicket", "menus", "onShow", "methods", "to<PERSON>ort", "showQuestfun", "confirmPdf", "uni", "title", "type", "book_id", "template_id", "url", "success", "filePath", "showMenu", "fail", "toRember", "toQuest", "order_sn", "changeFold", "getBookTem", "getChapterList", "getErrorCateList", "close", "subTem", "selTem", "change"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sQAEN;AACP,KAAK;AACL;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;eCoFtnB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;QACAC;MACA;QACAA;MACA;QACAA;MACA;MACAC;MACAC;MACAC;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAD;MACAE;MACAC;MACAP;MACAQ;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEA1B;MAAA;MACA2B;MACAC;IACA;IAEA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EAEAC;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;QACA,6FACAf,aACA;MACA;IACA;IACAgB;MACA;QACA;MACA;IACA;IACAC;MAAA;MACAC;QACAC;MACA;MACA;QACAC;QACAC;QACAC;MACA;MACA;QACA;UACA;UACAJ;YACAK;YACAC;cACAN;cACA;gBACA;gBACAA;kBACAO;kBACAC;kBAAA;kBACAF;gBACA;cACA;YACA;YACAG;cACAT;YACA;UACA;QACA;UACAA;QACA;MACA;QACAA;MACA;IACA;IACAU;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;QACA,+FACAC,kCACA9B;MACA;IACA;IACA+B;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;MACA;QACAX;MACA;QACA;UACA;YACA;cACA;YACA;YACA;YACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAY;MACA;IACA;IACAC;MAAA;MACA;QACAb;QACAC;MACA;QACA;UACA;UACA;YACA;YACA;cACA;YACA;YACA;YACA;cACA;YACA;YACA;UACA;QACA;MACA;IACA;IACAa;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjRA;AAAA;AAAA;AAAA;AAAqqC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACAzrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/error_quest/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/error_quest/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=bbcc50f6&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=bbcc50f6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bbcc50f6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/error_quest/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=bbcc50f6&scoped=true&\"", "var components\ntry {\n  components = {\n    tnTabs: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-tabs/tn-tabs\" */ \"@/tuniao-ui/components/tn-tabs/tn-tabs.vue\"\n      )\n    },\n    tnNumberBox: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-number-box/tn-number-box\" */ \"@/tuniao-ui/components/tn-number-box/tn-number-box.vue\"\n      )\n    },\n    tnPopup: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-popup/tn-popup\" */ \"@/tuniao-ui/components/tn-popup/tn-popup.vue\"\n      )\n    },\n    tnSelect: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-select/tn-select\" */ \"@/tuniao-ui/components/tn-select/tn-select.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.errorList.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.showType = true\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"top tn-flex tn-flex-row-between tn-flex-col-center\">\r\n\t\t\t<view class=\"top_left tn-text-ellipsis tn-text-bold\">{{name}}</view>\r\n\t\t\t<view class=\"top_right tn-flex tn-flex-col-center\" @click.stop=\"showQuestfun()\">\r\n\t\t\t\t<view class=\"tr_word tn-text-ellipsis tn-text-right\">\r\n\t\t\t\t\t{{selTemItem.template_name || ''}}\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-icon-right\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"tips_warp tn-flex tn-flex-row-between tn-flex-col-center\">\r\n\t\t\t<view style=\"width: 500rpx;\">\r\n\t\t\t\t<tn-tabs :list=\"list_type\" bold :isScroll=\"false\" activeColor=\"#333333\" inactiveColor=\"#9E9E9E\"\r\n\t\t\t\t\t:current=\"current\" name=\"name\" @change=\"change\"></tn-tabs>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"export_btn\" @click.stop=\"showType=true\">\r\n\t\t\t\t导出PDF\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"body\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-row-around\" style=\"margin-top: 20rpx;margin-bottom: 20rpx;\">\r\n\t\t\t\t<image src=\"../../static/vip_btn.png\" mode=\"widthFix\" style=\"width: 326rpx;height: 148rpx;\"\r\n\t\t\t\t\************=\"toRember()\"></image>\r\n\t\t\t\t<image src=\"../../static/err_btn.png\" mode=\"widthFix\" style=\"width: 326rpx;height: 148rpx;\"\r\n\t\t\t\t\************=\"toRember()\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-row-center\" v-if=\"errorList.length==0\">\r\n\t\t\t\t<image src=\"../../static/empty.png\" mode=\"widthFix\" style=\"width: 404rpx;\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view v-for=\"(item,index) in errorList\" :key=\"index\" class=\"tn-width-full\" style=\"margin-bottom: 20rpx;\">\r\n\t\t\t\t<error-model :item=\"item\" :foldId=\"foldId\" @changeFold=\"changeFold\" @toQuest=\"toQuest\"></error-model>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"error_footer tn-flex tn-flex-direction-column tn-flex-row-between\" v-if=\"current==2\">\r\n\t\t\t<view class=\"tn-flex tn-flex-row-between\">\r\n\t\t\t\t<view class=\"ef_left\">\r\n\t\t\t\t\t<view class=\"efl_top\">\r\n\t\t\t\t\t\t右边可设置每组刷题道数\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"efl_bottom\">\r\n\t\t\t\t\t\t共错 <text style=\"color: #FF000A;\">{{total}}</text> 道，已解决 <text\r\n\t\t\t\t\t\t\tstyle=\"color: #5552FF;\">{{res_total}}</text> 道\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<tn-number-box v-model=\"value\" :min=\"10\" :max=\"9999\" :positiveInteger=\"true\" disabledInput :step=\"5\"\r\n\t\t\t\t\t:inputWidth=\"68\"></tn-number-box>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"ef_sub\" @click.stop=\"toSort()\">\r\n\t\t\t\t乱序刷题\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<tn-popup v-model=\"showQuest\" mode=\"bottom\" :borderRadius=\"40\" safeAreaInsetBottom @close=\"close\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t请选择</view>\r\n\t\t\t<view class=\"scroll_warp tn-width-full\">\r\n\t\t\t\t<scroll-view scroll-y=\"true\" style=\"width: 100%;height: 100%;\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in temList\" :key=\"index\" style=\"margin-bottom: 40rpx;\"\r\n\t\t\t\t\t\tclass=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center\" @click.stop=\"selTem(item)\">\r\n\t\t\t\t\t\t<view class=\"q_pop_left tn-text-ellipsis\">\r\n\t\t\t\t\t\t\t{{item.template_name}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\" v-if=\"item.template_id!=selTemItem1.template_id\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/icon/nosel_icon.png\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\t\tstyle=\"width: 44rpx;height: 44rpx;\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"\" v-if=\"item.template_id==selTemItem1.template_id\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/icon/sel_icon.png\" mode=\"widthFix\"\r\n\t\t\t\t\t\t\t\tstyle=\"width: 44rpx;height: 44rpx;\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-flex tn-flex-col-top tn-flex-row-center\" style=\"height: 150rpx;\">\r\n\t\t\t\t<view class=\"submit\" @click=\"subTem()\">保存设置</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t\t<tn-select v-model=\"showType\" mode=\"single\" :list=\"typeData\" @confirm=\"confirmPdf\"></tn-select>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport errorModel from \"@/components/quest/error_model.vue\"\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\terrorModel\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tlist_type: [{\r\n\t\t\t\t\tname: '复习错题'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tname: '错题重做'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tname: '乱序刷题',\r\n\t\t\t\t}],\r\n\t\t\t\tcurrent: 0,\r\n\t\t\t\tshowType: false,\r\n\t\t\t\ttypeData: [{\r\n\t\t\t\t\t\tvalue: '0',\r\n\t\t\t\t\t\tlabel: '题目+答案'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tvalue: '1',\r\n\t\t\t\t\t\tlabel: '题目+答案+笔记'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tvalue: '2',\r\n\t\t\t\t\t\tlabel: '题目+答案+解析'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tvalue: 10,\r\n\t\t\t\tbookId: null,\r\n\t\t\t\ttemplateId: null,\r\n\t\t\t\tname: null,\r\n\t\t\t\tisselect: true,\r\n\t\t\t\tshowQuest: false,\r\n\t\t\t\ttemList: [],\r\n\t\t\t\tselTemItem1: {},\r\n\t\t\t\tselTemItem: {},\r\n\t\t\t\terrorList: [],\r\n\t\t\t\tfoldId: null,\r\n\t\t\t\ttotal: 0,\r\n\t\t\t\tres_total: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\tif (options.book_id) {\r\n\t\t\t\tthis.bookId = options.book_id;\r\n\t\t\t\tthis.templateId = options.template_id || 0;\r\n\t\t\t\tthis.name = options.name;\r\n\t\t\t\tthis.isselect = options.isselect;\r\n\t\t\t\tthis.getBookTem()\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonShow() {\r\n\t\t\tif (this.bookId) {\r\n\t\t\t\tthis.getChapterList()\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttoSort() {\r\n\t\t\t\tif (this.current == 2) {\r\n\t\t\t\t\tthis.$publicjs.toUrl(\"/pages/error_quest/quest_o?id=\" + this.bookId + \"&template_id=\" + this\r\n\t\t\t\t\t\t.templateId +\r\n\t\t\t\t\t\t\"&num=\" + this.value)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tshowQuestfun() {\r\n\t\t\t\tif (this.isselect == 1) {\r\n\t\t\t\t\tthis.showQuest = true;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tconfirmPdf(e) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: \"生成中\"\r\n\t\t\t\t})\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\ttype: e[0].value,\r\n\t\t\t\t\tbook_id: this.bookId,\r\n\t\t\t\t\ttemplate_id: this.templateId\r\n\t\t\t\t}\r\n\t\t\t\tthis.$http.post(this.$api.errorQuestExport, data).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tlet url = this.$config.baseUrl + res.data\r\n\t\t\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\t\t\turl: url,\r\n\t\t\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\t\t\t\t// 预览pdf文件\r\n\t\t\t\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\t\t\t\t\tshowMenu: true, // 右上角菜单，可以进行分享保存pdf\r\n\t\t\t\t\t\t\t\t\t\tsuccess: function(file) {}\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\ttoRember() {\r\n\t\t\t\tthis.$publicjs.toTab(\"/pages/rember/rember\")\r\n\t\t\t},\r\n\t\t\ttoQuest(item) {\r\n\t\t\t\tif (this.current == 0) {\r\n\t\t\t\t\tthis.$publicjs.toUrl(\"/pages/error_quest/quest?item=\" + JSON.stringify(item))\r\n\t\t\t\t}\r\n\t\t\t\tif (this.current == 1) {\r\n\t\t\t\t\tthis.$publicjs.toUrl(\"/pages/error_quest/quest_rem?id=\" + item.chapter_id + \"&orderSn=\" + item\r\n\t\t\t\t\t\t.order_sn + \"&template_id=\" + this\r\n\t\t\t\t\t\t.templateId)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tchangeFold(item) {\r\n\t\t\t\tif (item.chapter_id != this.foldId) {\r\n\t\t\t\t\tthis.foldId = item.chapter_id\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.foldId = null\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetBookTem() { //获取书籍模板\r\n\t\t\t\tthis.$http.post(this.$api.bookTem, {\r\n\t\t\t\t\tbook_id: this.bookId\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tif (this.templateId && res.data.length > 0) {\r\n\t\t\t\t\t\t\tlet data = res.data.filter(item => {\r\n\t\t\t\t\t\t\t\treturn item.template_id == this.templateId\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthis.selTemItem = data[0]\r\n\t\t\t\t\t\t\tthis.selTemItem1 = data[0]\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.temList = res.data\r\n\t\t\t\t\t\tthis.getErrorCateList()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetChapterList() {\r\n\t\t\t\tthis.getErrorCateList()\r\n\t\t\t},\r\n\t\t\tgetErrorCateList() {\r\n\t\t\t\tthis.$http.post(this.$api.errorCateList, {\r\n\t\t\t\t\tbook_id: this.bookId,\r\n\t\t\t\t\ttemplate_id: this.selTemItem.template_id || 0\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.errorList = res.data\r\n\t\t\t\t\t\tif (res.data.length > 0) {\r\n\t\t\t\t\t\t\tthis.total = 0\r\n\t\t\t\t\t\t\tlet a = res.data.map(item => {\r\n\t\t\t\t\t\t\t\tthis.total += Number(item.total_error)\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthis.res_total = 0\r\n\t\t\t\t\t\t\tlet b = res.data.map(item => {\r\n\t\t\t\t\t\t\t\tthis.resolved_error += Number(item.resolved_error)\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tthis.foldId = res.data[0].chapter_id\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.selTemItem1 = this.selTemItem\r\n\t\t\t},\r\n\t\t\tsubTem() {\r\n\t\t\t\tthis.showQuest = false\r\n\t\t\t\tthis.selTemItem = this.selTemItem1\r\n\t\t\t\tthis.getErrorCateList()\r\n\t\t\t},\r\n\t\t\tselTem(item) {\r\n\t\t\t\tthis.selTemItem1 = item\r\n\t\t\t},\r\n\t\t\tchange(index) {\r\n\t\t\t\tthis.current = index;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t/deep/.uni-input-input:disabled {\r\n\t\tcolor: #3D3D3D;\r\n\t}\r\n\r\n\t.ef_sub {\r\n\t\twidth: 690rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tbackground-color: #5552FF;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 32rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t}\r\n\r\n\t.efl_top {\r\n\t\tcolor: #D0D0D0;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.efl_bottom {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.error_footer {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0rpx;\r\n\t\tleft: 0rpx;\r\n\t\tright: 0rpx;\r\n\t\twidth: 750rpx;\r\n\t\theight: 212rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tborder-top: 1rpx solid #dfdfdf;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.submit {\r\n\t\twidth: 630rpx;\r\n\t\theight: 96rpx;\r\n\t\tborder-radius: 48rpx;\r\n\t\tbackground: #5552FF;\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #FFFFFF;\r\n\t\ttext-align: center;\r\n\t\tline-height: 96rpx;\r\n\t}\r\n\r\n\t.q_pop_left {\r\n\t\twidth: 550rpx;\r\n\t}\r\n\r\n\t.scroll_warp {\r\n\t\theight: 450rpx;\r\n\t\tpadding: 0rpx 40rpx 40rpx 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.scroll_warp2 {\r\n\t\theight: 300rpx;\r\n\t\tpadding: 0rpx 40rpx 40rpx 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.body {\r\n\t\twidth: 750rpx;\r\n\t\tpadding: 0rpx 30rpx;\r\n\t\tpadding-bottom: 250rpx;\r\n\t}\r\n\r\n\t.export_btn {\r\n\t\twidth: 132rpx;\r\n\t\theight: 54rpx;\r\n\t\tborder-radius: 27rpx;\r\n\t\tbackground: linear-gradient(127deg, #FF9C4C 29%, #FFB200 90%);\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\ttext-align: center;\r\n\t\tline-height: 54rpx;\r\n\t}\r\n\r\n\t.tips_warp {\r\n\t\twidth: 750rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 0rpx 0rpx 20rpx 20rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tpadding: 0rpx 40rpx 0rpx 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.top {\r\n\t\twidth: 750rpx;\r\n\t\theight: 100rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tpadding: 0rpx 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\r\n\t\t.top_left {\r\n\t\t\tcolor: #222222;\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\twidth: 350rpx;\r\n\t\t}\r\n\r\n\t\t.top_right {\r\n\t\t\tcolor: #666666;\r\n\t\t\tfont-size: 28rpx;\r\n\r\n\t\t\t.tr_word {\r\n\t\t\t\twidth: 250rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=bbcc50f6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=bbcc50f6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980619621\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}