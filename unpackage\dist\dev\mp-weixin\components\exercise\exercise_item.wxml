<view data-event-opts="{{[['tap',[['toQuest']]]]}}" class="exercise_item tn-flex tn-flex-direction-column tn-flex-row-between data-v-53ebd2c4" catchtap="__e"><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-53ebd2c4"><view class="tn-text-ellipsis tn-text-bold data-v-53ebd2c4" style="width:520rpx;font-size:30rpx;">{{''+item.title+''}}</view><chat-pro-small vue-id="298d83b0-1" opts="{{$root.m0}}" chartData="{{$root.m1}}" class="data-v-53ebd2c4" bind:__l="__l"></chat-pro-small></view><view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center data-v-53ebd2c4"><view class="tn-flex tn-flex-col-center data-v-53ebd2c4" style="width:520rpx;font-size:28rpx;color:#666666;"><text class="data-v-53ebd2c4">{{"共"+item.total+"题"}}</text><text style="margin-left:46rpx;" class="data-v-53ebd2c4">正确率:<text style="color:#5552FF;font-weight:bold;" class="data-v-53ebd2c4">{{(item.correct_ratio||0)+"%"}}</text></text><block wx:if="{{item.max_title_id>0}}"><view data-event-opts="{{[['tap',[['toEnd']]]]}}" class="his_btn tn-flex tn-flex-row-center tn-flex-col-center data-v-53ebd2c4" catchtap="__e"><image style="width:28rpx;height:28rpx;margin-right:2rpx;" src="../../static/icon/ex_his.png" mode="widthFix" class="data-v-53ebd2c4"></image>答题记录</view></block></view><block wx:if="{{$root.m2}}"><view class="tn-icon-right data-v-53ebd2c4" style="font-size:28rpx;color:#666666;"></view></block><block wx:if="{{!$root.m3}}"><image style="width:30rpx;height:30rpx;" src="../../static/lockicon.png" mode="widthFix" class="data-v-53ebd2c4"></image></block></view></view>