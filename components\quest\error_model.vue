<template>
	<view class="error_body" @click.stop="toQuest()">
		<view class="tn-width-full tn-flex tn-flex-row-right">
			<view class="tn-flex tn-flex-col-center" style="color: #666666;font-size: 28rpx;">立即巩固<view
					class="tn-icon-right"></view>
			</view>
		</view>
		<view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center"
			style="margin-top: 20rpx;margin-bottom: 20rpx;">
			<view class="eb_title tn-text-bold tn-text-ellipsis">
				{{item.chapter_title}}
			</view>
			<view class="eb_fold" @click.stop="changeFold()">
				共错{{item.total_error||0}}道<text class="tn-icon-up-triangle" style="font-size: 30rpx;"
					v-if="foldId==item.chapter_id"></text><text class="tn-icon-down-triangle" style="font-size: 30rpx;"
					v-if="foldId!=item.chapter_id"></text>
			</view>
		</view>
		<view class="tn-width-full" v-if="foldId==item.chapter_id">
			<view class="" style="font-size: 28rpx;color: #999999;margin-top: 30rpx;margin-bottom: 20rpx;"
				v-if="item.error_record.one&&item.error_record.one.length>0">
				单选题
			</view>
			<view class="tn-width-full tn-flex tn-flex-wrap tn-flex-row-between"
				v-if="item.error_record.one&&item.error_record.one.length>0">
				<view v-for="(it,ind) in item.error_record.one" :key="ind" style="width: 20%;position: relative;">
					<view class="eb_order">
						{{it.title_id}}
					</view>
					<view class="error_bad tn-flex tn-flex-col-center tn-flex-row-center">
						<image src="../../static/icon/error_icon.png" mode="widthFix"
							style="width: 24rpx;height: 24rpx;">
						</image>
						<text style="font-size: 24rpx;color: #ffffff;">{{it.error_num}}</text>
					</view>
				</view>
				<view v-for="(item,index) in 3" :key="'00'+index" style="width: 20%;"></view>
			</view>
			<view class="" style="font-size: 28rpx;color: #999999;margin-top: 30rpx;margin-bottom: 20rpx;"
				v-if="item.error_record.two&&item.error_record.two.length>0">
				多选题
			</view>
			<view class="tn-width-full tn-flex tn-flex-wrap tn-flex-row-between"
				v-if="item.error_record.two&&item.error_record.two.length>0">
				<view v-for="(it,ind) in item.error_record.two" :key="ind" style="width: 20%;position: relative;">
					<view class="eb_order">
						{{it.title_id}}
					</view>
					<view class="error_bad tn-flex tn-flex-col-center tn-flex-row-center">
						<image src="../../static/icon/error_icon.png" mode="widthFix"
							style="width: 24rpx;height: 24rpx;">
						</image>
						<text style="font-size: 24rpx;color: #ffffff;">{{it.error_num}}</text>
					</view>
				</view>
				<view v-for="(item,index) in 3" :key="'00'+index" style="width: 20%;"></view>
			</view>
			<view class="" style="font-size: 28rpx;color: #999999;margin-top: 30rpx;margin-bottom: 20rpx;"
				v-if="item.error_record.three&&item.error_record.three.length>0">
				不定项
			</view>
			<view class="tn-width-full tn-flex tn-flex-wrap tn-flex-row-between"
				v-if="item.error_record.three&&item.error_record.three.length>0">
				<view v-for="(it,ind) in item.error_record.three" :key="ind" style="width: 20%;position: relative;">
					<view class="eb_order">
						{{it.title_id}}
					</view>
					<view class="error_bad tn-flex tn-flex-col-center tn-flex-row-center">
						<image src="../../static/icon/error_icon.png" mode="widthFix"
							style="width: 24rpx;height: 24rpx;">
						</image>
						<text style="font-size: 24rpx;color: #ffffff;">{{it.error_num}}</text>
					</view>
				</view>
				<view v-for="(item,index) in 3" :key="'00'+index" style="width: 20%;"></view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "error_model",
		props: {
			item: {
				type: Object,
				default: () => {}
			},
			foldId: {
				type: Number,
				default: null
			}
		},
		data() {
			return {

			};
		},
		methods: {
			toQuest(){
				this.$emit("toQuest", this.item)
			},
			changeFold() {
				this.$emit("changeFold", this.item)
			},
		}
	}
</script>

<style lang="scss" scoped>
	.error_bad {
		width: 64rpx;
		height: 28rpx;
		border-radius: 17rpx 17rpx 17rpx 0rpx;
		// background-color: #FF000A;
		background: linear-gradient(180deg, #FFBD23 0%, #FFBD23 100%);
		position: absolute;
		top: 0rpx;
		right: 10rpx;
	}

	.eb_order {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		background-color: #F0F0F0;
		text-align: center;
		line-height: 100rpx;
		color: #333333;
		font-size: 32rpx;
		margin-bottom: 20rpx;
	}

	.error_body {
		width: 690rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		box-sizing: border-box;
		background-color: #FFFFFF;

		.eb_title {
			max-width: 420rpx;
			font-size: 30rpx;
			color: #333333;
		}

		.eb_fold {
			width: 165rpx;
			height: 54rpx;
			border-radius: 10rpx;
			background-color: #F7F7F7;
			color: #666666;
			font-size: 24rpx;
			text-align: center;
			line-height: 54rpx;
		}
	}
</style>