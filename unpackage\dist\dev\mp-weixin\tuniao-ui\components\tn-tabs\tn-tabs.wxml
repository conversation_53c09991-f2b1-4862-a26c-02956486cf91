<view class="{{['tn-tabs-class','tn-tabs','data-v-77999140',backgroundColorClass]}}" style="{{'background-color:'+(backgroundColorStyle)+';'+('margin-top:'+($root.g0)+';')}}"><view id="{{id}}" class="data-v-77999140"><scroll-view class="tn-tabs__scroll-view data-v-77999140" scroll-x="{{true}}" scroll-left="{{scrollLeft}}" scroll-with-animation="{{true}}"><view class="{{['tn-tabs__scroll-view__box','data-v-77999140',(!isScroll)?'tn-tabs__scroll-view--flex':'']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tn-tabs__scroll-view__item tn-text-ellipsis data-v-77999140" style="{{item.s0}}" id="{{'tn-tabs__scroll-view__item-'+index}}" data-event-opts="{{[['tap',[['clickTab',[index]]]]]}}" bindtap="__e"><block wx:if="{{item.$orig[count]||item.$orig['count']}}"><tn-badge vue-id="{{'7b9794b4-1-'+index}}" backgroundColor="tn-bg-red" fontColor="#FFFFFF" absolute="{{true}}" top="{{badgeOffset[0]||0}}" right="{{badgeOffset[1]||0}}" class="data-v-77999140" bind:__l="__l" vue-slots="{{['default']}}">{{item.$orig[count]||item.$orig['count']}}</tn-badge></block>{{''+(item.$orig[name]||item.$orig['name'])+''}}</view></block><block wx:if="{{showBar}}"><view class="tn-tabs__bar tn-flex tn-flex-direction-column tn-flex-col-center data-v-77999140" style="{{$root.s1}}"><view class="selBar1 data-v-77999140"></view><view class="selBar2 data-v-77999140"></view></view></block></view></scroll-view></view></view>