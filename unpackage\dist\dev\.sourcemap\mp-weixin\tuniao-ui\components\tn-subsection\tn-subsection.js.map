{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-subsection/tn-subsection.vue?4a41", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-subsection/tn-subsection.vue?4e2d", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-subsection/tn-subsection.vue?57fc", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-subsection/tn-subsection.vue?8708", "uni-app:///tuniao-ui/components/tn-subsection/tn-subsection.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-subsection/tn-subsection.vue?37f2", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-subsection/tn-subsection.vue?726f"], "names": ["mixins", "name", "props", "mode", "type", "default", "height", "list", "current", "activeColor", "inactiveColor", "bold", "backgroundColor", "buttonColor", "borderRadius", "animation", "animationType", "vibrateShort", "data", "listInfo", "itemBgStyle", "width", "left", "currentIndex", "buttonPadding", "firstVibrateShort", "watch", "handler", "item", "immediate", "deep", "created", "computed", "noBorderRight", "textStyle", "style", "itemStyle", "subsectionStyle", "subsectionBackgroundColorClass", "clazz", "itemBarClass", "itemBarStyle", "mounted", "setTimeout", "methods", "changeSectionStatus", "uni", "getTabsInfo", "view", "res", "itemBgLeft", "click", "index"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChCA;AAAA;AAAA;AAAA;AAAynB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACoB7oB;;;;;;;;;;;;;;;;;;;;gBACA;EACAA;EACAC;EACAC;IACA;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;EACA;EACAa;IACA;MACA;MACAC;MACA;MACAC;QACAC;QACAC;QACAV;QACAN;MACA;MACA;MACAiB;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACAnB;MACAoB;QACA;UACA;YACA;cACAN;cACApB;YACA;YACA;UACA;YACA2B;YACA;UACA;QACA;MACA;MACAC;MACAC;IACA;IACAtB;MACAmB;QACA;QACA;MACA;MACAE;IACA;EACA;EACAE;IACA;IACA;IACA;MACA;QACA;UACAV;UACApB;QACA;QACA;MACA;QACA2B;QACA;MACA;IACA;EACA;EACAI;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;UACAC;QACA;UACAA;QACA;QACA;QACA;QACA;QACAA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACAD;QACA;QACA;UACA;UACAA;UACAA;UACAA;QACA;QACA;MACA;IACA;IACA;IACAE;MACA;MACAF;MACA;QACAA;QACAA;QACAA;MACA;MACA;IACA;IACA;IACAG;MACA;MACA;QACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACAD;QACA;UACAA;QACA;MACA;MACA;QACAA;MACA;MACA;IACA;IACA;IACAE;MACA;MACA;MACA;QACAN;MACA;MACAA;MACA;QACAA;QACAA;QACAA;QACAA;MACA;MACA;IACA;EACA;EACAO;IAAA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;MACA;MACA;MACAF;QACA;MACA;MACA;QACA;;QAEAG;MAEA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;MACA;MACAA;QACA;QACA;UACAL;YACA;YACA;UACA;QACA;QACA;QACAM;UACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;;QAEA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;QACAC;QACAnD;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACrVA;AAAA;AAAA;AAAA;AAAwsC,CAAgB,8nCAAG,EAAC,C;;;;;;;;;;;ACA5tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-subsection/tn-subsection.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-subsection.vue?vue&type=template&id=d12b2624&scoped=true&\"\nvar renderjs\nimport script from \"./tn-subsection.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-subsection.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-subsection.vue?vue&type=style&index=0&id=d12b2624&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d12b2624\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-subsection/tn-subsection.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-subsection.vue?vue&type=template&id=d12b2624&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.subsectionStyle])\n  var l0 = _vm.__map(_vm.listInfo, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var s1 = _vm.__get_style([_vm.itemStyle(index)])\n    var m0 = _vm.noBorderRight(index)\n    var s2 = _vm.__get_style([_vm.textStyle(index)])\n    return {\n      $orig: $orig,\n      s1: s1,\n      m0: m0,\n      s2: s2,\n    }\n  })\n  var s3 = _vm.__get_style([_vm.itemBarStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        l0: l0,\n        s3: s3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-subsection.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-subsection.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"tn-subsection-class tn-subsection\" :class=\"[subsectionBackgroundColorClass]\"\r\n\t\t:style=\"[subsectionStyle]\">\r\n\t\t<!-- 滑块 -->\r\n\t\t<block v-for=\"(item, index) in listInfo\" :key=\"index\">\r\n\t\t\t<view class=\"tn-subsection__item tn-text-ellipsis\" :class=\"[\r\n          'section-item-' + index,\r\n          noBorderRight(index)\r\n        ]\" :style=\"[itemStyle(index)]\" @tap=\"click(index)\">\r\n\t\t\t\t<view class=\"tn-subsection__item--text tn-text-ellipsis\" :style=\"[textStyle(index)]\">\r\n\t\t\t\t\t{{ item.name }}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t\t<!-- 背景 -->\r\n\t\t<view class=\"tn-subsection__bg\" :class=\"[itemBarClass]\" :style=\"[itemBarStyle]\"></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport componentsColorMixin from '../../libs/mixin/components_color.js'\r\n\texport default {\r\n\t\tmixins: [componentsColorMixin],\r\n\t\tname: 'tn-subsection',\r\n\t\tprops: {\r\n\t\t\t// 模式选择\r\n\t\t\t// button 按钮模式 subsection 分段模式\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'subsection'\r\n\t\t\t},\r\n\t\t\t// 组件高度\r\n\t\t\theight: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 60\r\n\t\t\t},\r\n\t\t\t// tab的数据\r\n\t\t\tlist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 当前活动tab的index\r\n\t\t\tcurrent: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t// 激活时的字体颜色\r\n\t\t\tactiveColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#FFFFFF'\r\n\t\t\t},\r\n\t\t\t// 未激活时的字体颜色\r\n\t\t\tinactiveColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#AAAAAA'\r\n\t\t\t},\r\n\t\t\t// 激活tab的字体是否加粗\r\n\t\t\tbold: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tbackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#F4F4F4'\r\n\t\t\t},\r\n\t\t\t// 滑块的颜色\r\n\t\t\tbuttonColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#01BEFF'\r\n\t\t\t},\r\n\t\t\t// 当mode为button时生效，圆角的值，单位rpx\r\n\t\t\tborderRadius: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 10\r\n\t\t\t},\r\n\t\t\t// 是否开启动画\r\n\t\t\tanimation: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 动画类型\r\n\t\t\t// cubic-bezier -> 贝塞尔曲线\r\n\t\t\tanimationType: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 滑动滑块的是否，是否触发震动\r\n\t\t\tvibrateShort: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 列表数据\r\n\t\t\t\tlistInfo: [],\r\n\t\t\t\t// 子元素的背景样式\r\n\t\t\t\titemBgStyle: {\r\n\t\t\t\t\twidth: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tbackgroundColor: '#ffffff',\r\n\t\t\t\t\theight: '100%'\r\n\t\t\t\t},\r\n\t\t\t\t// 当前选中的滑块\r\n\t\t\t\tcurrentIndex: this.current,\r\n\t\t\t\tbuttonPadding: 3,\r\n\t\t\t\t// 组件初始化的是否current变换不应该震动\r\n\t\t\t\tfirstVibrateShort: true\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tlist: {\r\n\t\t\t\thandler(val) {\r\n\t\t\t\t\tthis.listInfo = val.map((item, index) => {\r\n\t\t\t\t\t\tif (typeof item !== 'object') {\r\n\t\t\t\t\t\t\tlet obj = {\r\n\t\t\t\t\t\t\t\twidth: 0,\r\n\t\t\t\t\t\t\t\tname: item\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn obj\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\titem.width = 0\r\n\t\t\t\t\t\t\treturn obj\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true,\r\n\t\t\t\tdeep: true\r\n\t\t\t},\r\n\t\t\tcurrent: {\r\n\t\t\t\thandler(val) {\r\n\t\t\t\t\tthis.currentIndex = val\r\n\t\t\t\t\tthis.changeSectionStatus(val)\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// 将list的数据，传入listInfo数组\r\n\t\t\t// 接受直接数组形式，或者数组元素为对象的形式，如：['开启', '关闭'],或者[{name: '开启'}, {name: '关闭'}]\r\n\t\t\tthis.listInfo = this.list.map((item, index) => {\r\n\t\t\t\tif (typeof item !== 'object') {\r\n\t\t\t\t\tlet obj = {\r\n\t\t\t\t\t\twidth: 0,\r\n\t\t\t\t\t\tname: item\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn obj\r\n\t\t\t\t} else {\r\n\t\t\t\t\titem.width = 0\r\n\t\t\t\t\treturn obj\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 设置mode=subsection时，滑块没有样式\r\n\t\t\tnoBorderRight() {\r\n\t\t\t\treturn index => {\r\n\t\t\t\t\tif (this.mode !== 'subsection') return\r\n\t\t\t\t\tlet clazz = ''\r\n\t\t\t\t\t// 不显示右边的边距\r\n\t\t\t\t\tif (index < this.list.length - 1) clazz += ' tn-subsection__item--none-border-right'\r\n\t\t\t\t\t// 显示整个组件的左右边圆角\r\n\t\t\t\t\tif (index === 0) clazz += ' tn-subsection__item--first'\r\n\t\t\t\t\tif (index === this.list.length - 1) clazz += ' tn-subsection__item--last'\r\n\t\t\t\t\treturn clazz\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 文字的样式\r\n\t\t\ttextStyle() {\r\n\t\t\t\treturn index => {\r\n\t\t\t\t\tlet style = {}\r\n\t\t\t\t\t// 设置字体颜色\r\n\t\t\t\t\tif (index === this.currentIndex) {\r\n\t\t\t\t\t\tstyle.color = this.activeColor\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tstyle.color = this.inactiveColor\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 字体加粗\r\n\t\t\t\t\tif (index === this.currentIndex && this.bold) style.fontWeight = 'bold'\r\n\t\t\t\t\t// 文字大小\r\n\t\t\t\t\tstyle.fontSize = (this.fontSize || 26) + this.fontUnit\r\n\t\t\t\t\treturn style\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 每个分段器item的样式\r\n\t\t\titemStyle() {\r\n\t\t\t\treturn index => {\r\n\t\t\t\t\tlet style = {}\r\n\t\t\t\t\tif (this.fontSizeStyle) {\r\n\t\t\t\t\t\tstyle.fontSize = this.fontSizeStyle\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (this.mode === 'subsection') {\r\n\t\t\t\t\t\t// 设置border的样式\r\n\t\t\t\t\t\tstyle.borderColor = this.buttonColor\r\n\t\t\t\t\t\tstyle.borderWidth = '1rpx'\r\n\t\t\t\t\t\tstyle.borderStyle = 'solid'\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn style\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// mode = button时，设置外层view的样式\r\n\t\t\tsubsectionStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\tstyle.height = this.height + 'rpx'\r\n\t\t\t\tif (this.mode === 'button') {\r\n\t\t\t\t\tstyle.backgroundColor = this.backgroundColorStyle\r\n\t\t\t\t\tstyle.padding = `${this.buttonPadding}px`\r\n\t\t\t\t\tstyle.borderRadius = `${this.borderRadius}rpx`\r\n\t\t\t\t}\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\t// mode = button时，设置外层view的背景class\r\n\t\t\tsubsectionBackgroundColorClass() {\r\n\t\t\t\tlet clazz = ''\r\n\t\t\t\tif (this.mode === 'button' && this.backgroundColorClass) {\r\n\t\t\t\t\tclazz = this.backgroundColorClass\r\n\t\t\t\t}\r\n\t\t\t\treturn clazz\r\n\t\t\t},\r\n\t\t\titemBarClass() {\r\n\t\t\t\tlet clazz = ''\r\n\t\t\t\tconst buttonBgClass = this.$tn.color.getBackgroundColorInternalClass(this.buttonColor)\r\n\t\t\t\tif (this.animation) {\r\n\t\t\t\t\tclazz += ' tn-subsection__bg__animation'\r\n\t\t\t\t\tif (this.animationType) {\r\n\t\t\t\t\t\tclazz += ` tn-subsection__bg__animation--${this.animationType}`\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (buttonBgClass) {\r\n\t\t\t\t\tclazz += ` ${buttonBgClass}`\r\n\t\t\t\t}\r\n\t\t\t\treturn clazz\r\n\t\t\t},\r\n\t\t\t// 滑块样式\r\n\t\t\titemBarStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\tconst buttonBgStyle = this.$tn.color.getBackgroundColorStyle(this.buttonColor)\r\n\t\t\t\tif (buttonBgStyle) {\r\n\t\t\t\t\tstyle.backgroundColor = this.buttonColor\r\n\t\t\t\t}\r\n\t\t\t\tstyle.zIndex = 1\r\n\t\t\t\tif (this.mode === 'button') {\r\n\t\t\t\t\tstyle.borderRadius = `${this.borderRadius}rpx`\r\n\t\t\t\t\tstyle.bottom = `${this.buttonPadding}px`\r\n\t\t\t\t\tstyle.height = (this.height - (this.buttonPadding * 4)) + 'rpx'\r\n\t\t\t\t\tstyle.zIndex = 0\r\n\t\t\t\t}\r\n\t\t\t\treturn Object.assign(this.itemBgStyle, style)\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// 等待加载组件完成\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.getTabsInfo()\r\n\t\t\t}, 10)\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 改变滑块样式\r\n\t\t\tchangeSectionStatus(val) {\r\n\t\t\t\tif (this.mode === 'subsection') {\r\n\t\t\t\t\t// 根据滑块在最左和最右时，显示对应的圆角\r\n\t\t\t\t\tif (val === this.list.length - 1) {\r\n\t\t\t\t\t\tthis.itemBgStyle.borderRadius = `0 ${this.buttonPadding}px ${this.buttonPadding}px 0`\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (val === 0) {\r\n\t\t\t\t\t\tthis.itemBgStyle.borderRadius = `${this.buttonPadding}px 0 0 ${this.buttonPadding}px`\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (val > 0 && val < this.list.length - 1) {\r\n\t\t\t\t\t\tthis.itemBgStyle.borderRadius = '0'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 更新滑块的位置\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.itemBgLeft()\r\n\t\t\t\t}, 10)\r\n\t\t\t\tif (this.vibrateShort && !this.firstVibrateShort) {\r\n\t\t\t\t\t// 使手机产生短促震动，微信小程序有效，APP(HX 2.6.8)和H5无效\r\n\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\tuni.vibrateShort();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t\tthis.firstVibrateShort = false\r\n\t\t\t},\r\n\t\t\t// 获取各个tab的节点信息\r\n\t\t\tgetTabsInfo() {\r\n\t\t\t\tlet view = uni.createSelectorQuery().in(this)\r\n\t\t\t\tfor (let i = 0; i < this.list.length; i++) {\r\n\t\t\t\t\tview.select('.section-item-' + i).boundingClientRect()\r\n\t\t\t\t}\r\n\t\t\t\tview.exec(res => {\r\n\t\t\t\t\t// 如果没有获取到，则重新获取\r\n\t\t\t\t\tif (!res.length) {\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.getTabsInfo()\r\n\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t}, 10)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 将每个分段器的宽度放入listInfo中\r\n\t\t\t\t\tres.map((item, index) => {\r\n\t\t\t\t\t\tthis.listInfo[index].width = item.width\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// 初始化滑块的宽度\r\n\t\t\t\t\tif (this.mode === 'subsection') {\r\n\t\t\t\t\t\tthis.itemBgStyle.width = this.listInfo[0].width + 'px'\r\n\t\t\t\t\t} else if (this.mode === 'button') {\r\n\t\t\t\t\t\tthis.itemBgStyle.width = this.listInfo[0].width + 'px'\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 初始化滑块的位置\r\n\t\t\t\t\tthis.itemBgLeft()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 设置滑块的位置\r\n\t\t\titemBgLeft() {\r\n\t\t\t\tlet left = 0\r\n\t\t\t\t// 计算当前活跃item到组件左边的距离\r\n\t\t\t\tthis.listInfo.map((item, index) => {\r\n\t\t\t\t\tif (index < this.currentIndex) left += item.width\r\n\t\t\t\t})\r\n\t\t\t\t// 根据不同的模式，计算滑块的位置\r\n\t\t\t\tif (this.mode === 'subsection') {\r\n\t\t\t\t\tthis.itemBgStyle.left = left + 'px'\r\n\t\t\t\t} else if (this.mode === 'button') {\r\n\t\t\t\t\tthis.itemBgStyle.left = left + this.buttonPadding + 'px'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 点击事件\r\n\t\t\tclick(index) {\r\n\t\t\t\t// 不允许点击当前激活的选项\r\n\t\t\t\tif (index === this.currentIndex) return\r\n\t\t\t\tthis.currentIndex = index\r\n\t\t\t\tthis.changeSectionStatus(index)\r\n\t\t\t\tthis.$emit('change', {\r\n\t\t\t\t\tindex: Number(index),\r\n\t\t\t\t\tname: this.listInfo[index]['name']\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.tn-subsection {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\toverflow: hidden;\r\n\t\tposition: relative;\r\n\r\n\t\t&__item {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: row;\r\n\t\t\tflex: 1;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\theight: 100%;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tpadding: 0 6rpx;\r\n\r\n\r\n\t\t\t&--text {\r\n\t\t\t\ttransition: all 0.3s;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tflex-direction: row;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tz-index: 3;\r\n\t\t\t}\r\n\r\n\t\t\t&--first {\r\n\t\t\t\tborder-top-left-radius: 8rpx;\r\n\t\t\t\tborder-bottom-left-radius: 8rpx;\r\n\t\t\t}\r\n\r\n\t\t\t&--last {\r\n\t\t\t\tborder-top-right-radius: 8rpx;\r\n\t\t\t\tborder-bottom-right-radius: 8rpx;\r\n\t\t\t}\r\n\r\n\t\t\t&--none-border-right {\r\n\t\t\t\tborder-right: none !important;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__bg {\r\n\t\t\tbackground-color: $tn-main-color;\r\n\t\t\tposition: absolute;\r\n\t\t\tz-index: -1;\r\n\t\t\ttransition-property: all;\r\n\t\t\ttransition-duration: 0s;\r\n\t\t\ttransition-timing-function: linear;\r\n\r\n\t\t\t&__animation {\r\n\t\t\t\ttransition-duration: 0.25s !important;\r\n\r\n\t\t\t\t&--cubic-bezier {\r\n\t\t\t\t\ttransition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-subsection.vue?vue&type=style&index=0&id=d12b2624&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-subsection.vue?vue&type=style&index=0&id=d12b2624&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980405017\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}