@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.upload.data-v-1dac5edc {
  width: 160rpx;
  height: 160rpx;
  border-radius: 20rpx;
  background-color: #F7F7F7;
  box-sizing: border-box;
  border: 2rpx dashed #999999;
}
.submit_body.data-v-1dac5edc {
  padding: 30rpx;
  box-sizing: border-box;
  background-color: #FFFFFF;
  border-radius: 30rpx;
}
page.data-v-1dac5edc {
  background-color: #f7f7f7 !important;
}
.header.data-v-1dac5edc {
  width: 750rpx;
  padding: 28rpx 0;
  border-radius: 0rpx 0rpx 20rpx 20rpx;
  background-color: #FFFFFF;
}
.headerimg.data-v-1dac5edc {
  width: 670rpx;
  height: 90rpx;
  display: block;
  margin: auto;
}
.brush-title.data-v-1dac5edc {
  line-height: 44rpx;
  font-size: 32rpx;
  color: #333333;
  font-weight: 600;
}
.brush-btn.data-v-1dac5edc {
  width: 200rpx;
  height: 72rpx;
  line-height: 72rpx;
  margin: 24rpx 0 50rpx;
  border-radius: 100rpx;
  color: #fff;
  text-align: center;
  background: linear-gradient(to bottom, #5552FF, #8F8DFF);
}
.brush-msg.data-v-1dac5edc {
  margin-top: 20rpx;
  color: #333333;
  font-size: 28rpx;
  line-height: 50rpx;
}
.brush-msgimg.data-v-1dac5edc {
  width: 170rpx;
  height: 70rpx;
  margin-right: 10rpx;
}
.brush-ts.data-v-1dac5edc {
  margin: 32rpx auto;
  width: 630rpx;
  height: 90rpx;
  line-height: 90rpx;
  padding: 0 20rpx;
  background-color: #FFF7EE;
  color: #FFAE5B;
  font-size: 28rpx;
  border-radius: 10rpx;
}
.fab_btn.data-v-1dac5edc {
  width: 154rpx;
  position: fixed;
  right: 0rpx;
  bottom: 150rpx;
}
.submit.data-v-1dac5edc {
  width: 630rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background-color: #3775F6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #Ffffff;
  margin-top: 30rpx;
}
.nosubmit.data-v-1dac5edc {
  width: 630rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background-color: #9f9f9f;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #Ffffff;
  margin-top: 30rpx;
}
.input_warp.data-v-1dac5edc {
  width: 630rpx;
  height: 114rpx;
  border-radius: 20rpx;
  background-color: #F4F4F4;
  margin-top: 24rpx;
  padding: 0rpx 40rpx;
  box-sizing: border-box;
}
page.data-v-1dac5edc {
  background-color: #Ffffff;
}
.content.data-v-1dac5edc {
  width: 100%;
  box-sizing: border-box;
}

