<template>
	<view>
		<view class="top tn-flex tn-flex-row-between tn-flex-col-center">
			<view class="top_left tn-text-ellipsis tn-text-bold">{{selBookItem.title||""}}</view>
			<view class="top_right tn-flex tn-flex-col-center">
				<view class="tr_word tn-text-ellipsis tn-text-right" @click.stop="showQuest=true">
					{{selTemItem.template_name||""}}
				</view>
				<view class="tn-icon-right"></view>
			</view>
		</view>
		<view class="check_title tn-flex tn-flex-col-center">
			<view class="check_inner tn-flex tn-flex-col-center tn-flex-row-between" @click.stop="showChap=true">
				<view class="tn-text-ellipsis" style="width: 550rpx;">
					{{selChapItem.title||""}}
				</view>
				<view class="tn-icon-right"></view>
			</view>
		</view>
		<view class="check_body tn-flex tn-flex-col-center">
			<view class="tn-width-full">
				<uni-table ref="table" border :emptyText="' '">
					<uni-tr>
						<uni-th width="172rpx" align="center">题号</uni-th>
						<uni-th width="172rpx" align="center">答案</uni-th>
						<uni-th width="172rpx" align="center">题号</uni-th>
						<uni-th width="172rpx" align="center">答案</uni-th>
					</uni-tr>
					<uni-tr v-for="(item, index) in solutLeng" :key="index">
						<uni-td bg="#F0F0F0" align="center"
							v-if=" solutList2[0][index]">{{ solutList2[0][index].title_id }}</uni-td>
						<uni-td align="center"
							v-if=" solutList2[0][index]">{{ $publicjs.numToCode(solutList2[0][index].solution) }}</uni-td>
						<uni-td bg="#F0F0F0" align="center"
							v-if="solutList2[1][index]">{{ solutList2[1][index].title_id }}</uni-td>
						<uni-td align="center"
							v-if="solutList2[1][index]">{{ $publicjs.numToCode(solutList2[1][index].solution) }}</uni-td>
					</uni-tr>
				</uni-table>
			</view>
		</view>
		<tn-popup v-model="showQuest" mode="bottom" :borderRadius="40" safeAreaInsetBottom @close="close">
			<view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				请选择</view>
			<view class="scroll_warp tn-width-full">
				<scroll-view scroll-y="true" style="width: 100%;height: 100%;">
					<view v-for="(item,index) in list_type" :key="index" style="margin-bottom: 40rpx;"
						class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center" @click.stop="selTem(item)">
						<view class="q_pop_left tn-text-ellipsis">
							{{item.template_name}}
						</view>
						<view class="" v-if="item.template_id!=selTemItem1.template_id">
							<image src="../../static/icon/nosel_icon.png" mode="widthFix"
								style="width: 44rpx;height: 44rpx;"></image>
						</view>
						<view class="" v-if="item.template_id==selTemItem1.template_id">
							<image src="../../static/icon/sel_icon.png" mode="widthFix"
								style="width: 44rpx;height: 44rpx;"></image>
						</view>
					</view>
				</scroll-view>
			</view>
			<view class="tn-flex tn-flex-col-top tn-flex-row-center" style="height: 150rpx;">
				<view class="submit" @click="subTem()">保存设置</view>
			</view>
		</tn-popup>
		<tn-select v-model="showChap" mode="single" :list="chapterList" @confirm="confirm" labelName="title"
			valueName="id"></tn-select>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				selBookId: null,
				list_type: [],
				bookDetail: {},
				selBookItem: {},
				selTemItem: {},
				selTemItem1: {},
				chapterList: [],
				selChapItem: {},
				showQuest: false,
				showChap: false,
				solutLeng: 0,
				solutList2: [],
			};
		},
		onLoad(options) {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			if (options.item) {
				this.selBookItem = JSON.parse(options.item)
				this.getBookTem()
			}
		},

		methods: {
			close() {
				this.selTemItem1 = this.selTemItem
			},
			confirm(e) {
				this.selChapItem = this.chapterList.filter(item => {
					return item.id == e[0].value
				})[0]
				this.getgetSolution()
			},
			subTem() {
				this.showQuest = false
				this.selTemItem = this.selTemItem1
				this.getChapterList()
			},
			selTem(item) {
				this.selTemItem1 = item
			},
			getgetSolution() {
				this.$http.post(this.$api.getSolution, {
					chapter_id: this.selChapItem.id
				}).then(res => {
					if (res.code == 200) {
						let arrall = JSON.parse(JSON.stringify(res.data))
						if (arrall.length > 0) {
							if (arrall.length % 2 == 0) {
								this.solutLeng = arrall.length / 2
							} else {
								this.solutLeng = (arrall.length + 1) / 2
							}
						}
						if (arrall.length > 0) {
							let arr1 = []
							let arr2 = []
							arr1 = arrall.filter((item, index) => {
								return index % 2 == 0
							})
							arr2 = arrall.filter((item, index) => {
								return index % 2 > 0
							})
							this.solutList2 = [arr1, arr2]
						}
					}
				})
			},
			getBookTem() { //获取书籍模板
				this.$http.post(this.$api.bookTem, {
					book_id: this.selBookItem.id
				}).then(res => {
					if (res.code == 200) {
						this.list_type = res.data
						this.selTemItem = this.list_type[0]
						this.selTemItem1 = this.list_type[0]
						this.getChapterList()
					}
				})
			},
			getChapterList() {
				this.$http.post(this.$api.chapterList, {
					book_id: this.selBookItem.id,
					template_id: this.selTemItem.template_id
				}).then(res => {
					if (res.code == 200) {
						this.chapterList = res.data
						this.selChapItem = res.data[0]
						this.getgetSolution()
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.submit {
		width: 630rpx;
		height: 96rpx;
		border-radius: 48rpx;
		background: #5552FF;
		font-size: 32rpx;
		color: #FFFFFF;
		text-align: center;
		line-height: 96rpx;
	}

	.q_pop_left {
		width: 550rpx;
	}

	.scroll_warp {
		height: 450rpx;
		padding: 0rpx 40rpx 40rpx 40rpx;
		box-sizing: border-box;
	}

	.scroll_warp2 {
		height: 300rpx;
		padding: 0rpx 40rpx 40rpx 40rpx;
		box-sizing: border-box;
	}

	.check_body {
		margin-top: 20rpx;
		width: 750rpx;
		padding: 30rpx;
		background-color: #FFFFFF;
	}

	.check_inner {
		width: 690rpx;
		height: 100rpx;
		border-radius: 20rpx;
		background-color: #E3E3FF;
		color: #5552FF;
		font-size: 28rpx;
		padding: 0rpx 30rpx;
		font-size: 28rpx;
		font-weight: bold;
	}

	.check_title {
		width: 750rpx;
		height: 140rpx;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
		background-color: #FFFFFF;
		padding: 0rpx 30rpx;
	}

	.top {
		width: 750rpx;
		height: 100rpx;
		background-color: #FFFFFF;
		padding: 0rpx 40rpx;
		box-sizing: border-box;

		.top_left {
			color: #222222;
			font-size: 30rpx;
			width: 350rpx;
		}

		.top_right {
			color: #666666;
			font-size: 28rpx;

			.tr_word {
				width: 250rpx;
			}
		}
	}
</style>