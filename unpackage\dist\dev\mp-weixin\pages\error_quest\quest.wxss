@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.sel_no.data-v-67f73f95 {
  width: 200rpx;
  height: 80rpx;
  border-radius: 55rpx;
  background-color: #F1F1F1;
  text-align: center;
  line-height: 80rpx;
  color: #666666;
  font-size: 28rpx;
}
.sel_is.data-v-67f73f95 {
  width: 200rpx;
  height: 80rpx;
  border-radius: 55rpx;
  background-color: #5552FF;
  text-align: center;
  line-height: 80rpx;
  color: #FFFFFF;
  font-size: 28rpx;
}
.scroll_warp2.data-v-67f73f95 {
  width: 690rpx;
  height: 384rpx;
  border-radius: 20rpx;
  background-color: #F8F8F8;
  margin: 0rpx auto;
  margin-bottom: 26rpx;
  padding: 30rpx;
  box-sizing: border-box;
}
.submit.data-v-67f73f95 {
  width: 630rpx;
  height: 96rpx;
  border-radius: 48rpx;
  background-color: #5552FF;
  font-size: 16px;
  color: #FFFFFF;
  text-align: center;
  line-height: 96rpx;
}
.mine_note_top.data-v-67f73f95 {
  width: 690rpx;
  height: 66rpx;
  border-radius: 55rpx;
  background-color: #E3E3FF;
  padding: 0rpx 30rpx;
}
.add_note.data-v-67f73f95 {
  width: 146rpx;
  height: 50rpx;
  border-radius: 55rpx;
  background-color: #E3E3FF;
  font-size: 24rpx;
  color: #5552FF;
}
.no_sel.data-v-67f73f95 {
  color: #333333;
  font-size: 24rpx;
}
.is_sel.data-v-67f73f95 {
  color: #5552FF;
  font-size: 24rpx;
}
.answer_history.data-v-67f73f95 {
  width: 750rpx;
  background-color: #FFFFFF;
  margin: 20rpx 0rpx;
  padding: 30rpx;
  box-sizing: border-box;
}
.answer_mask.data-v-67f73f95 {
  position: absolute;
  top: 0rpx;
  bottom: 0rpx;
  left: 0rpx;
  right: 0rpx;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.05) 100%);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  z-index: 100;
}
.answer_mask_title.data-v-67f73f95 {
  position: absolute;
  top: 0rpx;
  bottom: 0rpx;
  left: 0rpx;
  right: 0rpx;
  z-index: 120;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333333;
}
.answer_warp.data-v-67f73f95 {
  position: relative;
}
.suc_word.data-v-67f73f95 {
  width: 550rpx;
}
.nom_bg.data-v-67f73f95 {
  background-color: #F7F7F7;
}
.err_bg.data-v-67f73f95 {
  background-color: #fff6e4;
}
.suc_bg.data-v-67f73f95 {
  background-color: #DDFFF5;
}
.answer_item.data-v-67f73f95 {
  width: 690rpx;
  border-radius: 20rpx;
  padding: 40rpx;
  box-sizing: border-box;
  margin-bottom: 40rpx;
  color: #333333;
  font-size: 28rpx;
}
.cri_red.data-v-67f73f95 {
  width: 14rpx;
  height: 14rpx;
  box-sizing: border-box;
  border-radius: 50%;
  border: 2rpx solid #FF585F;
  margin-right: 10rpx;
}
.cri_blue.data-v-67f73f95 {
  width: 14rpx;
  height: 14rpx;
  box-sizing: border-box;
  border-radius: 50%;
  border: 2rpx solid #5552FF;
  margin-right: 10rpx;
}
.cri_gary.data-v-67f73f95 {
  width: 14rpx;
  height: 14rpx;
  box-sizing: border-box;
  border-radius: 50%;
  border: 2rpx solid #C8C8C8;
  margin-right: 10rpx;
}
.card_no.data-v-67f73f95 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 55rpx;
  background-color: #C8C8C8;
  text-align: center;
  line-height: 100rpx;
  font-size: 32rpx;
  color: #FFFFFF;
  margin-bottom: 40rpx;
  margin-left: 20rpx;
}
.card_now.data-v-67f73f95 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 55rpx;
  text-align: center;
  line-height: 100rpx;
  font-size: 32rpx;
  color: #5552FF;
  margin-bottom: 40rpx;
  margin-left: 20rpx;
  border: 2rpx solid #5552FF;
}
.card_sub.data-v-67f73f95 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 55rpx;
  text-align: center;
  line-height: 100rpx;
  font-size: 32rpx;
  color: #FF585F;
  margin-bottom: 40rpx;
  margin-left: 20rpx;
  border: 2rpx solid #FF585F;
}
.scroll_warp.data-v-67f73f95 {
  height: 450rpx;
  padding: 0rpx 40rpx 40rpx 40rpx;
  box-sizing: border-box;
}
.quest_title.data-v-67f73f95 {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 50rpx;
}
.quest_warp.data-v-67f73f95 {
  margin-top: 50rpx;
}
.correction.data-v-67f73f95 {
  width: 122rpx;
  height: 44rpx;
  border-radius: 10rpx;
  background-color: #EEEEEE;
  text-align: center;
  line-height: 44rpx;
  color: #A1A1A1;
  font-size: 26rpx;
  margin-left: 48rpx;
}
.type_tips.data-v-67f73f95 {
  width: 114rpx;
  height: 52rpx;
  border-radius: 26rpx 0rpx 26rpx 0rpx;
  background-color: #5552FF;
  text-align: center;
  line-height: 52rpx;
  color: #ffffff;
  font-size: 28rpx;
  margin-right: 30rpx;
}
.quest_primary.data-v-67f73f95 {
  width: 750rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  border-radius: 0rpx 0rpx 20rpx 20rpx;
  overflow: hidden;
}

