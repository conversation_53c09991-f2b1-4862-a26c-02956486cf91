@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.scroll_warp2.data-v-1b27d1e7 {
  width: 690rpx;
  height: 384rpx;
  border-radius: 20rpx;
  background-color: #F8F8F8;
  margin: 0rpx auto;
  margin-bottom: 26rpx;
  padding: 30rpx;
  box-sizing: border-box;
}
.submit.data-v-1b27d1e7 {
  width: 630rpx;
  height: 96rpx;
  border-radius: 48rpx;
  background-color: #7270FF;
  font-size: 16px;
  color: #FFFFFF;
  text-align: center;
  line-height: 96rpx;
}
.quest_suc.data-v-1b27d1e7 {
  background-color: #5552FF;
}
.quest_err.data-v-1b27d1e7 {
  background-color: #FF585F;
}
.f_btn1.data-v-1b27d1e7 {
  width: 330rpx;
  height: 80rpx;
  border-radius: 55rpx;
  box-sizing: border-box;
  border: 2rpx solid #3775F6;
  text-align: center;
  line-height: 80rpx;
  color: #5552FF;
  font-size: 32rpx;
}
.f_btn2.data-v-1b27d1e7 {
  width: 330rpx;
  height: 80rpx;
  border-radius: 55rpx;
  box-sizing: border-box;
  border: 2rpx solid #5552FF;
  background-color: #5552FF;
  text-align: center;
  line-height: 80rpx;
  color: #FFFFFF;
  font-size: 32rpx;
}
.footer_model.data-v-1b27d1e7 {
  width: 750rpx;
  height: 120rpx;
  background-color: #FFFFFF;
  position: fixed;
  bottom: 0rpx;
  left: 0rpx;
  right: 0rpx;
  padding: 0rpx 30rpx;
}
.quest_or.data-v-1b27d1e7 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  color: #FFFFFF;
  font-size: 32rpx;
  line-height: 100rpx;
  text-align: center;
  margin-top: 40rpx;
  margin-left: 10rpx;
  margin-right: 10rpx;
}
.h_btn1.data-v-1b27d1e7 {
  width: 286rpx;
  height: 96rpx;
  border-radius: 50rpx;
  background-color: #F1F0FF;
  text-align: center;
  line-height: 96rpx;
  color: #7270FF;
  font-size: 32rpx;
}
.h_btn2.data-v-1b27d1e7 {
  width: 286rpx;
  height: 96rpx;
  border-radius: 50rpx;
  background-color: #7270FF;
  text-align: center;
  line-height: 96rpx;
  color: #FFFFFF;
  font-size: 32rpx;
}
.title.data-v-1b27d1e7 {
  font-size: 26rpx;
  color: #666666;
}
.num.data-v-1b27d1e7 {
  color: #333333;
  font-size: 48rpx;
  font-weight: bold;
  margin-top: 25rpx;
}
.card_warp.data-v-1b27d1e7 {
  width: 750rpx;
  background-color: #FFFFFF;
  padding: 30rpx;
  border-radius: 20rpx;
  box-sizing: border-box;
}
.header_bottom.data-v-1b27d1e7 {
  width: 690rpx;
  height: 320rpx;
  border-radius: 10rpx;
  background-color: #FFFFFF;
  margin-top: 4rpx;
  padding: 30rpx;
  box-sizing: border-box;
}
.header_top.data-v-1b27d1e7 {
  width: 690rpx;
  height: 320rpx;
  border-radius: 20rpx;
  background-color: #FFFFFF;
  padding: 30rpx;
  box-sizing: border-box;
}
.header.data-v-1b27d1e7 {
  width: 750rpx;
  background: linear-gradient(to bottom, #FFE8E4, #FFE8E4) no-repeat;
  background-size: 100% 274rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}

