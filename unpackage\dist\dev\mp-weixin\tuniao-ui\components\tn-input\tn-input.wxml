<view data-event-opts="{{[['tap',[['inputClick',['$event']]]]]}}" class="{{['tn-input-class','tn-input','data-v-628dad5c',(border)?'tn-input--border':'',(validateState)?'tn-input--error':'']}}" style="{{'padding:'+('0 '+(border?20:0)+'rpx')+';'+('border-color:'+(borderColor)+';')+('text-align:'+(inputAlign)+';')}}" catchtap="__e"><block wx:if="{{showLeftIcon}}"><view class="tn-input__left-icon__item tn-input__left-icon__clear data-v-628dad5c"><tn-button vue-id="21af01ac-1" shape="icon" scene="{{scene}}" block-time="{{blockTime}}" data-event-opts="{{[['^click',[['leftIconClick']]]]}}" bind:click="__e" class="data-v-628dad5c" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['icon','data-v-628dad5c','tn-icon-'+leftIcon]}}"></view></tn-button></view></block><block wx:if="{{type==='textarea'}}"><textarea class="tn-input__input tn-input__textarea data-v-628dad5c" style="{{$root.s0}}" placeholder="{{placeholder}}" placeholderStyle="{{placeholderStyle}}" disabled="{{disabled||type==='select'}}" maxlength="{{maxLength}}" fixed="{{fixed}}" focus="{{focus}}" autoHeight="{{autoHeight}}" selectionStart="{{elSelectionStart}}" selectionEnd="{{elSelectionEnd}}" cursorSpacing="{{cursorSpacing}}" showConfirmBar="{{showConfirmBar}}" data-event-opts="{{[['input',[['handleInput',['$event']]]],['blur',[['handleBlur',['$event']]]],['focus',[['onFocus',['$event']]]],['confirm',[['onConfirm',['$event']]]]]}}" value="{{defaultValue}}" bindinput="__e" bindblur="__e" bindfocus="__e" bindconfirm="__e"></textarea></block><block wx:else><view style="width:100%;" class="data-v-628dad5c"><block wx:if="{{type==='select'}}"><view class="tn-input__text data-v-628dad5c">{{''+defaultValue+''}}</view></block><block wx:else><input class="tn-input__input data-v-628dad5c" style="{{$root.s1}}" type="{{type==='password'?'text':type}}" password="{{type==='password'&&!showPassword}}" placeholder="{{placeholder}}" placeholderStyle="{{placeholderStyle}}" disabled="{{disabled||type==='select'}}" maxlength="{{maxLength}}" focus="{{focus}}" confirmType="{{confirmType}}" selectionStart="{{elSelectionStart}}" selectionEnd="{{elSelectionEnd}}" cursorSpacing="{{cursorSpacing}}" showConfirmBar="{{showConfirmBar}}" data-event-opts="{{[['input',[['handleInput',['$event']]]],['blur',[['handleBlur',['$event']]]],['focus',[['onFocus',['$event']]]],['confirm',[['onConfirm',['$event']]]]]}}" value="{{defaultValue}}" bindinput="__e" bindblur="__e" bindfocus="__e" bindconfirm="__e"/></block></view></block><view class="tn-input__right-icon tn-flex tn-flex-col-center data-v-628dad5c"><block wx:if="{{clearable&&value!==''&&focused}}"><view data-event-opts="{{[['tap',[['onClear',['$event']]]]]}}" class="tn-input__right-icon__item tn-input__right-icon__clear data-v-628dad5c" bindtap="__e"><view class="icon tn-icon-close data-v-628dad5c"></view></view></block><block wx:else><block wx:if="{{type==='text'&&!focused&&showRightIcon&&rightIcon!==''}}"><view class="tn-input__right-icon__item tn-input__right-icon__clear data-v-628dad5c"><tn-button vue-id="21af01ac-2" shape="icon" scene="{{scene}}" block-time="{{blockTime}}" data-event-opts="{{[['^click',[['rightIconClick']]]]}}" bind:click="__e" class="data-v-628dad5c" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['icon','data-v-628dad5c','tn-icon-'+rightIcon]}}"></view></tn-button></view></block></block><block wx:if="{{passwordIcon&&type==='password'}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="tn-input__right-icon__item tn-input__right-icon__clear data-v-628dad5c" bindtap="__e"><block wx:if="{{!showPassword}}"><view class="tn-icon-eye-hide data-v-628dad5c"></view></block><block wx:else><view class="icon tn-icon-eye data-v-628dad5c"></view></block></view></block><block wx:if="{{type==='select'}}"><view class="{{['tn-input__right-icon__item','tn-input__right-icon__select','data-v-628dad5c',(selectOpen)?'tn-input__right-icon__select--reverse':'']}}"><view class="icon tn-icon-up-triangle data-v-628dad5c"></view></view></block></view></view>