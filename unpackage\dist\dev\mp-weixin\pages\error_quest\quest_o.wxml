<view style="padding-bottom:140rpx;" class="data-v-cb7eef76"><view class="quest_primary tn-bg-white data-v-cb7eef76"><view class="tn-width-full tn-flex data-v-cb7eef76" style="margin-bottom:30rpx;"><view style="width:600rpx;" class="data-v-cb7eef76"><tn-line-progress vue-id="a45dfd3e-1" percent="{{percent}}" height="{{20}}" activeColor="#5552FF" inactiveColor="#F5F5F5" class="data-v-cb7eef76" bind:__l="__l"></tn-line-progress></view><text style="margin-left:40rpx;font-size:28rpx;color:#999999;" class="data-v-cb7eef76"><text style="color:#FF000A;" class="data-v-cb7eef76">{{questIndex+1}}</text><text style="margin:0rpx 5rpx;" class="data-v-cb7eef76">/</text>{{''+$root.g0+''}}</text></view><view class="qp_top tn-flex tn-flex-row-between tn-flex-col-center data-v-cb7eef76"><view class="qpt_left tn-flex tn-flex-col-center data-v-cb7eef76"><view class="type_tips data-v-cb7eef76"><block wx:if="{{questItem.type==0}}"><text class="data-v-cb7eef76">单选题</text></block><block wx:if="{{questItem.type==1}}"><text class="data-v-cb7eef76">多选题</text></block><block wx:if="{{questItem.type==2}}"><text class="data-v-cb7eef76">不定项</text></block></view><view class="tn-flex tn-flex-col-center data-v-cb7eef76" style="font-size:28rpx;color:#999999;"><view class="tn-icon-clock-fill data-v-cb7eef76" style="font-size:36rpx;"></view><view style="margin-left:5rpx;" class="data-v-cb7eef76"><bing-countup vue-id="a45dfd3e-2" show-hour="{{showHour}}" data-ref="countUp" data-event-opts="{{[['^change',[['onChangeTime']]]]}}" bind:change="__e" class="data-v-cb7eef76 vue-ref" bind:__l="__l"></bing-countup></view></view></view><view class="tn-flex tn-flex-col-center data-v-cb7eef76"><block wx:if="{{questItem.is_collect==1}}"><view data-event-opts="{{[['tap',[['toColl']]]]}}" style="font-size:28rpx;color:#A1A1A1;" catchtap="__e" class="data-v-cb7eef76"><text class="tn-icon-star-fill data-v-cb7eef76" style="color:#FFBD23;"></text>已收藏</view></block><block wx:if="{{questItem.is_collect==0}}"><view data-event-opts="{{[['tap',[['toColl']]]]}}" style="font-size:28rpx;color:#A1A1A1;" catchtap="__e" class="data-v-cb7eef76"><text class="tn-icon-star-fill data-v-cb7eef76" style="color:#A1A1A1;"></text>收藏</view></block><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="correction data-v-cb7eef76" catchtap="__e"><text class="tn-icon-help-fill data-v-cb7eef76" style="margin-right:4rpx;"></text>纠错</view></view></view><view class="quest_warp data-v-cb7eef76"><view class="quest_title data-v-cb7eef76">{{''+(questItem.title_id+"、")+(questItem.title||'')+''}}</view><block wx:if="{{$root.g1}}"><view class="tn-width-full data-v-cb7eef76" style="margin-top:20rpx;margin-bottom:20rpx;"><block wx:for="{{questItem.title_img}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tn-width-full data-v-cb7eef76"><image style="width:100%;" src="{{baseUrl+item}}" mode="widthFix" class="data-v-cb7eef76"></image></view></block></view></block><block wx:if="{{questItem.type==0}}"><view class="tn-width-full data-v-cb7eef76"><block wx:if="{{questItem.type==0}}"><answer-com bind:selAnswer="__e" vue-id="a45dfd3e-3" data-ref="exeAnsCom" data-event-opts="{{[['^selAnswer',[['nextQuest']]]]}}" class="data-v-cb7eef76 vue-ref" bind:__l="__l"></answer-com></block></view></block><block wx:if="{{questItem.type==1||questItem.type==2}}"><view class="tn-width-full data-v-cb7eef76"><answer-com-tum bind:selAnswer="__e" vue-id="a45dfd3e-4" data-ref="exeAnsComTum" data-event-opts="{{[['^selAnswer',[['nextQuestTum']]]]}}" class="data-v-cb7eef76 vue-ref" bind:__l="__l"></answer-com-tum></view></block></view></view><quest-fixed vue-id="a45dfd3e-5" end="{{$root.g2?true:false}}" data-event-opts="{{[['^nextQuest',[['nextQuestBefore']]],['^lastQuest',[['lastQuestBefore']]],['^showCard',[['e1']]]]}}" bind:nextQuest="__e" bind:lastQuest="__e" bind:showCard="__e" class="data-v-cb7eef76" bind:__l="__l"></quest-fixed><tn-popup bind:input="__e" vue-id="a45dfd3e-6" mode="bottom" borderRadius="{{40}}" safeAreaInsetBottom="{{true}}" value="{{showCard}}" data-event-opts="{{[['^input',[['__set_model',['','showCard','$event',[]]]]]]}}" class="data-v-cb7eef76" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-cb7eef76" style="width:750rpx;height:120rpx;padding:30rpx;box-sizing:border-box;background:url('../../static/pop_head.png') no-repeat;background-size:100% 100%;"><text class="tn-text-bold data-v-cb7eef76">答题卡</text><view class="tn-flex tn-flex-col-center tn-flex-row-between data-v-cb7eef76" style="width:300rpx;"><view class="tn-flex tn-flex-col-center data-v-cb7eef76"><view class="cri_red data-v-cb7eef76"></view><view style="font-size:24rpx;color:#333333;" class="data-v-cb7eef76">已交</view></view><view class="tn-flex tn-flex-col-center data-v-cb7eef76"><view class="cri_blue data-v-cb7eef76"></view><view style="font-size:24rpx;color:#333333;" class="data-v-cb7eef76">当前</view></view><view class="tn-flex tn-flex-col-center data-v-cb7eef76"><view class="cri_gary data-v-cb7eef76"></view><view style="font-size:24rpx;color:#333333;" class="data-v-cb7eef76">未答</view></view></view></view><view class="scroll_warp data-v-cb7eef76"><scroll-view style="width:100%;height:100%;" scroll-y="true" class="data-v-cb7eef76"><view class="tn-flex tn-flex-wrap tn-flex-row-between data-v-cb7eef76"><block wx:for="{{questList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['jumpQuest',[index]]]]]}}" class="{{['data-v-cb7eef76',questIndex!=index?answerAll[item.id]!==null?'card_sub':'card_no':'card_now']}}" catchtap="__e">{{''+item.title_id+''}}</view></block><block wx:for="{{5-$root.g3%5}}" wx:for-item="item" wx:for-index="index"><view style="width:100rpx;margin-bottom:40rpx;margin-left:20rpx;" class="data-v-cb7eef76"></view></block></view></scroll-view></view></tn-popup><tn-popup bind:input="__e" vue-id="a45dfd3e-7" mode="bottom" borderRadius="{{40}}" safeAreaInsetBottom="{{true}}" value="{{showError}}" data-event-opts="{{[['^input',[['__set_model',['','showError','$event',[]]]]]]}}" class="data-v-cb7eef76" bind:__l="__l" vue-slots="{{['default']}}"><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-cb7eef76" style="width:750rpx;height:120rpx;padding:30rpx;box-sizing:border-box;background:url('../../static/pop_head.png') no-repeat;background-size:100% 100%;"><text class="tn-text-bold data-v-cb7eef76">反馈</text></view><view class="tn-width-full tn-flex tn-flex-direction-column data-v-cb7eef76" style="padding:0rpx 30rpx 30rpx 30rpx;box-sizing:border-box;"><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-cb7eef76"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="{{['data-v-cb7eef76',selError=='有错别字'?'sel_is':'sel_no']}}" catchtap="__e">有错别字</view><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="{{['data-v-cb7eef76',selError=='题干有误'?'sel_is':'sel_no']}}" catchtap="__e">题干有误</view><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="{{['data-v-cb7eef76',selError=='答案有误'?'sel_is':'sel_no']}}" catchtap="__e">答案有误</view></view><view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between data-v-cb7eef76" style="margin-top:30rpx;"><view data-event-opts="{{[['tap',[['e5',['$event']]]]]}}" class="{{['data-v-cb7eef76',selError=='解析有误'?'sel_is':'sel_no']}}" catchtap="__e">解析有误</view><view data-event-opts="{{[['tap',[['e6',['$event']]]]]}}" class="{{['data-v-cb7eef76',selError=='解析缺失'?'sel_is':'sel_no']}}" catchtap="__e">解析缺失</view><view data-event-opts="{{[['tap',[['e7',['$event']]]]]}}" class="{{['data-v-cb7eef76',selError=='选择有误'?'sel_is':'sel_no']}}" catchtap="__e">选择有误</view></view></view><view class="scroll_warp2 tn-width-full data-v-cb7eef76"><tn-input bind:input="__e" vue-id="{{('a45dfd3e-8')+','+('a45dfd3e-7')}}" placeholder="开始输入..." clearable="{{false}}" type="textarea" border="{{false}}" height="{{324}}" autoHeight="{{false}}" value="{{err_value}}" data-event-opts="{{[['^input',[['__set_model',['','err_value','$event',[]]]]]]}}" class="data-v-cb7eef76" bind:__l="__l"></tn-input></view><view class="tn-flex tn-flex-col-top tn-flex-row-center data-v-cb7eef76" style="height:150rpx;"><view data-event-opts="{{[['tap',[['submitErrorQuest']]]]}}" class="submit data-v-cb7eef76" catchtap="__e">立即反馈</view></view></tn-popup></view>