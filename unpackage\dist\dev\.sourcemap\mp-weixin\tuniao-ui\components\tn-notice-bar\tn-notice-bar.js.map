{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-notice-bar/tn-notice-bar.vue?9709", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-notice-bar/tn-notice-bar.vue?0d02", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-notice-bar/tn-notice-bar.vue?6082", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-notice-bar/tn-notice-bar.vue?642f", "uni-app:///tuniao-ui/components/tn-notice-bar/tn-notice-bar.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-notice-bar/tn-notice-bar.vue?4a39", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-notice-bar/tn-notice-bar.vue?7c1c"], "names": ["name", "mixins", "props", "list", "type", "default", "keyName", "valueName", "keyValue", "show", "playStatus", "mode", "leftIcon", "leftIconName", "leftIconSize", "rightIcon", "rightIconName", "rightIconSize", "closeBtn", "radius", "padding", "autoplay", "duration", "speed", "circular", "autoHidden", "computed", "showNotice", "watch", "handler", "deep", "data", "valueList", "mounted", "methods", "click", "findValue", "objList", "close", "isObj", "clickLeftIcon", "clickRightIcon", "end", "loadList", "tmpList"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,aAAa,sQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAynB,CAAgB,unBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACkE7oB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;EACAA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;EACA;EACAqB;IACA;IACAC;MACA;IACA;EACA;EACAC;IACApB;MACAqB;QACA;MACA;MACAC;IACA;IACA3B;MACA0B;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;MACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;UACA;UACA;YACAC;UACA;QACA;UACA;UACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;UACA;UACA;YACAC;UACA;QACA;UACAA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC9QA;AAAA;AAAA;AAAA;AAAwsC,CAAgB,8nCAAG,EAAC,C;;;;;;;;;;;ACA5tC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-notice-bar/tn-notice-bar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-notice-bar.vue?vue&type=template&id=b91dbbc0&scoped=true&\"\nvar renderjs\nimport script from \"./tn-notice-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-notice-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-notice-bar.vue?vue&type=style&index=0&id=b91dbbc0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b91dbbc0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-notice-bar/tn-notice-bar.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-notice-bar.vue?vue&type=template&id=b91dbbc0&scoped=true&\"", "var components\ntry {\n  components = {\n    tnRowNotice: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-row-notice/tn-row-notice\" */ \"@/tuniao-ui/components/tn-row-notice/tn-row-notice.vue\"\n      )\n    },\n    tnColumnNotice: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-column-notice/tn-column-notice\" */ \"@/tuniao-ui/components/tn-column-notice/tn-column-notice.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-notice-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-notice-bar.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view\r\n    v-if=\"showNotice\"\r\n    class=\"tn-notice-bar-class tn-notice-bar\"\r\n    :style=\"{\r\n      borderRadius: radius + 'rpx'\r\n    }\"\r\n  >\r\n    <block v-if=\"mode === 'horizontal' && circular\">\r\n      <tn-row-notice\r\n        :backgroundColor=\"backgroundColor\"\r\n        :fontColor=\"fontColor\"\r\n        :fontSize=\"fontSize\"\r\n        :fontUnit=\"fontUnit\"\r\n        :list=\"valueList\"\r\n        :show=\"show\"\r\n        :playStatus=\"playStatus\"\r\n        :leftIcon=\"leftIcon\"\r\n        :leftIconName=\"leftIconName\"\r\n        :leftIconSize=\"leftIconSize\"\r\n        :rightIcon=\"rightIcon\"\r\n        :rightIconName=\"rightIconName\"\r\n        :rightIconSize=\"rightIconSize\"\r\n        :closeBtn=\"closeBtn\"\r\n        :autoplay=\"autoplay\"\r\n        :radius=\"radius\"\r\n        :padding=\"padding\"\r\n        :speed=\"speed\"\r\n        @click=\"click\"\r\n        @close=\"close\"\r\n        @clickLeft=\"clickLeftIcon\"\r\n        @clickRight=\"clickRightIcon\"\r\n      ></tn-row-notice>\r\n    </block>\r\n    <block v-if=\"mode === 'vertical' || (mode === 'horizontal' && !circular)\">\r\n      <tn-column-notice\r\n        :backgroundColor=\"backgroundColor\"\r\n        :fontColor=\"fontColor\"\r\n        :fontSize=\"fontSize\"\r\n        :fontUnit=\"fontUnit\"\r\n        :list=\"valueList\"\r\n        :show=\"show\"\r\n        :mode=\"mode\"\r\n        :playStatus=\"playStatus\"\r\n        :leftIcon=\"leftIcon\"\r\n        :leftIconName=\"leftIconName\"\r\n        :leftIconSize=\"leftIconSize\"\r\n        :rightIcon=\"rightIcon\"\r\n        :rightIconName=\"rightIconName\"\r\n        :rightIconSize=\"rightIconSize\"\r\n        :closeBtn=\"closeBtn\"\r\n        :autoplay=\"autoplay\"\r\n        :radius=\"radius\"\r\n        :padding=\"padding\"\r\n        :duration=\"duration\"\r\n        @click=\"click\"\r\n        @close=\"close\"\r\n        @clickLeft=\"clickLeftIcon\"\r\n        @clickRight=\"clickRightIcon\"\r\n        @end=\"end\"\r\n      ></tn-column-notice>\r\n    </block>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import componentsColorMixin from '../../libs/mixin/components_color.js'\r\n  export default {\r\n    name: 'tn-notice-bar',\r\n    mixins: [componentsColorMixin],\r\n    props: {\r\n      // 显示的内容\r\n      list: {\r\n        type: Array,\r\n        default() {\r\n          return []\r\n        }\r\n      },\r\n      keyName:{\r\n        type:String,\r\n        default: 'key'\r\n      },\r\n      valueName:{\r\n        type:String,\r\n        default: 'value'\r\n      },\r\n      keyValue:{\r\n        type:String,\r\n        default: undefined\r\n      },\r\n      // 是否显示\r\n      show: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 播放状态\r\n      // play -> 播放 paused -> 暂停\r\n      playStatus: {\r\n        type: String,\r\n        default: 'play'\r\n      },\r\n      // 滚动方向\r\n      // horizontal -> 水平滚动 vertical -> 垂直滚动\r\n      mode: {\r\n        type: String,\r\n        default: 'horizontal'\r\n      },\r\n      // 是否显示左边图标\r\n      leftIcon: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 左边图标的名称\r\n      leftIconName: {\r\n        type: String,\r\n        default: 'sound'\r\n      },\r\n      // 左边图标的大小\r\n      leftIconSize: {\r\n        type: Number,\r\n        default: 34\r\n      },\r\n      // 是否显示右边的图标\r\n      rightIcon: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 右边图标的名称\r\n      rightIconName: {\r\n        type: String,\r\n        default: 'right'\r\n      },\r\n      // 右边图标的大小\r\n      rightIconSize: {\r\n        type: Number,\r\n        default: 26\r\n      },\r\n      // 是否显示关闭按钮\r\n      closeBtn: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 圆角\r\n      radius: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // 内边距\r\n      padding: {\r\n        type: String,\r\n        default: '18rpx 24rpx'\r\n      },\r\n      // 自动播放\r\n      autoplay: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 滚动周期\r\n      duration: {\r\n        type: Number,\r\n        default: 2000\r\n      },\r\n      // 水平滚动时的速度，即每秒滚动多少rpx\r\n      speed: {\r\n        type: Number,\r\n        default: 160\r\n      },\r\n      // 水平滚动的时候是否采用衔接的模式\r\n      circular: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 没有数据时是否显示通知\r\n      autoHidden: {\r\n        type: Boolean,\r\n        default: true\r\n      }\r\n    },\r\n    computed: {\r\n      // 当设置了show为false，或者autoHidden为true且list为空时，不显示通知\r\n      showNotice() {\r\n         return !(this.show === false || (this.autoHidden && this.list.length === 0))\r\n      }\r\n    },\r\n    watch:{\r\n      keyValue:{\r\n        handler(value) {\r\n            this.loadList();\r\n        },\r\n        deep: true\r\n      },\r\n      list:{\r\n        handler(value) {\r\n          this.loadList();\r\n        },\r\n        deep: true\r\n      },\r\n    },\r\n    data() {\r\n      return {\r\n        //显示的值\r\n        valueList:[],\r\n      }\r\n    },\r\n    mounted() {\r\n      this.loadList();\r\n    },\r\n    methods: {\r\n      // 点击了通知栏\r\n      click(index) {\r\n\t\tlet value = this.findValue(index);\r\n        //如果是对象，返回传入的对象\r\n        if (this.isObj(value)){\r\n          this.$emit('click', value)\r\n        }else {\r\n          this.$emit('click', index)\r\n        }\r\n      },\r\n\t  findValue(findIndex){\r\n\t\t  let objList = []\r\n\t\t  for (let index in this.list){\r\n\t\t    let v = this.list[index];\r\n\t\t    if (this.isObj(v)){\r\n\t\t      //判断是否指定key显示，如果是则显示指定的列表，否则就返回所有列表\r\n\t\t      if (this.keyValue == undefined || v[this.keyName] == this.keyValue){\r\n\t\t\t\t\t    objList.push(v);\r\n\t\t      }\r\n\t\t    }else{\r\n\t\t\t\t //兼容旧的，旧的直接返回下表\r\n\t\t\t\treturn findIndex;\r\n\t\t\t}\r\n\t\t  }\r\n\t\t  return findIndex >= objList.length ? undefined : objList[findIndex];\r\n\t  },\r\n      // 点击了关闭按钮\r\n      close() {\r\n        this.$emit('close')\r\n      },\r\n      //判断元素是否是对象\r\n      isObj(value){\r\n        return typeof(value) === 'object';\r\n      },\r\n      // 点击了左边图标\r\n      clickLeftIcon() {\r\n        this.$emit('clickLeft')\r\n      },\r\n      // 点击了右边图标\r\n      clickRightIcon() {\r\n        this.$emit('clickRight')\r\n      },\r\n      // 一个周期滚动结束\r\n      end() {\r\n        this.$emit('end')\r\n      },\r\n      loadList() {\r\n        let tmpList = [];\r\n        for (let index in this.list) {\r\n          let v = this.list[index];\r\n          if (this.isObj(v)) {\r\n            //判断是否指定key显示，如果是则显示指定的列表，否则就返回所有列表\r\n            if (this.keyValue == undefined || v[this.keyName] == this.keyValue) {\r\n              tmpList.push(v[this.valueName]);\r\n            }\r\n          } else {\r\n            tmpList.push(v);\r\n          }\r\n        }\r\n        this.valueList = tmpList;\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  \r\n  .tn-notice-bar {\r\n    overflow: hidden;\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-notice-bar.vue?vue&type=style&index=0&id=b91dbbc0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-notice-bar.vue?vue&type=style&index=0&id=b91dbbc0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980404783\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}