@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mit_right.data-v-f574ee98 {
  width: 92rpx;
  height: 34rpx;
  border-radius: 17rpx;
  background: linear-gradient(124deg, #FF9C4C 30%, #FFB200 90%);
  font-size: 24rpx;
  color: #FFFFFF;
  text-align: center;
  padding-left: 10rpx;
  box-sizing: border-box;
  line-height: 34rpx;
}
.mit_left.data-v-f574ee98 {
  font-size: 30rpx;
  color: #FF000A;
}
.mit_left .mitl_word.data-v-f574ee98 {
  max-width: 430rpx;
}
.pro_model_foot.data-v-f574ee98 {
  margin-top: 30rpx;
  margin-bottom: 10rpx;
}
.pm_bottom.data-v-f574ee98 {
  margin-top: 32rpx;
  margin-bottom: 15rpx;
}
.pro_model_btn.data-v-f574ee98 {
  width: 316rpx;
  height: 96rpx;
  border-radius: 48rpx;
  box-sizing: border-box;
  border: 4rpx solid #7270FF;
  color: #7270FF;
  font-size: 32rpx;
  line-height: 96rpx;
}
.pro_model_btn2.data-v-f574ee98 {
  width: 316rpx;
  height: 96rpx;
  border-radius: 48rpx;
  box-sizing: border-box;
  background-color: #7270FF;
  border: 4rpx solid #7270FF;
  text-align: center;
  line-height: 92rpx;
  color: #FFFFFF;
  font-size: 32rpx;
}
.pmb_pro.data-v-f574ee98 {
  margin-bottom: 18rpx;
}
.pmt_left.data-v-f574ee98 {
  width: 410rpx;
}
.pmtl_title.data-v-f574ee98 {
  font-size: 32rpx;
}
.pmtl_sub.data-v-f574ee98 {
  margin-top: 16rpx;
}
.pro_model.data-v-f574ee98 {
  width: 650rpx;
  border-radius: 20rpx;
  background-color: #7270FF;
  padding: 30rpx;
  box-sizing: border-box;
  margin-top: 20rpx;
}
.model_inner_top.data-v-f574ee98 {
  width: 650rpx;
  height: 66rpx;
  border-radius: 10rpx;
  background-color: #FDEEE9;
  padding: 0rpx 20rpx;
  box-sizing: border-box;
}
.model_inner.data-v-f574ee98 {
  width: 690rpx;
  border-radius: 20rpx;
  background-color: #FFFFFF;
  margin-top: 36rpx;
  padding: 20rpx;
  box-sizing: border-box;
}

