<view class="{{['tn-column-notice-class','tn-column-notice','data-v-114031fc',backgroundColorClass]}}" style="{{$root.s0}}"><view class="tn-column-notice__icon data-v-114031fc"><block wx:if="{{leftIcon}}"><view data-event-opts="{{[['tap',[['clickLeftIcon',['$event']]]]]}}" class="{{['tn-column-notice__icon--left','data-v-114031fc','tn-icon-'+leftIconName,fontColorClass]}}" style="{{$root.s1}}" bindtap="__e"></view></block></view><swiper class="tn-column-notice__swiper data-v-114031fc" style="{{$root.s2}}" vertical="{{vertical}}" circular="{{true}}" autoplay="{{autoplay&&playStatus==='play'}}" interval="{{duration}}" data-event-opts="{{[['change',[['change',['$event']]]]]}}" bindchange="__e"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="tn-column-notice__swiper--item data-v-114031fc"><view data-event-opts="{{[['tap',[['click',[index]]]]]}}" class="{{['tn-column-notice__swiper--content','tn-text-ellipsis','data-v-114031fc',fontColorClass]}}" style="{{$root.s3}}" bindtap="__e">{{item}}</view></swiper-item></block></swiper><view class="tn-column-notice__icon data-v-114031fc"><block wx:if="{{rightIcon}}"><view data-event-opts="{{[['tap',[['clickRightIcon',['$event']]]]]}}" class="{{['tn-column-notice__icon--right','data-v-114031fc','tn-icon-'+rightIconName,fontColorClass]}}" style="{{$root.s4}}" bindtap="__e"></view></block><block wx:if="{{closeBtn}}"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="{{['tn-column-notice__icon--right','data-v-114031fc','tn-icon-close',fontColorClass]}}" style="{{$root.s5}}" bindtap="__e"></view></block></view></view>