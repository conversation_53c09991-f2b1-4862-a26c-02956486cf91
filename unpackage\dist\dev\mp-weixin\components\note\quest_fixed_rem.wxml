<view class="quest_foot_fixed tn-flex tn-flex-col-center tn-flex-row-between data-v-2a9cb960"><view class="qff_left tn-flex tn-flex-col-center tn-flex-row-center data-v-2a9cb960"><view data-event-opts="{{[['tap',[['showNote']]]]}}" class="tn-flex tn-flex-direction-column tn-flex-col-center data-v-2a9cb960" catchtap="__e"><image style="width:50rpx;" src="../../static/icon/notes.png" mode="widthFix" class="data-v-2a9cb960"></image><view style="color:#666666;font-size:22rpx;" class="data-v-2a9cb960">笔记</view></view></view><view class="qff_right tn-flex tn-flex-col-center tn-flex-row-between data-v-2a9cb960"><view data-event-opts="{{[['tap',[['last']]]]}}" class="last_btn data-v-2a9cb960" catchtap="__e">上一题</view><block wx:if="{{end==1}}"><view data-event-opts="{{[['tap',[['next']]]]}}" class="next_btn data-v-2a9cb960" catchtap="__e">下一题</view></block><block wx:if="{{end==2}}"><view data-event-opts="{{[['tap',[['submitEnd']]]]}}" class="next_btn data-v-2a9cb960" catchtap="__e">完成</view></block></view></view>