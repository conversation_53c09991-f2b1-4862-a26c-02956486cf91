<template>
	<view class="content">
		<view class="tn-text-bold tn-text-lg">
			解锁会员
		</view>
		<view class="input_warp tn-flex tn-flex-col-center">
			<tn-input v-model="code" placeholder="请输入验证码"></tn-input>
		</view>
		<view :class="isSub?'submit':'nosubmit'" @click.stop="submit()">
			立即解锁
		</view>
		<image src="../../static/vip_poster.png" mode="widthFix" style="width: 630rpx; margin-top: 300rpx;"></image>
		<image src="../../static/icon/zixun.png" mode="widthFix" class="fab_btn" @click.stop="showKefu=true"></image>
		<tn-popup v-model="showKefu" mode="bottom" :borderRadius="40" @close="close">
			<view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				客服帮助</view>
			<view class="tn-width-full" style="">
				<view class="tn-flex tn-flex-row-center"
					style="padding: 30rpx 46rpx 66rpx 46rpx;box-sizing: border-box;">
					<view style="width: 400rpx;height: 400rpx;">
						<image show-menu-by-longpress style="width: 400rpx;height: 400rpx;" :src="baseUrl+service_img"
							mode=""></image>
					</view>
				</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	import shareMixin from "@/mixins/shareMixin.js"
	export default {
		mixins: [shareMixin],
		data() {
			return {
				baseUrl: this.$config.baseUrl,
				code: "",
				isSub: true,
				service_img: "",
				showKefu: false
			}
		},
		onLoad() {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			this.getsysconfig()
		},

		methods: {
			close() {
				let userinfo = uni.getStorageSync('userinfo')
				if (userinfo.recite_status == 1) {
					uni.navigateBack()
				}
			},
			getsysconfig() { // 获取系统配置
				this.$http.post(this.$api.systemData, {}).then(res => {
					if (res.code == 200) {
						let datas = res.data;
						this.service_img = datas.config.service_img
						uni.setStorageSync('systemData', datas)
					}
				})
			},
			getUserInfo() {
				this.$http.post(this.$api.getUserInfo, {}).then(res => {
					if (res.code == 200) {
						uni.setStorageSync('userinfo', res.data)
						this.showKefu = true
					}
				})
			},
			submit() {
				let that = this
				if (!that.code) {
					uni.showToast({
						title: "请填写会员码",
						icon: "none"
					})
					return false
				}
				that.$http.post(that.$api.activeCode, {
					code: that.code
				}).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: "已解锁,请添加客服微信",
							icon: "none",
							success: () => {
								this.getUserInfo()
							}
						})
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.fab_btn {
		width: 154rpx;
		position: fixed;
		right: 0rpx;
		bottom: 150rpx;
	}

	.submit {
		width: 630rpx;
		height: 100rpx;
		border-radius: 50rpx;
		background-color: #3775F6;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #Ffffff;
		margin-top: 30rpx;
	}

	.nosubmit {
		width: 630rpx;
		height: 100rpx;
		border-radius: 50rpx;
		background-color: #9f9f9f;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #Ffffff;
		margin-top: 30rpx;
	}

	.input_warp {
		width: 630rpx;
		height: 114rpx;
		border-radius: 20rpx;
		background-color: #efeeee;
		margin-top: 24rpx;
		padding: 0rpx 40rpx;
		box-sizing: border-box;
	}

	page {
		background-color: #ffffff !important;
	}

	.content {
		width: 100%;
		padding: 60rpx;
		box-sizing: border-box;
	}
</style>