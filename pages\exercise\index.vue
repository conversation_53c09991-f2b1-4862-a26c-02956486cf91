<template>
	<view>
		<view class="top_model tn-width-full">
			<view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
				<view class="exercise_info tn-height-full tn-flex tn-flex-direction-column tn-flex-row-between">
					<view class="name tn-text-ellipsis-2" style="width: 380rpx;">
						{{name}}
					</view>
				</view>
				<view class="subsect">
					<tn-subsection :list="list" :height="72" inactiveColor="#5552FF" buttonColor="#5552FF"
						:current="current" @change="changeSub"></tn-subsection>
				</view>
			</view>
			<view class="tn-width-full" style="margin-top: 30rpx;">
				<scroll-view scroll-x="true" class="scroll-view-x">
					<view v-for="(item,index) in list_type" :key="index" class="scroll-view-item"
						:class="item.template_id==templateId?'sel_tem':''" @click.stop="changeTem(item)">
						{{item.template_name}}
					</view>
				</scroll-view>
			</view>
		</view>
		<view class="body">
			<view v-for="(item,index) in chapterList" :key="index" class="tn-width-full" style="margin-bottom: 20rpx;">
				<exercise-item :item="item" @toQuest="toQuest" @toEnd="toEnd"></exercise-item>
			</view>
		</view>
		<tn-popup v-model="showReason" mode="bottom" :borderRadius="40">
			<view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx 30rpx 0rpx 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				提示</view>
			<view class="tn-width-full" style="">
				<view class="tn-flex tn-flex-row-center tn-flex-direction-column tn-flex-col-center"
					style="padding: 30rpx 46rpx 66rpx 46rpx;box-sizing: border-box;">
					<view class="tn-width-full tn-text-left">
						{{reason}}
					</view>
					<view class="submit" @click.stop="toShare()">
						立即分享
					</view>
				</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	import exerciseItem from "@/components/exercise/exercise_item.vue"
	import shareMixin from "@/mixins/shareMixin.js"
	export default {
		mixins: [shareMixin],
		components: {
			exerciseItem
		},
		data() {
			return {
				list: ['刷题模式', '背诵模式'],
				bookId: null,
				templateId: null,
				name: null,
				list_type: [],
				chapterList: [],
				current: 0,
				userinfo: {},
				reason: "",
				showReason: false
			};
		},
		onLoad(options) {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			if (options.book_id) {
				this.bookId = options.book_id
				this.templateId = options.template_id || 0
				this.name = options.name
				this.getBookTem()
			}
			this.userinfo = uni.getStorageSync('userinfo')
		},

		onPullDownRefresh() {
			this.getBookTem()
		},
		onShow() {
			this.getCancelVipRea()
		},
		methods: {
			refreshData() {
				this.chapterList = []
				this.getBookTem()
				this.userinfo = uni.getStorageSync('userinfo')
			},
			toShare() {
				this.$publicjs.toUrl("/pages/mine/share")
			},
			getCancelVipRea() {
				this.$http.post(this.$api.cancelVipRea).then(res => {
					if (res.code == 200) {
						if (res.data) {
							let userinfo = uni.getStorageSync('userinfo')
							this.reason = res.data
							if (userinfo.exam_status == 0) {
								this.showReason = true
							} else {
								this.showReason = false
							}
						} else {
							this.showReason = false
						}
					}
				})
			},
			toEnd(item) {
				this.userinfo = uni.getStorageSync('userinfo')
				if (this.userinfo.exam_status == 0) {
					if (item.is_unlock == 1) {
						this.$publicjs.toUrl("/pages/mine/share")
					} else {
						this.$publicjs.toUrl("/pages/exercise/exercise_res?chapter_id=" + item.id)
					}
				} else {
					this.$publicjs.toUrl("/pages/exercise/exercise_res?chapter_id=" + item.id)
				}
			},
			changeSub(e) {
				this.current = e.index
			},
			toQuest(item) {
				this.userinfo = uni.getStorageSync('userinfo')
				if (this.userinfo.exam_status == 0) {
					if (item.is_unlock == 1) {
						this.$publicjs.toUrl("/pages/mine/share")
						return false
					} else {
						this.checkQuest(item)
					}
				} else {
					this.checkQuest(item)
				}
			},
			checkQuest(item) {
				this.$http.post(this.$api.questList, {
					chapter_id: item.id
				}).then(res => {
					if (res.code == 200) {
						if (res.data.length > 0) {
							if (this.current == 0) {
								uni.navigateTo({
									url: "/pages/exercise/quest?id=" + item.id + "&maxTitleId=" + item
										.max_title_id
								})
							} else {
								uni.navigateTo({
									url: "/pages/exercise/quest_rem?id=" + item.id + "&maxTitleId=" + item
										.max_title_id
								})
							}
						} else {
							uni.showToast({
								title: "暂无试题",
								icon: "none"
							})
						}
					}
				})


			},
			changeTem(item) {
				if (this.templateId != item.template_id) {
					this.templateId = item.template_id
					this.chapterList = []
					this.getChapterList()
				}
			},
			getBookTem() { //获取书籍模板
				this.$http.post(this.$api.bookTem, {
					book_id: this.bookId
				}).then(res => {
					uni.stopPullDownRefresh();
					if (res.code == 200) {
						this.list_type = res.data
						this.getChapterList()
					}
				})
			},
			getChapterList() {
				this.$http.post(this.$api.chapterList, {
					book_id: this.bookId,
					template_id: this.templateId
				}).then(res => {
					if (res.code == 200) {
						this.chapterList = res.data
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.submit {
		width: 630rpx;
		height: 100rpx;
		border-radius: 50rpx;
		background-color: #3775F6;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #Ffffff;
		margin-top: 70rpx;
	}

	.sel_tem {
		background-color: #E3E3FF !important;
		color: #5552FF !important;
	}

	.body {
		width: 100%;
		padding: 20rpx 30rpx;
		box-sizing: border-box;
	}

	.scroll-view-item {
		display: inline-block;
		min-width: 128rpx;
		height: 60rpx;
		border-radius: 30rpx;
		background-color: #F7F7F7;
		padding: 0rpx 20rpx;
		box-sizing: border-box;
		text-align: center;
		line-height: 60rpx;
		margin-right: 10rpx;
		color: #666666;
		font-size: 28rpx;
	}

	.scroll-view-x {
		white-space: nowrap;
		width: 100%;
	}

	.name {
		font-size: 30rpx;
		font-weight: bold;
		color: #222222;
	}

	.time {
		font-size: 20rpx;
		color: #666666;
	}

	/deep/ .tn-subsection__item--text {
		font-size: 24rpx !important;
	}

	.subsect {
		width: 272rpx;
	}

	.top_model {
		width: 750rpx;
		background-color: #FFFFFF;
		padding: 30rpx;
		box-sizing: border-box;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
	}
</style>