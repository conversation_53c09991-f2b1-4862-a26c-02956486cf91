<view class="tn-number-box-class tn-number-box data-v-61aeb8b2"><view data-event-opts="{{[['touchstart',[['touchStart',['minus']]]],['touchend',[['clearTimer',['$event']]]]]}}" class="{{['tn-number-box__btn__minus','data-v-61aeb8b2',backgroundColorClass,fontColorClass,[(disabled||inputValue<=min)?'tn-number-box__btn--disabled':'']]}}" style="{{'background-color:'+(backgroundColorStyle)+';'+('height:'+($root.g0)+';')+('color:'+(fontColorStyle)+';')+('font-size:'+(fontSizeStyle)+';')}}" catchtouchstart="__e" catchtouchend="__e"><view class="tn-icon-reduce data-v-61aeb8b2"></view></view><input class="{{['tn-number-box__input','data-v-61aeb8b2',fontColorClass,[(disabledInput||disabled)?'tn-number-box__input--disabled':'']]}}" style="{{'width:'+($root.g1)+';'+('height:'+($root.g2)+';')+('color:'+(fontColorStyle)+';')+('font-size:'+(fontSizeStyle)+';')+('background-color:'+(backgroundColorStyle)+';')}}" disabled="{{disabledInput||disabled}}" cursor-spacing="{{getCursorSpacing}}" data-event-opts="{{[['blur',[['blurInput',['$event']]]],['focus',[['focusInput',['$event']]]],['input',[['__set_model',['','inputValue','$event',[]]]]]]}}" value="{{inputValue}}" bindblur="__e" bindfocus="__e" bindinput="__e"/><view data-event-opts="{{[['touchstart',[['touchStart',['plus']]]],['touchend',[['clearTimer',['$event']]]]]}}" class="{{['tn-number-box__btn__plus','data-v-61aeb8b2',backgroundColorClass,fontColorClass,[(disabled||inputValue>=max)?'tn-number-box__btn--disabled':'']]}}" style="{{'background-color:'+(backgroundColorStyle)+';'+('height:'+($root.g3)+';')+('color:'+(fontColorStyle)+';')+('font-size:'+(fontSizeStyle)+';')}}" catchtouchstart="__e" catchtouchend="__e"><view class="tn-icon-add data-v-61aeb8b2"></view></view></view>