{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-form/tn-form.vue?cdce", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-form/tn-form.vue?7558", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-form/tn-form.vue?3233", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-form/tn-form.vue?2562", "uni-app:///tuniao-ui/components/tn-form/tn-form.vue"], "names": ["name", "props", "model", "type", "default", "errorType", "borderBottom", "labelPosition", "labelWidth", "labelAlign", "labelStyle", "provide", "tnForm", "data", "rules", "created", "methods", "setRules", "resetFields", "field", "validate", "valid", "errors", "resolve"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;;;AAGtD;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmnB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;gBCOvoB;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACA;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;QACA;MACA;IACA;EACA;EACA;EACAO;IACA;MACAC;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;UACA;UACAD;YACA;YACA;cACAE;cACAC;YACA;YACA;YACA;cACAC;cACA;cACA,8CACA,yCACAD;gBACA;cACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B", "file": "tuniao-ui/components/tn-form/tn-form.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-form.vue?vue&type=template&id=30431dec&\"\nvar renderjs\nimport script from \"./tn-form.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-form.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-form/tn-form.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-form.vue?vue&type=template&id=30431dec&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-form.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-form.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"tn-form-class tn-form\">\r\n    <slot></slot>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'tn-form',\r\n    props: {\r\n      // 表单数据对象（需要验证的表单数据）\r\n      model: {\r\n        type: Object,\r\n        default() {\r\n          return {}\r\n        }\r\n      },\r\n      // 发生错误时的提示方式\r\n      // toast - 弹出toast框\r\n      // message - 提示信息\r\n      // border - 如果设置了边框，边框会变成红色\r\n      // border-bottom - 下边框会呈现红色\r\n      // none - 无提示\r\n      errorType: {\r\n        type: Array,\r\n        default() {\r\n          return ['message', 'toast']\r\n        }\r\n      },\r\n      // 是否显示表单域的下划线边框\r\n      borderBottom: {\r\n        type:Boolean,\r\n        default: true\r\n      },\r\n      // label(标签名称)的位置\r\n      // left - 左边\r\n      // top - 上边\r\n      labelPosition: {\r\n        type: String,\r\n        default: 'left'\r\n      },\r\n      // label的宽度\r\n      labelWidth: {\r\n        type: Number,\r\n        default: 90\r\n      },\r\n      // label的对齐方式\r\n      // left - 左对齐\r\n      // center - 居中对齐\r\n      // right - 右对齐\r\n      labelAlign: {\r\n        type: String,\r\n        default: 'left'\r\n      },\r\n      // label 的样式\r\n      labelStyle: {\r\n        type: Object,\r\n        default() {\r\n          return {}\r\n        }\r\n      }\r\n    },\r\n    // 向子孙传递数据\r\n    provide() {\r\n      return {\r\n        tnForm: this\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        rules: {}\r\n      }\r\n    },\r\n    created() {\r\n      // 存储当前form下的所有form-item的实例\r\n      // 不能定义再data中，否则小程序会循环引用而报错\r\n      this.fields = []\r\n    },\r\n    methods: {\r\n      /**\r\n       * 设置规则\r\n       * \r\n       * @param {Object} rules\r\n       */\r\n      setRules(rules) {\r\n        this.rules = rules\r\n      },\r\n      /**\r\n       * 清空form-item组件\r\n       */\r\n      resetFields() {\r\n        this.fields.map(field => {\r\n          field.resetField()\r\n        })\r\n      },\r\n      /**\r\n       * 校验数据\r\n       * @param {Object} callback 校验回调方法\r\n       */\r\n      validate(callback) {\r\n        return new Promise(resolve => {\r\n          // 标记校验是否通过\r\n          let valid = true\r\n          // 标记是否检查完毕\r\n          let count = 0\r\n          // 存放错误信息\r\n          let errors = []\r\n          \r\n          // 对所有form-item进行校验\r\n          this.fields.map(field => {\r\n            // 调用对应form-item实例的validation校验方法\r\n            field.validation('', error => {\r\n              // 如果有一个form-item校验不通过，则整个表单校验不通过\r\n              if (error) {\r\n                valid = false\r\n                errors.push(error)\r\n              }\r\n              // 当遍历完所有的form-item的校验规则，返回信息\r\n              if (++count === this.fields.length) {\r\n                resolve(valid)\r\n                // 判断是否设置了toast的提示方式，只提示表单域中最前面的一条错误信息\r\n                if (this.errorType.indexOf('none') === -1 && \r\n                    this.errorType.indexOf('toast') >= 0 &&\r\n                    errors.length > 0) {\r\n                  this.$tn.message.toast(errors[0])\r\n                }\r\n                // 调用回调方法\r\n                if (typeof callback == 'function') callback(valid)\r\n              }\r\n            })\r\n          })\r\n        })\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style>\r\n</style>\r\n"], "sourceRoot": ""}