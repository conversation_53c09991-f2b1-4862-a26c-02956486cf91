{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/project/shuati_new/pages/mine/code.vue?2468", "webpack:///D:/project/shuati_new/pages/mine/code.vue?2ec5", "webpack:///D:/project/shuati_new/pages/mine/code.vue?f9ae", "webpack:///D:/project/shuati_new/pages/mine/code.vue?c69c", "uni-app:///pages/mine/code.vue", "webpack:///D:/project/shuati_new/pages/mine/code.vue?42a5", "webpack:///D:/project/shuati_new/pages/mine/code.vue?e1f7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "data", "baseUrl", "code", "isSub", "service_img", "showKefu", "onLoad", "withShareTicket", "menus", "methods", "close", "uni", "getsysconfig", "getUserInfo", "submit", "title", "icon", "that", "success"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAAimB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+BrnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEAZ;MAAA;MACAa;MACAC;IACA;IAEA;EACA;EAEAC;IACAC;MACA;MACA;QACAC;MACA;IACA;IACAC;MAAA;MAAA;MACA;QACA;UACA;UACA;UACAD;QACA;MACA;IACA;IACAE;MAAA;MACA;QACA;UACAF;UACA;QACA;MACA;IACA;IACAG;MAAA;MACA;MACA;QACAH;UACAI;UACAC;QACA;QACA;MACA;MACAC;QACAf;MACA;QACA;UACAS;YACAI;YACAC;YACAE;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAAA;AAAoqC,CAAgB,qnCAAG,EAAC,C;;;;;;;;;;;ACAxrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/code.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/code.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./code.vue?vue&type=template&id=a1aaa500&scoped=true&\"\nvar renderjs\nimport script from \"./code.vue?vue&type=script&lang=js&\"\nexport * from \"./code.vue?vue&type=script&lang=js&\"\nimport style0 from \"./code.vue?vue&type=style&index=0&id=a1aaa500&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a1aaa500\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/code.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./code.vue?vue&type=template&id=a1aaa500&scoped=true&\"", "var components\ntry {\n  components = {\n    tnInput: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-input/tn-input\" */ \"@/tuniao-ui/components/tn-input/tn-input.vue\"\n      )\n    },\n    tnPopup: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-popup/tn-popup\" */ \"@/tuniao-ui/components/tn-popup/tn-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.showKefu = true\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./code.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./code.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"tn-text-bold tn-text-lg\">\r\n\t\t\t解锁会员\r\n\t\t</view>\r\n\t\t<view class=\"input_warp tn-flex tn-flex-col-center\">\r\n\t\t\t<tn-input v-model=\"code\" placeholder=\"请输入验证码\"></tn-input>\r\n\t\t</view>\r\n\t\t<view :class=\"isSub?'submit':'nosubmit'\" @click.stop=\"submit()\">\r\n\t\t\t立即解锁\r\n\t\t</view>\r\n\t\t<image src=\"../../static/vip_poster.png\" mode=\"widthFix\" style=\"width: 630rpx; margin-top: 300rpx;\"></image>\r\n\t\t<image src=\"../../static/icon/zixun.png\" mode=\"widthFix\" class=\"fab_btn\" @click.stop=\"showKefu=true\"></image>\r\n\t\t<tn-popup v-model=\"showKefu\" mode=\"bottom\" :borderRadius=\"40\" @close=\"close\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t客服帮助</view>\r\n\t\t\t<view class=\"tn-width-full\" style=\"\">\r\n\t\t\t\t<view class=\"tn-flex tn-flex-row-center\"\r\n\t\t\t\t\tstyle=\"padding: 30rpx 46rpx 66rpx 46rpx;box-sizing: border-box;\">\r\n\t\t\t\t\t<view style=\"width: 400rpx;height: 400rpx;\">\r\n\t\t\t\t\t\t<image show-menu-by-longpress style=\"width: 400rpx;height: 400rpx;\" :src=\"baseUrl+service_img\"\r\n\t\t\t\t\t\t\tmode=\"\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport shareMixin from \"@/mixins/shareMixin.js\"\r\n\texport default {\r\n\t\tmixins: [shareMixin],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbaseUrl: this.$config.baseUrl,\r\n\t\t\t\tcode: \"\",\r\n\t\t\t\tisSub: true,\r\n\t\t\t\tservice_img: \"\",\r\n\t\t\t\tshowKefu: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\tthis.getsysconfig()\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\tclose() {\r\n\t\t\t\tlet userinfo = uni.getStorageSync('userinfo')\r\n\t\t\t\tif (userinfo.recite_status == 1) {\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetsysconfig() { // 获取系统配置\r\n\t\t\t\tthis.$http.post(this.$api.systemData, {}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tlet datas = res.data;\r\n\t\t\t\t\t\tthis.service_img = datas.config.service_img\r\n\t\t\t\t\t\tuni.setStorageSync('systemData', datas)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetUserInfo() {\r\n\t\t\t\tthis.$http.post(this.$api.getUserInfo, {}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tuni.setStorageSync('userinfo', res.data)\r\n\t\t\t\t\t\tthis.showKefu = true\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tsubmit() {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tif (!that.code) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"请填写会员码\",\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\tthat.$http.post(that.$api.activeCode, {\r\n\t\t\t\t\tcode: that.code\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"已解锁,请添加客服微信\",\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tthis.getUserInfo()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.fab_btn {\r\n\t\twidth: 154rpx;\r\n\t\tposition: fixed;\r\n\t\tright: 0rpx;\r\n\t\tbottom: 150rpx;\r\n\t}\r\n\r\n\t.submit {\r\n\t\twidth: 630rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbackground-color: #3775F6;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #Ffffff;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.nosubmit {\r\n\t\twidth: 630rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbackground-color: #9f9f9f;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #Ffffff;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.input_warp {\r\n\t\twidth: 630rpx;\r\n\t\theight: 114rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #efeeee;\r\n\t\tmargin-top: 24rpx;\r\n\t\tpadding: 0rpx 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\tpage {\r\n\t\tbackground-color: #ffffff !important;\r\n\t}\r\n\r\n\t.content {\r\n\t\twidth: 100%;\r\n\t\tpadding: 60rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./code.vue?vue&type=style&index=0&id=a1aaa500&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./code.vue?vue&type=style&index=0&id=a1aaa500&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980402489\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}