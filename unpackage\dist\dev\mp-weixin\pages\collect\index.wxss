@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.sel_tem.data-v-4f224584 {
  background-color: #E3E3FF !important;
  color: #5552FF !important;
}
.body.data-v-4f224584 {
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}
.scroll-view-item.data-v-4f224584 {
  display: inline-block;
  min-width: 128rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #F7F7F7;
  padding: 0rpx 20rpx;
  box-sizing: border-box;
  text-align: center;
  line-height: 60rpx;
  margin-right: 10rpx;
  color: #666666;
  font-size: 28rpx;
}
.scroll-view-x.data-v-4f224584 {
  white-space: nowrap;
  width: 100%;
}
.name.data-v-4f224584 {
  font-size: 30rpx;
  font-weight: bold;
  color: #222222;
}
.time.data-v-4f224584 {
  font-size: 20rpx;
  color: #666666;
}
.data-v-4f224584 .tn-subsection__item--text {
  font-size: 24rpx !important;
}
.subsect.data-v-4f224584 {
  width: 272rpx;
}
.top_model.data-v-4f224584 {
  width: 750rpx;
  background-color: #FFFFFF;
  padding: 30rpx;
  box-sizing: border-box;
  border-radius: 0rpx 0rpx 20rpx 20rpx;
}

