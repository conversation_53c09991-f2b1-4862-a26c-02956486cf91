{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/project/shuati_new/pages/collect/index.vue?f68b", "webpack:///D:/project/shuati_new/pages/collect/index.vue?2ee8", "webpack:///D:/project/shuati_new/pages/collect/index.vue?1013", "uni-app:///pages/collect/index.vue", "webpack:///D:/project/shuati_new/pages/collect/index.vue?a227", "webpack:///D:/project/shuati_new/pages/collect/index.vue?9c18"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "components", "collectItem", "data", "list", "bookId", "templateId", "name", "list_type", "collectList", "current", "onLoad", "withShareTicket", "menus", "methods", "changeSub", "toQuest", "uni", "url", "changeTem", "getBookTem", "book_id", "getCollectList", "template_id"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACiCtnB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEAhB;MAAA;MACAiB;MACAC;IACA;IAEA;MACA;MACA;MACA;IACA;EACA;EAEAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAC;QACAC;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;MACA;QACAC;MACA;QACA;UACA;UACA;YACA;UACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACAD;QACAE;MACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9GA;AAAA;AAAA;AAAA;AAAqqC,CAAgB,snCAAG,EAAC,C;;;;;;;;;;;ACAzrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/collect/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/collect/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4f224584&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4f224584&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4f224584\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/collect/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4f224584&scoped=true&\"", "var components\ntry {\n  components = {\n    tnSubsection: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-subsection/tn-subsection\" */ \"@/tuniao-ui/components/tn-subsection/tn-subsection.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"top_model tn-width-full\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-row-between tn-flex-col-center\">\r\n\t\t\t\t<view class=\"exercise_info tn-height-full tn-flex tn-flex-direction-column tn-flex-row-between\">\r\n\t\t\t\t\t<view class=\"name tn-text-ellipsis-2\" style=\"width: 380rpx;\">\r\n\t\t\t\t\t\t{{name}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"subsect\">\r\n\t\t\t\t\t<tn-subsection :list=\"list\" :height=\"72\" inactiveColor=\"#5552FF\" buttonColor=\"#5552FF\"\r\n\t\t\t\t\t\t:current=\"current\" @change=\"changeSub\"></tn-subsection>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-width-full\" style=\"margin-top: 30rpx;\">\r\n\t\t\t\t<scroll-view scroll-x=\"true\" class=\"scroll-view-x\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in list_type\" :key=\"index\" class=\"scroll-view-item\"\r\n\t\t\t\t\t\t:class=\"item.template_id==templateId?'sel_tem':''\" @click.stop=\"changeTem(item)\">\r\n\t\t\t\t\t\t{{item.template_name}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"body\">\r\n\t\t\t<view v-for=\"(item,index) in collectList\" :key=\"index\" class=\"tn-width-full\" style=\"margin-bottom: 20rpx;\">\r\n\t\t\t\t<collect-item :item=\"item\" @toQuest=\"toQuest\"></collect-item>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport collectItem from \"@/components/collect_item.vue\"\r\n\timport shareMixin from \"@/mixins/shareMixin.js\"\r\n\texport default {\r\n\t\tmixins: [shareMixin],\r\n\t\tcomponents: {\r\n\t\t\tcollectItem\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tlist: ['刷题模式', '背诵模式'],\r\n\t\t\t\tbookId: null,\r\n\t\t\t\ttemplateId: 0,\r\n\t\t\t\tname: null,\r\n\t\t\t\tlist_type: [],\r\n\t\t\t\tcollectList: [],\r\n\t\t\t\tcurrent: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\tif (options.book_id) {\r\n\t\t\t\tthis.bookId = options.book_id\r\n\t\t\t\tthis.name = options.name\r\n\t\t\t\tthis.getBookTem()\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\tchangeSub(e) {\r\n\t\t\t\tthis.current = e.index\r\n\t\t\t},\r\n\t\t\ttoQuest(item) {\r\n\t\t\t\t// if (this.current == 0) {\r\n\t\t\t\t// \tuni.navigateTo({\r\n\t\t\t\t// \t\turl: \"/pages/collect/quest?id=\" + item.chapter_id\r\n\t\t\t\t// \t})\r\n\t\t\t\t// } else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: \"/pages/collect/quest_rem?id=\" + item.chapter_id\r\n\t\t\t\t\t})\r\n\t\t\t\t// }\r\n\t\t\t},\r\n\t\t\tchangeTem(item) {\r\n\t\t\t\tif (this.templateId != item.template_id) {\r\n\t\t\t\t\tthis.templateId = item.template_id\r\n\t\t\t\t\tthis.collectList = []\r\n\t\t\t\t\tthis.getCollectList()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetBookTem() { //获取书籍模板\r\n\t\t\t\tthis.$http.post(this.$api.bookTem, {\r\n\t\t\t\t\tbook_id: this.bookId\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.list_type = res.data\r\n\t\t\t\t\t\tif (res.data.length > 0) {\r\n\t\t\t\t\t\t\tthis.templateId = res.data[0].template_id\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.getCollectList()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetCollectList() {\r\n\t\t\t\tthis.$http.post(this.$api.bookCollect, {\r\n\t\t\t\t\tbook_id: this.bookId,\r\n\t\t\t\t\ttemplate_id: this.templateId\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.collectList = res.data\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.sel_tem {\r\n\t\tbackground-color: #E3E3FF !important;\r\n\t\tcolor: #5552FF !important;\r\n\t}\r\n\r\n\t.body {\r\n\t\twidth: 100%;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.scroll-view-item {\r\n\t\tdisplay: inline-block;\r\n\t\tmin-width: 128rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tbackground-color: #F7F7F7;\r\n\t\tpadding: 0rpx 20rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\ttext-align: center;\r\n\t\tline-height: 60rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.scroll-view-x {\r\n\t\twhite-space: nowrap;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.name {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #222222;\r\n\t}\r\n\r\n\t.time {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t/deep/ .tn-subsection__item--text {\r\n\t\tfont-size: 24rpx !important;\r\n\t}\r\n\r\n\t.subsect {\r\n\t\twidth: 272rpx;\r\n\t}\r\n\r\n\t.top_model {\r\n\t\twidth: 750rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-radius: 0rpx 0rpx 20rpx 20rpx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4f224584&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4f224584&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753981198429\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}