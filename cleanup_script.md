# 项目空间清理指南

## 🚀 立即可删除的文件（安全）

### 1. 编译输出目录
```bash
# 删除编译输出目录（可重新生成）
rm -rf unpackage/
```
**预计节省：大量空间**

### 2. 测试文件
```bash
# 删除测试页面（如果不需要）
rm -rf pages/test/
```
**同时需要从 pages.json 中移除测试页面配置**

### 3. 可能不需要的工具文件
```bash
# 如果所有页面都使用shareMixin，可以删除快速修复文件
rm utils/shareQuickFix.js
```

## ⚠️ 需要确认后删除的文件

### 1. uni_modules模块检查

#### ✅ 正在使用的模块（请勿删除）
- **qiun-data-charts** - 图表组件（在chart_line.vue、chart_radar.vue、chat_pro.vue等中使用）
- **uni-table** - 表格组件（在pages/exercise/check_index.vue中使用）
- **uni-datetime-picker** - 日期选择器（被uni-table依赖）
- **uni-scss** - 样式库（被其他组件依赖）
- **uni-icons** - 图标库（可能在项目中使用）
- **mp-html** - 富文本组件（在components/quest_item.vue中使用）

#### ❌ 暂无可删除的uni_modules模块
所有模块都在使用中，建议保留。

### 2. 图片资源检查
检查 static 目录中是否有未使用的图片：
- `static/empty.png` 和 `static/empty01.png` - 检查是否都在使用
- 各种 icon 文件 - 确认每个都有对应的使用场景

## 🔍 代码优化建议

### 1. 清理注释代码
在 `config/index.js` 中：
```javascript
// 删除这些注释行
// baseUrl:"https://exam.hn8090kj.com",
// baseUrl: "https://exam.hn8090kj.com", // 后台接口请求地址
```

### 2. 合并重复样式
检查各页面中是否有重复的CSS样式，可以提取到公共样式文件中。

### 3. 组件优化
检查 components 目录中是否有未使用的组件：
- `chat_pro.vue` 和 `chat_pro_small.vue` - 确认是否都在使用
- 其他自定义组件的使用情况

## 📋 检查清单

### 使用前请确认：
- [ ] 项目是否使用了表格功能（uni-table）
- [ ] 项目是否使用了日期选择器（uni-datetime-picker）
- [ ] 项目是否使用了富文本显示（mp-html）
- [ ] 是否还需要测试页面
- [ ] 是否所有页面都已使用shareMixin（可删除shareQuickFix.js）
- [ ] 备份重要文件

### 安全删除步骤：
1. **先备份整个项目**
2. 删除 unpackage 目录
3. 检查并删除未使用的 uni_modules
4. 删除测试文件
5. 清理注释代码
6. 重新编译测试

## 🎯 预期效果

完成清理后，预计可以节省：
- **unpackage目录**：50-200MB（取决于项目大小）
- **未使用的uni_modules**：每个模块5-20MB
- **测试文件**：1-5MB
- **总计**：可能节省100-300MB空间

## ⚡ 快速清理命令

```bash
# 在项目根目录执行
# 1. 删除编译输出（安全，可重新生成）
rm -rf unpackage/

# 2. 删除测试文件（如果确认不需要）
# rm -rf pages/test/
# rm utils/shareQuickFix.js

# 3. 重新编译
# npm run dev:mp-weixin
```

## 📊 清理结果总结

### ✅ 可以安全删除的文件
1. **unpackage/** - 编译输出目录（50-200MB）
2. **utils/shareQuickFix.js** - 如果所有页面都使用了shareMixin（1KB）
3. **pages/test/** - 测试页面（如果不需要）（1-5MB）

### ❌ 不建议删除的文件
1. **所有uni_modules模块** - 都在使用中
2. **tuniao-ui** - UI框架，项目依赖
3. **components目录** - 所有组件都在使用
4. **static目录** - 图片资源都在使用

### 🎯 预期节省空间
- **最大可节省**：50-200MB（主要来自unpackage目录）
- **实际建议删除**：unpackage目录即可

**注意：执行前请务必备份项目！**
