{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-circle-progress/tn-circle-progress.vue?770f", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-circle-progress/tn-circle-progress.vue?6be1", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-circle-progress/tn-circle-progress.vue?c1cc", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-circle-progress/tn-circle-progress.vue?c3fb", "uni-app:///tuniao-ui/components/tn-circle-progress/tn-circle-progress.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-circle-progress/tn-circle-progress.vue?2114", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-circle-progress/tn-circle-progress.vue?c8ca"], "names": ["name", "props", "percent", "type", "default", "validator", "borderWidth", "width", "striped", "stripedActive", "activeColor", "inactiveColor", "showPercent", "duration", "data", "elBgId", "elId", "progressContext", "widthPx", "borderWidthPx", "startAngle", "newPercent", "oldPercent", "watch", "setTimeout", "created", "mounted", "methods", "drawProgressBg", "ctx", "drawCircleByProgress", "progress"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2I;AAC3I;AACsE;AACL;AACsC;;;AAGvG;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,wFAAM;AACR,EAAE,yGAAM;AACR,EAAE,kHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8nB,CAAgB,4nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCqClpB;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;EACA;EACAU;IACA;MACA;;MAEAC;MACAC;MAMA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACArB;MAAA;MAAA;MACA;MACA;MAEA;MACA;MACAsB;QACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IAAA;IACAF;MACA;MACA;IACA;EACA;EACAG;IACA;IACAC;MACA;MACA;MACAC;MACA;MACAA;MACAA;MACA;MACAA;MACAA;MACAA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACAD;QACA;MACA;MACA;MACA;MACAA;MACAA;MACA;MACA;MACA;MACA;MACA;MACAA;MACAA;MACAA;MACAA;;MAEA;MACA;QACA;QACAE;QACA;QACA;MACA;QACAA;QACA;MACA;MACAP;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9LA;AAAA;AAAA;AAAA;AAA6sC,CAAgB,moCAAG,EAAC,C;;;;;;;;;;;ACAjuC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-circle-progress/tn-circle-progress.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-circle-progress.vue?vue&type=template&id=5206e2dc&scoped=true&\"\nvar renderjs\nimport script from \"./tn-circle-progress.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-circle-progress.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-circle-progress.vue?vue&type=style&index=0&id=5206e2dc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5206e2dc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-circle-progress/tn-circle-progress.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-circle-progress.vue?vue&type=template&id=5206e2dc&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-circle-progress.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-circle-progress.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view\r\n    class=\"tn-circle-progress-class tn-circle-progress\"\r\n    :style=\"{\r\n      width: widthPx + 'px',\r\n      height: widthPx + 'px'\r\n    }\"\r\n  >\r\n    <!-- 支付宝小程序不支持canvas-id属性，必须用id属性 -->\r\n    <!-- 默认圆环 -->\r\n    <canvas\r\n      class=\"tn-circle-progress__canvas-bg\"\r\n      :canvas-id=\"elBgId\"\r\n      :id=\"elBgId\"\r\n      :style=\"{\r\n        width: widthPx + 'px',\r\n        height: widthPx + 'px',\r\n      }\"\r\n    ></canvas>\r\n    <!-- 进度圆环 -->\r\n    <canvas\r\n      class=\"tn-circle-progress__canvas\"\r\n      :canvas-id=\"elId\"\r\n      :id=\"elId\"\r\n      :style=\"{\r\n        width: widthPx + 'px',\r\n        height: widthPx + 'px',\r\n      }\"\r\n    ></canvas>\r\n    <view class=\"tn-circle-progress__content\">\r\n      <slot v-if=\"$slots.default || $slots.$default\"></slot>\r\n      <view v-else-if=\"showPercent\" class=\"tn-circle-progress__content__percent\">{{ percent + '%' }}</view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    name: 'tn-circle-progress',\r\n    props: {\r\n      // 进度（百分比）\r\n      percent: {\r\n        type: Number,\r\n        default: 0,\r\n        validator: val => {\r\n          return val >= 0 && val <= 100\r\n        }\r\n      },\r\n      // 圆环线宽\r\n      borderWidth: {\r\n        type: Number,\r\n        default: 14\r\n      },\r\n      // 整体圆的宽度\r\n      width: {\r\n        type: Number,\r\n        default: 200\r\n      },\r\n      // 是否显示条纹\r\n      striped: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 条纹是否运动\r\n      stripedActive: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 激活部分颜色\r\n      activeColor: {\r\n        type: String,\r\n        default: '#01BEFF'\r\n      },\r\n      // 非激活部分颜色\r\n      inactiveColor: {\r\n        type: String,\r\n        default: '#f0f0f0'\r\n      },\r\n      // 是否显示进度条内部百分比值\r\n      showPercent: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 圆环执行动画的时间，ms\r\n      duration: {\r\n        type: Number,\r\n        default: 1500\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        // 微信小程序中不能使用this.$tn.uuid()形式动态生成id值，否则会报错\r\n        // #ifdef MP-WEIXIN\r\n        elBgId: 'tCircleProgressBgId',\r\n        elId: 'tCircleProgressElId',\r\n        // #endif\r\n        // #ifndef MP-WEIXIN\r\n        elBgId: this.$tn.uuid(),\r\n        elId: this.$tn.uuid(),\r\n        // #endif\r\n        // 活动圆上下文\r\n        progressContext: null,\r\n        // 转换成px为单位的背景宽度\r\n        widthPx: uni.upx2px(this.width || 200),\r\n        // 转换成px为单位的圆环宽度\r\n        borderWidthPx: uni.upx2px(this.borderWidth || 14),\r\n        // canvas画圆的起始角度，默认为-90度，顺时针\r\n        startAngle: -90 * Math.PI / 180,\r\n        // 动态修改进度值的时候，保存进度值的变化前后值\r\n        newPercent: 0,\r\n        oldPercent: 0\r\n      }\r\n    },\r\n    watch: {\r\n      percent(newVal, oldVal = 0) {\r\n        if (newVal > 100) newVal = 100\r\n        if (oldVal < 0) oldVal = 0\r\n        \r\n        this.newPercent = newVal\r\n        this.oldPercent = oldVal\r\n        setTimeout(() => {\r\n        \t// 无论是百分比值增加还是减少，需要操作还是原来的旧的百分比值\r\n        \t// 将此值减少或者新增到新的百分比值\r\n        \tthis.drawCircleByProgress(oldVal)\r\n        }, 50)\r\n      }\r\n    },\r\n    created() {\r\n      // 赋值，用于加载后第一个画圆使用\r\n      this.newPercent = this.percent;\r\n      this.oldPercent = 0;\r\n    },\r\n    mounted() {\r\n      setTimeout(() => {\r\n      \tthis.drawProgressBg()\r\n      \tthis.drawCircleByProgress(this.oldPercent)\r\n      }, 50)\r\n    },\r\n    methods: {\r\n      // 绘制进度条背景\r\n      drawProgressBg() {\r\n        let ctx = uni.createCanvasContext(this.elBgId, this)\r\n        // 设置线宽\r\n        ctx.setLineWidth(this.borderWidthPx+2)\r\n        // 设置颜色\r\n        ctx.setStrokeStyle(this.inactiveColor)\r\n        ctx.beginPath()\r\n        let radius = this.widthPx / 2\r\n        ctx.arc(radius, radius, radius - this.borderWidthPx, 0, 360 * Math.PI / 180, false)\r\n        ctx.stroke()\r\n        ctx.draw()\r\n      },\r\n      // 绘制圆弧的进度\r\n      drawCircleByProgress(progress) {\r\n        // 如果已经存在则拿来使用\r\n        let ctx = this.progressContext\r\n        if (!ctx) {\r\n          ctx =uni.createCanvasContext(this.elId, this)\r\n          this.progressContext = ctx\r\n        }\r\n        // ctx.setLineCap('round')\r\n        // 设置线条宽度和颜色\r\n        ctx.setLineWidth(this.borderWidthPx)\r\n        ctx.setStrokeStyle(this.activeColor)\r\n        // 将总过渡时间除以100，得出每修改百分之一进度所需的时间\r\n        let preSecondTime = Math.floor(this.duration / 100)\r\n        // 结束角的计算依据为：将2π分为100份，乘以当前的进度值，得出终止点的弧度值，加起始角，为整个圆从默认的\r\n        let endAngle = ((360 * Math.PI / 180) / 100) * progress + this.startAngle\r\n        let radius = this.widthPx / 2\r\n        ctx.beginPath()\r\n        ctx.arc(radius, radius, radius - this.borderWidthPx, this.startAngle, endAngle, false)\r\n        ctx.stroke()\r\n        ctx.draw()\r\n        \r\n        // 如果变更后新值大于旧值，意味着增大了百分比\r\n        if (this.newPercent > this.oldPercent) {\r\n          // 每次递增百分之一\r\n          progress++\r\n          // 如果新增后的值，大于需要设置的值百分比值，停止继续增加\r\n          if (progress > this.newPercent) return\r\n        } else {\r\n          progress--\r\n          if (progress < this.newPercent) return\r\n        }\r\n        setTimeout(() => {\r\n          // 定时器，每次操作间隔为time值，为了让进度条有动画效果\r\n          this.drawCircleByProgress(progress)\r\n        }, preSecondTime)\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  \r\n  .tn-circle-progress {\r\n    position: relative;\r\n    /* #ifndef APP-NVUE */\r\n    display: inline-flex;\t\t\r\n    /* #endif */\r\n    align-items: center;\r\n    justify-content: center;\r\n    background-color: transparent;\r\n    \r\n    &__canvas {\r\n      position: absolute;\r\n      \r\n      &-bg {\r\n        position: absolute;\r\n      }\r\n    }\r\n    \r\n    &__content {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      \r\n      &__percent {\r\n        font-size: 28rpx;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-circle-progress.vue?vue&type=style&index=0&id=5206e2dc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-circle-progress.vue?vue&type=style&index=0&id=5206e2dc&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980404978\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}