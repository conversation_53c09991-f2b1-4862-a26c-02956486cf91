@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.tn-line-progress.data-v-77f7b248 {
  display: inline-flex;
  align-items: center;
  width: 100%;
  height: 28rpx;
  overflow: hidden;
  border-radius: 100rpx;
  background-color: #f0f0f0;
}
.tn-line-progress--active.data-v-77f7b248 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-items: flex-end;
  justify-content: space-around;
  width: 0;
  height: 100%;
  font-size: 20rpx;
  color: #FFFFFF;
  background-color: #01BEFF;
  transition: all 0.3s ease;
}
.tn-line-progress__striped.data-v-77f7b248 {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 80rpx 80rpx;
}
.tn-line-progress__striped--active.data-v-77f7b248 {
  -webkit-animation: progress-striped-data-v-77f7b248 2s linear infinite;
          animation: progress-striped-data-v-77f7b248 2s linear infinite;
}
@-webkit-keyframes progress-striped-data-v-77f7b248 {
0% {
    background-position: 0 0;
}
100% {
    background-position: 80rpx 0;
}
}
@keyframes progress-striped-data-v-77f7b248 {
0% {
    background-position: 0 0;
}
100% {
    background-position: 80rpx 0;
}
}

