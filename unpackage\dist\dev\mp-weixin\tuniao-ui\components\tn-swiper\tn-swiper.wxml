<view class="tn-swiper__wrap-class tn-swiper__wrap data-v-4ba19b58" style="{{'border-radius:'+(radius+'rpx')+';'}}"><swiper class="{{['tn-swiper','data-v-4ba19b58',backgroundColorClass]}}" style="{{$root.s0}}" current="{{current}}" interval="{{interval}}" circular="{{circular}}" autoplay="{{autoplay}}" duration="{{duration}}" previous-margin="{{effect3d?effect3dPreviousSpacing+'rpx':'0'}}" next-margin="{{effect3d?effect3dPreviousSpacing+'rpx':'0'}}" data-event-opts="{{[['change',[['change',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="tn-swiper__item data-v-4ba19b58"><view data-event-opts="{{[['tap',[['click',[index]]]]]}}" class="{{['tn-swiper__item__image__wrap','data-v-4ba19b58',swiperIndex!==index?'tn-swiper__item__image--scale':'']}}" style="{{'border-radius:'+(radius+'rpx')+';'+('transform:'+(effect3d&&swiperIndex!==index?'scaleY(0.9)':'scaleY(1)')+';')+('margin:'+(effect3d&&swiperIndex!==index?'0 20rpx':0)+';')}}" bindtap="__e"><image class="tn-swiper__item__image data-v-4ba19b58" src="{{item.$orig[name]||item.$orig}}" mode="{{imageMode}}"></image><block wx:if="{{title&&item.$orig[titleName]}}"><view class="tn-swiper__item__title tn-text-ellipsis data-v-4ba19b58" style="{{item.s1}}">{{''+item.$orig[titleName]+''}}</view></block></view></swiper-item></block></swiper><view class="tn-swiper__indicator data-v-4ba19b58" style="{{$root.s2}}"><block wx:if="{{mode==='rect'}}"><block class="data-v-4ba19b58"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['tn-swiper__indicator__rect','data-v-4ba19b58',(swiperIndex===index)?'tn-swiper__indicator__rect--active':'']}}"></view></block></block></block><block wx:if="{{mode==='dot'}}"><block class="data-v-4ba19b58"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['tn-swiper__indicator__dot','data-v-4ba19b58',(swiperIndex===index)?'tn-swiper__indicator__dot--active':'']}}"></view></block></block></block><block wx:if="{{mode==='round'}}"><block class="data-v-4ba19b58"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['tn-swiper__indicator__round','data-v-4ba19b58',(swiperIndex===index)?'tn-swiper__indicator__round--active':'']}}"></view></block></block></block><block wx:if="{{mode==='number'}}"><block class="data-v-4ba19b58"><view class="tn-swiper__indicator__number data-v-4ba19b58">{{swiperIndex+1+"/"+$root.g0}}</view></block></block></view></view>