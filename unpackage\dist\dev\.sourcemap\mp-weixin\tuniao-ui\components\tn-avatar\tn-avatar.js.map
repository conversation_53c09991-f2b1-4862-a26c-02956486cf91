{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-avatar/tn-avatar.vue?5134", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-avatar/tn-avatar.vue?2cf5", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-avatar/tn-avatar.vue?a398", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-avatar/tn-avatar.vue?3c56", "uni-app:///tuniao-ui/components/tn-avatar/tn-avatar.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-avatar/tn-avatar.vue?a4a8", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-avatar/tn-avatar.vue?1756"], "names": ["mixins", "name", "props", "index", "type", "default", "shape", "size", "shadow", "border", "borderColor", "borderSize", "src", "text", "icon", "imgMode", "badge", "badgeSize", "badgeBgColor", "badgeColor", "badgeIcon", "badgeText", "badgePosition", "data", "imgLoadError", "computed", "showImg", "avatarClass", "clazz", "avatarS<PERSON>le", "style", "imgClass", "methods", "loadImageError", "click", "_checkSizeIsInline"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAqnB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0CzoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;EACAA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACA;IACAiB;MACAlB;MACAC;QACA;MACA;IACA;EACA;EACAkB;IACA;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACAC;MAEA;QACAA;MACA;MAEA;QACAA;MACA;MAEA;IACA;IACAC;MACA;MAEA;QACAC;MACA;QACAA;MACA;MAEA;QACAA;MACA;MAEA;QACAA;MACA;MAEA;QACAA;MACA;MAEA;QACAA;QACAA;MACA;MAEA;IACA;IACAC;MACA;MACAH;MAEA;IACA;EACA;EACAI;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA,+DACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC7NA;AAAA;AAAA;AAAA;AAAosC,CAAgB,0nCAAG,EAAC,C;;;;;;;;;;;ACAxtC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-avatar/tn-avatar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-avatar.vue?vue&type=template&id=27426dd6&scoped=true&\"\nvar renderjs\nimport script from \"./tn-avatar.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-avatar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-avatar.vue?vue&type=style&index=0&id=27426dd6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"27426dd6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-avatar/tn-avatar.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-avatar.vue?vue&type=template&id=27426dd6&scoped=true&\"", "var components\ntry {\n  components = {\n    tnBadge: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-badge/tn-badge\" */ \"@/tuniao-ui/components/tn-badge/tn-badge.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.avatarStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-avatar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-avatar.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view\r\n    class=\"tn-avatar-class tn-avatar\"\r\n    :class=\"[backgroundColorClass,fontColorClass,avatarClass]\"\r\n    :style=\"[avatarStyle]\"\r\n    @tap=\"click\"\r\n  >\r\n    <image\r\n      v-if=\"showImg\"\r\n      class=\"tn-avatar__img\"\r\n      :class=\"[imgClass]\"\r\n      :src=\"src\"\r\n      :mode=\"imgMode || 'aspectFill'\"\r\n      @error=\"loadImageError\"\r\n    ></image>\r\n    <view v-else class=\"tn-avatar__text\" >\r\n      <view v-if=\"text\">{{ text }}</view>\r\n      <view v-else :class=\"[`tn-icon-${icon}`]\"></view>\r\n    </view>\r\n    \r\n    <!-- 角标 -->\r\n    <tn-badge\r\n      v-if=\"badge && (badgeIcon || badgeText)\"\r\n      :radius=\"badgeSize\"\r\n      :backgroundColor=\"badgeBgColor\"\r\n      :fontColor=\"badgeColor\"\r\n      :fontSize=\"badgeSize - 8\"\r\n      :absolute=\"true\"\r\n      :top=\"badgePosition[0]\"\r\n      :right=\"badgePosition[1]\"\r\n    >\r\n      <view v-if=\"badgeIcon && badgeText === ''\">\r\n        <view :class=\"[`tn-icon-${badgeIcon}`]\"></view>\r\n      </view>\r\n      <view v-else>\r\n        {{ badgeText }}\r\n      </view>\r\n    </tn-badge>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import componentsColorMixin from '../../libs/mixin/components_color.js'\r\n  export default {\r\n    mixins: [componentsColorMixin],\r\n    name: 'tn-avatar',\r\n    props: {\r\n      // 序号\r\n      index: {\r\n        type: [Number, String],\r\n        default: 0\r\n      },\r\n      // 头像类型\r\n      // square 带圆角正方形 circle 圆形\r\n      shape: {\r\n        type: String,\r\n        default: 'circle'\r\n      },\r\n      // 大小 \r\n      // sm 小头像 lg 大头像 xl 加大头像\r\n      // 如果为其他则认为是直接设置大小\r\n      size: {\r\n        type: [Number, String],\r\n        default: ''\r\n      },\r\n      // 是否显示阴影\r\n      shadow: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 是否显示边框\r\n      border: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 边框颜色\r\n      borderColor: {\r\n        type: String,\r\n        default: 'rgba(0, 0, 0, 0.1)'\r\n      },\r\n      // 边框大小, rpx\r\n      borderSize: {\r\n        type: Number,\r\n        default: 2\r\n      },\r\n      // 头像路径\r\n      src: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // 文字\r\n      text: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // 图标\r\n      icon: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // 当设置为显示头像信息时，\r\n      // 图片的裁剪模式\r\n      imgMode: {\r\n        type: String,\r\n        default: 'aspectFill'\r\n      },\r\n      // 是否显示角标\r\n      badge: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      // 设置显示角标后，角标大小\r\n      badgeSize: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // 角标背景颜色\r\n      badgeBgColor: {\r\n        type: String,\r\n        default: '#AAAAAA'\r\n      },\r\n      // 角标字体颜色\r\n      badgeColor: {\r\n        type: String,\r\n        default: '#FFFFFF'\r\n      },\r\n      // 角标图标\r\n      badgeIcon: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // 角标文字，优先级比icon高\r\n      badgeText: {\r\n        type: String,\r\n        default: ''\r\n      },\r\n      // 角标坐标\r\n      // [top, right]\r\n      badgePosition: {\r\n        type: Array,\r\n        default() {\r\n          return [0, 0]\r\n        }\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        // 图片显示是否发生错误\r\n        imgLoadError: false\r\n      }\r\n    },\r\n    computed: {\r\n      showImg() {\r\n        // 如果设置了图片地址，则为显示图片，否则为显示文本\r\n        return this.text === '' && this.icon === ''\r\n      },\r\n      avatarClass() {\r\n        let clazz = ''\r\n        clazz += ` tn-avatar--${this.shape}`\r\n        \r\n        if (this._checkSizeIsInline()) {\r\n          clazz += ` tn-avatar--${this.size}`\r\n        }\r\n        \r\n        if (this.shadow) {\r\n          clazz += ' tn-avatar--shadow'\r\n        }\r\n        \r\n        return clazz\r\n      },\r\n      avatarStyle() {\r\n        let style = {}\r\n        \r\n        if (this.backgroundColorStyle) {\r\n          style.background = this.backgroundColorStyle\r\n        } else if (this.shadow && this.showImg) {\r\n          style.backgroundImage = `url(${this.src})`\r\n        }\r\n\r\n        if(this.fontColorStyle) {\r\n\t\t\tstyle.color = this.fontColorStyle\r\n\t\t}\r\n\t\t\r\n\t\tif(this.fontSizeStyle) {\r\n\t\t\tstyle.fontSize = this.fontSizeStyle\r\n\t\t}\r\n        \r\n        if (this.border) {\r\n          style.border = `${this.borderSize}rpx solid ${this.borderColor}`\r\n        }\r\n        \r\n        if (!this._checkSizeIsInline()) {\r\n          style.width = this.size\r\n          style.height = this.size\r\n        }\r\n        \r\n        return style\r\n      },\r\n      imgClass() {\r\n        let clazz = ''\r\n        clazz += ` tn-avatar__img--${this.shape}`\r\n        \r\n        return clazz\r\n      }\r\n    },\r\n    methods: {\r\n      // 加载图片失败\r\n      loadImageError() {\r\n        this.imgLoadError = true\r\n      },\r\n      // 点击事件\r\n      click() {\r\n        this.$emit(\"click\", this.index)\r\n      },\r\n      \r\n      // 检查是否使用内置的大小进行设置\r\n      _checkSizeIsInline() {\r\n        if (/^(xs|sm|md|lg|xl|xxl)$/.test(this.size)) return true\r\n        else return false\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  \r\n  .tn-avatar {\r\n    /* #ifndef APP-NVUE */\r\n    display: inline-flex;\r\n    /* #endif */\r\n    margin: 0;\r\n    padding: 0;\r\n    text-align: center;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background-color: $tn-font-holder-color;\r\n    // color: #FFFFFF;\r\n    white-space: nowrap;\r\n    position: relative;\r\n    width: 64rpx;\r\n    height: 64rpx;\r\n    z-index: 1;\r\n    \r\n    &--sm {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n    }\r\n    &--lg {\r\n      width: 96rpx;\r\n      height: 96rpx;\r\n    }\r\n    &--xl {\r\n      width: 128rpx;\r\n      height: 128rpx;\r\n    }\r\n    \r\n    &--square {\r\n      border-radius: 10rpx;\r\n    }\r\n    \r\n    &--circle {\r\n      border-radius: 5000rpx;\r\n    }\r\n    \r\n    &--shadow {\r\n      position: relative;\r\n      \r\n      &::after {\r\n        content: \" \";\r\n        display: block;\r\n        background: inherit;\r\n        filter: blur(10rpx);\r\n        position: absolute;\r\n        width: 100%;\r\n        height: 100%;\r\n        top: 10rpx;\r\n        left: 10rpx;\r\n        z-index: -1;\r\n        opacity: 0.4;\r\n        transform-origin: 0 0;\r\n        border-radius: inherit;\r\n        transform: scale(1, 1);\r\n      }\r\n    }\r\n    \r\n    &__img {\r\n      width: 100%;\r\n      height: 100%;\r\n      \r\n      &--square {\r\n        border-radius: 10rpx;\r\n      }\r\n      \r\n      &--circle {\r\n        border-radius: 5000rpx;\r\n      }\r\n    }\r\n    \r\n    &__text {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-avatar.vue?vue&type=style&index=0&id=27426dd6&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-avatar.vue?vue&type=style&index=0&id=27426dd6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980404999\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}