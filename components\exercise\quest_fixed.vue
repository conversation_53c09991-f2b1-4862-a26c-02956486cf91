<template>
	<view class="quest_foot_fixed tn-flex tn-flex-col-center tn-flex-row-between">
		<view class="qff_left tn-flex tn-flex-col-center  tn-flex-row-center">
			<view class="tn-flex tn-flex-direction-column tn-flex-col-center" @click.stop="showCard()">
				<image src="../../static/icon/card.png" mode="widthFix" style="width: 50rpx;"></image>
				<view style="color: #666666;font-size: 22rpx;">
					答题卡
				</view>
			</view>
		</view>
		<view class="qff_right tn-flex tn-flex-col-center tn-flex-row-between">
			<view class="last_btn" @click.stop="last()">
				上一题
			</view>
			<view class="next_btn" @click.stop="next()">
				{{end?'完成':'下一题'}}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "quest_fixed_rem",
		props: {
			end: {
				type: Boolean,
				default: false
			},
		},
		data() {
			return {

			};
		},
		methods: {
			showCard(){
				this.$emit("showCard")
			},
			last() {
				this.$emit("lastQuest")
			},
			next() {
				this.$emit("nextQuest")
			},
		}
	}
</script>

<style lang="scss" scoped>
	.qff_right {
		width: 580rpx;
	}

	.next_btn {
		width: 280rpx;
		height: 80rpx;
		border-radius: 55rpx;
		box-sizing: border-box;
		border: 2rpx solid #5552FF;
		background-color: #5552FF;
		text-align: center;
		line-height: 80rpx;
		color: #ffffff;
		font-size: 32rpx;
	}

	.last_btn {
		width: 280rpx;
		height: 80rpx;
		border-radius: 55rpx;
		box-sizing: border-box;
		border: 2rpx solid #5552FF;
		text-align: center;
		line-height: 80rpx;
		color: #5552FF;
		font-size: 32rpx;
	}

	.qff_left {
		width: 100rpx;
	}

	.quest_foot_fixed {
		position: fixed;
		bottom: 0rpx;
		left: 0rpx;
		right: 0rpx;
		height: 100rpx;
		background-color: #FFFFFF;
		padding: 0rpx 30rpx;
		box-sizing: border-box;
	}
</style>