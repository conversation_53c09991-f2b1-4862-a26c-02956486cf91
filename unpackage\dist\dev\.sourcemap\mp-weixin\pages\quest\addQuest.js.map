{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/project/shuati_new/pages/quest/addQuest.vue?bdb1", "webpack:///D:/project/shuati_new/pages/quest/addQuest.vue?dc7a", "webpack:///D:/project/shuati_new/pages/quest/addQuest.vue?5cd6", "uni-app:///pages/quest/addQuest.vue", "webpack:///D:/project/shuati_new/pages/quest/addQuest.vue?f651", "webpack:///D:/project/shuati_new/pages/quest/addQuest.vue?cd06"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "data", "labelStyle", "fontSize", "fontWeight", "baseUrl", "selType", "code", "isSub", "service_img", "showKefu", "img1", "img2", "form1", "title", "solution", "analysis", "source", "form2", "exam_title", "src", "onLoad", "withShareTicket", "menus", "methods", "submit1", "uni", "icon", "type", "success", "uploadFile", "count", "profile", "myUpload", "mask", "url", "header", "filePath", "name", "duration", "fail", "submit", "img", "getsysconfig"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gNAEN;AACP,KAAK;AACL;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAqmB,CAAgB,knBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC0GznB;AAAA;AAAA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAJ;QACAK;QACAC;MACA;IACA;EACA;EACAC;IAEA1B;MAAA;MACA2B;MACAC;IACA;IAEA;EACA;EAEAC;IACAC;MACA;MACA;QACA;UACAC;YACAZ;YACAa;UACA;UACA;QACA;QACA1B;UACA2B;QAAA,GACA,WACA;MACA;MACA;QACA;UACAF;YACAZ;YACAa;UACA;UACA;QACA;QACA1B;UACA2B;QAAA,GACA,WACA;MACA;MACA;QACA;UACAF;YACAZ;YACAa;YACAE;cACAH;YACA;UACA;QACA;MACA;IACA;IACAI;MAAA;MACA;MACAnC;QACAoC;QAAA;QACAH;QACAC;UACAG;UACA;QACA;MACA;IACA;IACAC;MAAA;MACAP;QACAQ;MACA;MACAR;QACAS;QACAC;UACA;QACA;QACAC;QACAC;QACAT;UACAH;UACA;UACA;YACA;YACA;UACA;YACAA;cACAZ;cACAyB;cACAZ;YACA;UACA;QACA;QACAa;UACAd;QACA;MACA;IACA;IACAe;MACA;MACA;QACAxC;MACA;MACA;QACAA;MACA;MACA;QACAyB;UACAZ;UACAa;QACA;QACA;MACA;MACA;QACAe;MACA;QACA;UACAhB;YACAZ;YACAa;YACAE;cACAH;YACA;UACA;QACA;MACA;IACA;IACAiB;MAAA;MAAA;MACA;QACA;UACA;UACA;UACAjB;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3QA;AAAA;AAAA;AAAA;AAAwqC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACA5rC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/quest/addQuest.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/quest/addQuest.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./addQuest.vue?vue&type=template&id=44c085f2&scoped=true&\"\nvar renderjs\nimport script from \"./addQuest.vue?vue&type=script&lang=js&\"\nexport * from \"./addQuest.vue?vue&type=script&lang=js&\"\nimport style0 from \"./addQuest.vue?vue&type=style&index=0&id=44c085f2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"44c085f2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/quest/addQuest.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addQuest.vue?vue&type=template&id=44c085f2&scoped=true&\"", "var components\ntry {\n  components = {\n    tnForm: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-form/tn-form\" */ \"@/tuniao-ui/components/tn-form/tn-form.vue\"\n      )\n    },\n    tnFormItem: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-form-item/tn-form-item\" */ \"@/tuniao-ui/components/tn-form-item/tn-form-item.vue\"\n      )\n    },\n    tnInput: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-input/tn-input\" */ \"@/tuniao-ui/components/tn-input/tn-input.vue\"\n      )\n    },\n    tnPopup: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-popup/tn-popup\" */ \"@/tuniao-ui/components/tn-popup/tn-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.selType = 0\n    }\n    _vm.e1 = function ($event) {\n      $event.stopPropagation()\n      _vm.selType = 1\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addQuest.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addQuest.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"tn-text-bold tn-text-lx\">\r\n\t\t\t\t上传截图-解锁强化题库\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tn-width-full tn-flex\" style=\"margin-top: 30rpx;\">\r\n\t\t\t\t<view :class=\"selType==0?'is_sel':'no_sel'\" @click.stop=\"selType=0\">\r\n\t\t\t\t\t上传试题\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"width: 30rpx;\"></view>\r\n\t\t\t\t<view :class=\"selType==1?'is_sel':'no_sel'\" @click.stop=\"selType=1\">\r\n\t\t\t\t\t上传试卷\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"body\" v-if=\"selType==0\">\r\n\t\t\t<tn-form :model=\"form1\" ref=\"form\" :errorType=\"['message']\">\r\n\t\t\t\t<tn-form-item label=\"标题\" :labelStyle=\"labelStyle\" required prop=\"title\" :borderBottom=\"false\"\r\n\t\t\t\t\tlabelPosition=\"top\">\r\n\t\t\t\t\t<view class=\"tn-width-full\"\r\n\t\t\t\t\t\tstyle=\"background-color: #F8F8F8;padding: 30rpx;box-sizing: border-box;border-radius: 20rpx;\">\r\n\t\t\t\t\t\t<tn-input type=\"textarea\" placeholder=\"如果是选择题，试题内容需包含选项\" v-model=\"form1.title\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</tn-form-item>\r\n\t\t\t\t<tn-form-item label=\"答案\" :labelStyle=\"labelStyle\" required prop=\"solution\" :borderBottom=\"false\"\r\n\t\t\t\t\tlabelPosition=\"top\">\r\n\t\t\t\t\t<view class=\"tn-width-full\"\r\n\t\t\t\t\t\tstyle=\"background-color: #F8F8F8;padding: 30rpx;box-sizing: border-box;border-radius: 20rpx;\">\r\n\t\t\t\t\t\t<tn-input type=\"textarea\" placeholder=\"请输入完整答案\" v-model=\"form1.solution\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</tn-form-item>\r\n\t\t\t\t<tn-form-item label=\"解析\" :labelStyle=\"labelStyle\" prop=\"analysis\" :borderBottom=\"false\"\r\n\t\t\t\t\tlabelPosition=\"top\">\r\n\t\t\t\t\t<view class=\"tn-width-full\"\r\n\t\t\t\t\t\tstyle=\"background-color: #F8F8F8;padding: 30rpx;box-sizing: border-box;border-radius: 20rpx;\">\r\n\t\t\t\t\t\t<tn-input type=\"textarea\" placeholder=\"如果没有解析可以不填写\" v-model=\"form1.analysis\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</tn-form-item>\r\n\t\t\t\t<tn-form-item label=\"试题来源\" :labelStyle=\"labelStyle\" prop=\"source\" :borderBottom=\"false\"\r\n\t\t\t\t\tlabelPosition=\"top\">\r\n\t\t\t\t\t<view class=\"tn-width-full\"\r\n\t\t\t\t\t\tstyle=\"background-color: #F8F8F8;padding: 30rpx;box-sizing: border-box;border-radius: 20rpx;\">\r\n\t\t\t\t\t\t<tn-input type=\"textarea\" placeholder=\"填写试题的来源教材或网课名称，如《1000题》马原第一章第二题\"\r\n\t\t\t\t\t\t\tv-model=\"form1.source\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</tn-form-item>\r\n\t\t\t</tn-form>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-row-center tn-flex-col-center\">\r\n\t\t\t\t<view :class=\"isSub?'submit':'nosubmit'\" @click.stop=\"submit1()\">\r\n\t\t\t\t\t提交\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"body\" v-if=\"selType==1\">\r\n\t\t\t<tn-form :model=\"form2\" ref=\"form\" :errorType=\"['message']\">\r\n\t\t\t\t<tn-form-item label=\"标题\" :labelStyle=\"labelStyle\" required prop=\"title\" :borderBottom=\"false\"\r\n\t\t\t\t\tlabelPosition=\"top\">\r\n\t\t\t\t\t<view class=\"tn-width-full\"\r\n\t\t\t\t\t\tstyle=\"background-color: #F8F8F8;padding: 30rpx;box-sizing: border-box;border-radius: 20rpx;\">\r\n\t\t\t\t\t\t<tn-input type=\"textarea\" placeholder=\"如：英语\" v-model=\"form2.title\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</tn-form-item>\r\n\t\t\t\t<tn-form-item label=\"考试全称\" required :labelStyle=\"labelStyle\" prop=\"exam_title\" :borderBottom=\"false\"\r\n\t\t\t\t\tlabelPosition=\"top\">\r\n\t\t\t\t\t<view class=\"tn-width-full\"\r\n\t\t\t\t\t\tstyle=\"background-color: #F8F8F8;padding: 30rpx;box-sizing: border-box;border-radius: 20rpx;\">\r\n\t\t\t\t\t\t<tn-input type=\"textarea\" placeholder=\"如：2022高等数学A期末考试\" v-model=\"form2.exam_title\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</tn-form-item>\r\n\t\t\t\t<tn-form-item label=\"试卷文档\" required :labelStyle=\"labelStyle\" prop=\"src\" :borderBottom=\"false\"\r\n\t\t\t\t\tlabelPosition=\"top\">\r\n\t\t\t\t\t<view class=\"upload2\" v-if=\"form2.src\" @click.stop=\"uploadFile()\">\r\n\t\t\t\t\t\t<!-- <image :src=\"baseUrl+img1\" mode=\"aspectFill\" style=\"width: 100%;height: 100%;\"></image> -->\r\n\t\t\t\t\t\t文件已上传，可点击更改或提交\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @click.stop=\"uploadFile()\"\r\n\t\t\t\t\t\tclass=\"upload tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-center\" v-else>\r\n\t\t\t\t\t\t<text class=\"tn-icon-add tn-text-bold\" style=\"font-size: 44rpx;color: #999999;\"></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</tn-form-item>\r\n\t\t\t</tn-form>\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-row-center tn-flex-col-center\">\r\n\t\t\t\t<view :class=\"isSub?'submit':'nosubmit'\" @click.stop=\"submit1()\">\r\n\t\t\t\t\t提交\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<tn-popup v-model=\"showKefu\" mode=\"bottom\" :borderRadius=\"40\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t客服帮助</view>\r\n\t\t\t<view class=\"tn-width-full\" style=\"\">\r\n\t\t\t\t<view class=\"tn-flex tn-flex-row-center\"\r\n\t\t\t\t\tstyle=\"padding: 30rpx 46rpx 66rpx 46rpx;box-sizing: border-box;\">\r\n\t\t\t\t\t<view style=\"width: 400rpx;height: 400rpx;\">\r\n\t\t\t\t\t\t<image show-menu-by-longpress style=\"width: 400rpx;height: 400rpx;\" :src=\"baseUrl+service_img\"\r\n\t\t\t\t\t\t\tmode=\"\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport shareMixin from \"@/mixins/shareMixin.js\"\r\n\texport default {\r\n\t\tmixins: [shareMixin],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tlabelStyle: {\r\n\t\t\t\t\tfontSize: '30rpx',\r\n\t\t\t\t\tfontWeight: 'bold'\r\n\t\t\t\t},\r\n\t\t\t\tbaseUrl: this.$config.baseUrl,\r\n\t\t\t\tselType: 0,\r\n\t\t\t\tcode: \"\",\r\n\t\t\t\tisSub: true,\r\n\t\t\t\tservice_img: \"\",\r\n\t\t\t\tshowKefu: false,\r\n\t\t\t\timg1: \"\",\r\n\t\t\t\timg2: \"\",\r\n\t\t\t\tform1: {\r\n\t\t\t\t\ttitle: '',\r\n\t\t\t\t\tsolution: '',\r\n\t\t\t\t\tanalysis: '',\r\n\t\t\t\t\tsource: ''\r\n\t\t\t\t},\r\n\t\t\t\tform2: {\r\n\t\t\t\t\ttitle: \"\",\r\n\t\t\t\t\texam_title: \"\",\r\n\t\t\t\t\tsrc: \"\"\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t\tthis.getsysconfig()\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\tsubmit1() {\r\n\t\t\t\tlet data = {}\r\n\t\t\t\tif (this.selType == 0) {\r\n\t\t\t\t\tif (!this.form1.title || !this.form1.solution) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"请填写必填项\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\t}\r\n\t\t\t\t\tdata = {\r\n\t\t\t\t\t\ttype: this.selType,\r\n\t\t\t\t\t\t...this.form1\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.selType == 1) {\r\n\t\t\t\t\tif (!this.form2.title || !this.form2.exam_title || !this.form2.src) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"请填写必填项\",\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\t}\r\n\t\t\t\t\tdata = {\r\n\t\t\t\t\t\ttype: this.selType,\r\n\t\t\t\t\t\t...this.form2\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.$http.post(this.$api.uploadExam, data).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"上传成功\",\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tuploadFile() {\r\n\t\t\t\tlet profile = ''\r\n\t\t\t\twx.chooseMessageFile({\r\n\t\t\t\t\tcount: 1, //默认9\r\n\t\t\t\t\ttype: 'file',\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tprofile = res.tempFiles[0].path;\r\n\t\t\t\t\t\tthis.myUpload(profile)\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tmyUpload(rsp) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\tmask: true,\r\n\t\t\t\t})\r\n\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\turl: this.$config.baseUrl + \"/api/upload/upload\",\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\t'Authorization': uni.getStorageSync('TOKEN'),\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfilePath: rsp,\r\n\t\t\t\t\tname: 'file',\r\n\t\t\t\t\tsuccess: (ress) => {\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tlet mdata = JSON.parse(ress.data);\r\n\t\t\t\t\t\tif (mdata.code == 200) {\r\n\t\t\t\t\t\t\tthis.form2.src = mdata.data.src\r\n\t\t\t\t\t\t\tthis.$forceUpdate()\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: mdata.msg,\r\n\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsubmit() {\r\n\t\t\t\tlet data = []\r\n\t\t\t\tif (this.img1) {\r\n\t\t\t\t\tdata.push(this.img1)\r\n\t\t\t\t}\r\n\t\t\t\tif (this.img2) {\r\n\t\t\t\t\tdata.push(this.img2)\r\n\t\t\t\t}\r\n\t\t\t\tif (data.length == 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"请上传图片\",\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\tthis.$http.post(this.$api.shareActive, {\r\n\t\t\t\t\timg: data\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"上传成功，等待审核\",\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetsysconfig() { // 获取系统配置\r\n\t\t\t\tthis.$http.post(this.$api.systemData, {}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tlet datas = res.data;\r\n\t\t\t\t\t\tthis.service_img = datas.config.service_img\r\n\t\t\t\t\t\tuni.setStorageSync('systemData', datas)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.body {\r\n\t\twidth: 750rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tpadding: 0rpx 30rpx 30rpx 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.is_sel {\r\n\t\twidth: 180rpx;\r\n\t\theight: 88rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tbackground-color: #E3E3FF;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #5552FF;\r\n\t\tfont-weight: bold;\r\n\t\ttext-align: center;\r\n\t\tline-height: 88rpx;\r\n\t}\r\n\r\n\t.no_sel {\r\n\t\twidth: 180rpx;\r\n\t\theight: 88rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tbackground-color: #F7F7F7;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t\ttext-align: center;\r\n\t\tline-height: 88rpx;\r\n\t}\r\n\r\n\t.upload2 {\r\n\t\twidth: 450rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #F7F7F7;\r\n\t\tbox-sizing: border-box;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t\tborder: 2rpx dashed #999999;\r\n\t}\r\n\r\n\t.upload {\r\n\t\twidth: 160rpx;\r\n\t\theight: 160rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #F7F7F7;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder: 2rpx dashed #999999;\r\n\t}\r\n\r\n\t.submit_body {\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tborder-radius: 30rpx;\r\n\t}\r\n\r\n\tpage {\r\n\t\tbackground-color: #f7f7f7 !important;\r\n\t}\r\n\r\n\t.header {\r\n\t\twidth: 750rpx;\r\n\t\tborder-radius: 0rpx 0rpx 20rpx 20rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground-color: #FFFFFF;\r\n\t}\r\n\r\n\t.fab_btn {\r\n\t\twidth: 154rpx;\r\n\t\tposition: fixed;\r\n\t\tright: 0rpx;\r\n\t\tbottom: 150rpx;\r\n\t}\r\n\r\n\t.submit {\r\n\t\twidth: 630rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbackground-color: #3775F6;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #Ffffff;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.nosubmit {\r\n\t\twidth: 630rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbackground-color: #9f9f9f;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #Ffffff;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.input_warp {\r\n\t\twidth: 630rpx;\r\n\t\theight: 114rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #F4F4F4;\r\n\t\tmargin-top: 24rpx;\r\n\t\tpadding: 0rpx 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\tpage {\r\n\t\tbackground-color: #Ffffff;\r\n\t}\r\n\r\n\t.content {\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addQuest.vue?vue&type=style&index=0&id=44c085f2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./addQuest.vue?vue&type=style&index=0&id=44c085f2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753982221744\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}