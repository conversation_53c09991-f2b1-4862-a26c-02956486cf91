/**
 * 分享功能辅助工具
 * 提供获取分享图片的通用方法，包含缓存和降级处理
 */

import { http } from '@/utils/request/index.js'
import { getPublicConfig } from '@/api/api.js'
import indexConfig from '@/config/index.js'

class ShareHelper {
  constructor() {
    this.shareImageCache = null
    this.cacheTime = null
    this.cacheExpiry = 30 * 60 * 1000 // 30分钟缓存
    this.defaultImage = '/static/222.jpg'
    this.isLoading = false
    this.loadingPromise = null
  }

  /**
   * 获取分享图片URL
   * @returns {Promise<string>} 分享图片的完整URL
   */
  async getShareImage() {
    try {
      // 检查缓存是否有效
      if (this.isValidCache()) {
        return this.buildImageUrl(this.shareImageCache)
      }

      // 如果正在加载中，返回加载中的Promise
      if (this.isLoading && this.loadingPromise) {
        return await this.loadingPromise
      }

      // 开始加载
      this.isLoading = true
      this.loadingPromise = this.fetchShareImage()
      
      const result = await this.loadingPromise
      this.isLoading = false
      this.loadingPromise = null
      
      return result
    } catch (error) {
      console.error('获取分享图片失败:', error)
      this.isLoading = false
      this.loadingPromise = null
      return this.buildImageUrl(this.defaultImage)
    }
  }

  /**
   * 从API获取分享图片
   * @returns {Promise<string>} 分享图片的完整URL
   */
  async fetchShareImage() {
    try {
      const response = await http.get(getPublicConfig, {
        name: 'share_image'
      })

      if (response && response.code === 200 && response.data && response.data.value) {
        // 更新缓存
        this.shareImageCache = response.data.value
        this.cacheTime = Date.now()
        
        return this.buildImageUrl(this.shareImageCache)
      } else {
        console.warn('API返回数据格式不正确:', response)
        return this.buildImageUrl(this.defaultImage)
      }
    } catch (error) {
      console.error('调用分享图片API失败:', error)
      return this.buildImageUrl(this.defaultImage)
    }
  }

  /**
   * 检查缓存是否有效
   * @returns {boolean} 缓存是否有效
   */
  isValidCache() {
    return this.shareImageCache && 
           this.cacheTime && 
           (Date.now() - this.cacheTime) < this.cacheExpiry
  }

  /**
   * 构建完整的图片URL
   * @param {string} imagePath 图片路径
   * @returns {string} 完整的图片URL
   */
  buildImageUrl(imagePath) {
    if (!imagePath) {
      imagePath = this.defaultImage
    }
    
    // 如果已经是完整URL，直接返回
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath + '?t=' + new Date().getTime()
    }
    
    // 构建完整URL
    const baseUrl = indexConfig.baseUrl
    const fullUrl = baseUrl + imagePath
    return fullUrl + '?t=' + new Date().getTime()
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.shareImageCache = null
    this.cacheTime = null
  }

  /**
   * 预加载分享图片（可选，用于提前加载）
   */
  async preloadShareImage() {
    try {
      await this.getShareImage()
    } catch (error) {
      console.warn('预加载分享图片失败:', error)
    }
  }

  /**
   * 获取分享配置对象
   * @param {Object} options 分享选项
   * @param {string} options.title 分享标题
   * @param {string} options.path 分享路径
   * @returns {Promise<Object>} 分享配置对象
   */
  async getShareConfig(options = {}) {
    const {
      title = '考研政治刷题库，名师题库免费刷，超100w考生都在用！',
      path = '/pages/quest/quest'
    } = options

    const imageUrl = await this.getShareImage()

    return {
      title,
      path,
      imageUrl
    }
  }
}

// 创建单例实例
const shareHelper = new ShareHelper()

export default shareHelper
