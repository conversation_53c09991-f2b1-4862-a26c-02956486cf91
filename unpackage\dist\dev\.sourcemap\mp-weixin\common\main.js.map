{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/project/shuati_new/App.vue?8bc7", "uni-app:///App.vue", "webpack:///D:/project/shuati_new/App.vue?0987", "webpack:///D:/project/shuati_new/App.vue?ca3f", "webpack:///D:/project/shuati_new/App.vue?4443", "webpack:///D:/project/shuati_new/App.vue?17e5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "use", "TuniaoUI", "vuexStore", "require", "mixin", "config", "productionTip", "prototype", "$publicjs", "publicjs", "$config", "$http", "http", "api", "$api", "$tools", "tools", "App", "mpType", "app", "store", "$mount", "onLaunch", "withShareTicket", "menus", "onShow", "console", "uni", "provider", "success", "code", "key", "data", "onHide", "methods", "updateManagerfun", "updateManager", "content", "showCancel", "confirmText", "getversion", "plus", "url"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAE2D;AAG3D;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AAGA;AAEA;AAA2B;AAAA;AAAA;AAAA;AAlB3B;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAgB1DC,YAAG,CAACC,GAAG,CAACC,iBAAQ,CAAC;AAEjB;AACA,IAAIC,SAAS,GAAGC,mBAAO,CAAC,8BAAsB,CAAC;AAC/CJ,YAAG,CAACK,KAAK,CAACF,SAAS,CAAC;AAGpBH,YAAG,CAACM,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCP,YAAG,CAACQ,SAAS,CAACC,SAAS,GAAGC,iBAAQ,EAAE;AACpCV,YAAG,CAACQ,SAAS,CAACG,OAAO,GAAGL,cAAM,EAAE;AAChCN,YAAG,CAACQ,SAAS,CAACI,KAAK,GAAGC,aAAI,EAAE;AAC5Bb,YAAG,CAACc,GAAG,GAAGd,YAAG,CAACQ,SAAS,CAACO,IAAI,GAAGD,GAAG,EAAE;AACpCd,YAAG,CAACQ,SAAS,CAACQ,MAAM,GAAGC,eAAK,EAAE;AAC9BC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAIpB,YAAG;EAClBqB,KAAK,EAALA;AAAK,GACDH,YAAG,EACN;AACF,UAAAE,GAAG,EAACE,MAAM,EAAE,C;;;;;;;;;;;;;ACpCZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;AACD;;;AAG/D;AAC6J;AAC7J,gBAAgB,6KAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAkkB,CAAgB,6mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;eCCtlB;EACAC;IACA;IACA;IACA;IACA;IACA;;IAMA;IACA1B;MAAA;MACA2B;MACAC;IACA;EAEA;EACAC;IAAA;IACAC;IAEAC;MACAC;MACAC;QACA;UACAC;QACA;UACA;YACAH;cACAI;cACAC;cACA;YACA;UACA;QACA;MACA;IACA;EAEA;;EACAC;IACAP;EACA;EACAQ;IACAC;MAAA;MACA;MACAC;QACA;UACAT;YACAU;YACAC;YACAC;YACAV;cACA;gBACAO;kBACAA;gBACA;gBACAA;kBAAA;kBACA;kBACAT;oBACAU;oBACAC;oBACAC;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACAC;QACA;UACA;YACA;cACAd;gBACAe;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvFA;AAAA;AAAA;AAAA;AAAqlC,CAAgB,4lCAAG,EAAC,C;;;;;;;;;;;ACAzmC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAy0B,CAAgB,k2BAAG,EAAC,C;;;;;;;;;;;ACA71B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from './App'\r\n\r\n\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor'\r\nimport {\r\n\thttp\r\n} from '@/utils/request';\r\nimport * as api from './api/api.js'\r\nimport tools from '@/utils/common.js'\r\nimport publicjs from '@/utils/publicJs.js'\r\nimport config from '@/config/index.js'\r\nimport '@/utils'\r\n\r\n// 引入全局TuniaoUI\r\nimport TuniaoUI from 'tuniao-ui'\r\nVue.use(TuniaoUI)\r\nimport store from './store'\r\n// 引入TuniaoUI提供的vuex简写方法\r\nlet vuexStore = require('@/store/$tn.mixin.js')\r\nVue.mixin(vuexStore)\r\n\r\n\r\nVue.config.productionTip = false\r\nVue.prototype.$publicjs = publicjs  // 封装方法\r\nVue.prototype.$config = config  // 域名\r\nVue.prototype.$http = http  // get post put delete ...\r\nVue.api = Vue.prototype.$api = api  // 接口\r\nVue.prototype.$tools = tools  // toast\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n\tstore,\r\n  ...App\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\nimport style1 from \"./App.vue?vue&type=style&index=1&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\t// uni.setStorage({\r\n\t\t\t// \tkey: \"TOKEN\",\r\n\t\t\t// \tdata: 'oyU6A7dAoFs_n-1vRh7wRHqa94eI'\r\n\t\t\t// \t// data: '123456789'\r\n\t\t\t// })\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\t// this.getversion()\r\n\t\t\t// #endif\r\n\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tthis.updateManagerfun();\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tuni.login({\r\n\t\t\t\tprovider: 'weixin',\r\n\t\t\t\tsuccess: (suc) => {\r\n\t\t\t\t\tthis.$http.post(this.$api.login, {\r\n\t\t\t\t\t\tcode: suc.code\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\t\tuni.setStorage({\r\n\t\t\t\t\t\t\t\tkey: \"TOKEN\",\r\n\t\t\t\t\t\t\t\tdata: res.data\r\n\t\t\t\t\t\t\t\t// data: \"oyU6A7UHYmTsNOMmVB95qwXgArks\"\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tupdateManagerfun() { // 更新小程序\r\n\t\t\t\tconst updateManager = uni.getUpdateManager();\r\n\t\t\t\tupdateManager.onCheckForUpdate(res => {\r\n\t\t\t\t\tif (res.hasUpdate) {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\tcontent: '新版本已经准备好，是否重启应用？',\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\tconfirmText: '确定',\r\n\t\t\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\tupdateManager.onUpdateReady(function(res) {\r\n\t\t\t\t\t\t\t\t\t\tupdateManager.applyUpdate();\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tupdateManager.onUpdateFailed(res => { // 新版本下载失败的回调\r\n\t\t\t\t\t\t\t\t\t\t// 新版本下载失败，提示用户删除后通过冷启动重新打开\r\n\t\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\t\tcontent: '下载失败，请删除当前小程序后重新打开',\r\n\t\t\t\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\t\t\t\tconfirmText: '知道了'\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//更新app\r\n\t\t\tgetversion() {\r\n\t\t\t\tplus.runtime.getProperty(plus.runtime.appid, (wgtinfo) => {\r\n\t\t\t\t\tthis.$http.post(this.$api.version).then(res => {\r\n\t\t\t\t\t\tif (res.code == 1) {\r\n\t\t\t\t\t\t\tif (wgtinfo.version == res.data.newversion) {} else {\r\n\t\t\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\t\t\turl: \"/pages/fullscreen/fullscreen\"\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\t/* 注意要写在第一行，同时给style标签加入lang=\"scss\"属性 */\r\n\t@import './tuniao-ui/index.scss';\r\n\t@import './tuniao-ui/iconfont.css';\r\n\r\n\timage {\r\n\t\t@include cssimg;\r\n\t}\r\n</style>\r\n<style>\r\n\t/*每个页面公共css */\r\n\tpage {\r\n\t\tbackground-color: #f7f7f7;\r\n\t}\r\n</style>", "import mod from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980405873\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=1&lang=css&\"; export default mod; export * from \"-!../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=1&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980380699\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}