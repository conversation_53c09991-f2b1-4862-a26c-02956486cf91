<template>
	<view class="charts_warp tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-between">
		<view class="top tn-flex tn-flex-row-between tn-flex-col-center">
			<view class="cwt_left tn-flex tn-flex-col-center">
				<view class="dot_word tn-text-bold">
					各章节正确率
				</view>
			</view>
			<view class="cwt_right tn-flex tn-flex-col-center">
				<view>
					<image src="../../static/icon/mine_tips.png" mode="widthFix" style="width: 22rpx;margin-right: 5rpx;"></image> 我的
				</view>
				<view style="margin-left: 30rpx;">
					<image src="../../static/icon/avg_tips.png" mode="widthFix" style="width: 22rpx;margin-right: 5rpx;"></image> 平均
				</view>
			</view>
		</view>
		<view class="charts-box">
			<qiun-data-charts type="line" tooltipFormat="tooltipDemo1" :canvas2d="true" :opts="opts"
				:chartData="chartData" />
		</view>
	</view>
</template>

<script>
	export default {
		name: "chart_line",
		props: {
			chartData: {
				type: Object,
				default: () => {}
			},
			item: {
				type: Object,
				default: () => {}
			},
			name: {
				type: String,
				default: ""
			},
		},
		data() {
			return {
				opts: {
					padding: [15, 20, 10, 15],
					enableScroll: false,
					dataPointShapeType: "hollow",
					dataLabel: false,
					legend: {
						show: false,
					},
					xAxis: {
						disabled: false,
						axisLine: true,
						calibration: true,
						fontColor: "#666666",
						fontSize: 10,
						marginTop: 10,
						labelCount: 13
					},
					yAxis: {
						disabled: false,
						disableGrid: false,
						gridType: "dash",
						showTitle: false,
						splitNumber: 2,
						data: [{
							axisLine: false,
							fontColor: "#666666",
							fontSize: 13,
							min: 0,
							max: 100,
							unit: "%"
						}]
					},
					extra: {
						line: {
							type: "curve"
						},
						tooltip: {
							showBox: true,
							borderRadius: 4,
							bgOpacity: 1,
							gridType: "dash",
							bgColor: "#DEFAFF",
							fontColor: "#222222",
							legendShape: "rect"
						},
					},
				},
			}
		}
	}
</script>

<style lang="scss" scoped>
	.chart_bottom {
		width: 100%;
		padding: 0rpx 40rpx;
	}

	.cb_w {
		font-size: 24rpx;
		color: #333333;
	}

	.top {
		width: 100%;
		box-sizing: border-box;
		padding-right: 24rpx;
		margin-bottom: 20rpx;
	}

	.dot {
		width: 16rpx;
		height: 16rpx;
		border-radius: 0rpx 8rpx 8rpx 0rpx;
		background-color: #FF000A;
		margin-right: 6rpx;
	}

	.dot_word {
		font-size: 30rpx;
		color: #333333;
	}

	.cwt_right {
		font-size: 26rpx;
		color: #9E9E9E;
	}

	.charts_warp {
		width: 690rpx;
	}

	/* 请根据实际需求修改父元素尺寸，组件自动识别宽高 */
	.charts-box {
		width: 690rpx;
		height: 250rpx;
	}
</style>