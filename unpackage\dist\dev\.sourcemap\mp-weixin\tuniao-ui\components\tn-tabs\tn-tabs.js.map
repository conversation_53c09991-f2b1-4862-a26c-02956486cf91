{"version": 3, "sources": ["webpack:///D:/project/shuati_new/tuniao-ui/components/tn-tabs/tn-tabs.vue?8252", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-tabs/tn-tabs.vue?f1ed", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-tabs/tn-tabs.vue?509e", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-tabs/tn-tabs.vue?1a47", "uni-app:///tuniao-ui/components/tn-tabs/tn-tabs.vue", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-tabs/tn-tabs.vue?e402", "webpack:///D:/project/shuati_new/tuniao-ui/components/tn-tabs/tn-tabs.vue?6daa"], "names": ["mixins", "name", "props", "list", "type", "default", "count", "current", "isScroll", "height", "top", "itemWidth", "duration", "activeColor", "inactiveColor", "inactiveItemStyle", "activeItemStyle", "showBar", "<PERSON><PERSON><PERSON><PERSON>", "barHeight", "barStyle", "gutter", "badgeOffset", "bold", "computed", "tabBarStyle", "width", "borderRadius", "opacity", "transform", "transitionDuration", "Object", "tabItemStyle", "lineHeight", "fontSize", "padding", "flex", "style", "data", "id", "scrollLeft", "tabQueryInfo", "componentWidth", "scrollBarLeft", "componentLeft", "currentIndex", "barMoveFirst", "watch", "handler", "immediate", "mounted", "methods", "init", "tabRect", "clickTab", "getTabRect", "query", "size", "rect", "scrollByIndex", "setTimeout"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsK;AACtK,gBAAgB,6KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAmnB,CAAgB,inBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACgCvoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;EACAA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAJ;MACAG;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;QACA;MACA;IACA;IACA;IACAW;MACAZ;MACAC;QACA;MACA;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;QACA;MACA;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;QACA;MACA;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;EACA;EACAmB;IACA;IACAC;MACA;QACAC;QACAjB;QACAkB;QACA;QACAC;QACAC;QACAC;MACA;MACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACAN;UACAjB;UACAwB;UACAC;UACAC;UACAC;UACAN;QACA;QACA;UACA;YACAO;UACA;UACAA;UACAN;QACA;UACAM;UACAN;QACA;QACA;MACA;IACA;EACA;EACAO;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA5C;MAAA;MACA;MACA;MACA;QACA;MACA;IACA;IACAI;MACAyC;QAAA;QACA;UACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACAC;UACAC;UACAC;QACA;MACA;MACAF;QACA;QACA;QACA;MACA;IACA;IACA;IACAG;MAAA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;;MAEA;MACA;QACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AClSA;AAAA;AAAA;AAAA;AAAksC,CAAgB,wnCAAG,EAAC,C;;;;;;;;;;;ACAttC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "tuniao-ui/components/tn-tabs/tn-tabs.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tn-tabs.vue?vue&type=template&id=77999140&scoped=true&\"\nvar renderjs\nimport script from \"./tn-tabs.vue?vue&type=script&lang=js&\"\nexport * from \"./tn-tabs.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tn-tabs.vue?vue&type=style&index=0&id=77999140&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"77999140\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"tuniao-ui/components/tn-tabs/tn-tabs.vue\"\nexport default component.exports", "export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-tabs.vue?vue&type=template&id=77999140&scoped=true&\"", "var components\ntry {\n  components = {\n    tnBadge: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-badge/tn-badge\" */ \"@/tuniao-ui/components/tn-badge/tn-badge.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.$tn.string.getLengthUnitValue(_vm.top, \"px\")\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var s0 = _vm.__get_style([_vm.tabItemStyle(index)])\n    return {\n      $orig: $orig,\n      s0: s0,\n    }\n  })\n  var s1 = _vm.showBar ? _vm.__get_style([_vm.tabBarStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-tabs.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-tabs.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"tn-tabs-class tn-tabs\" :class=\"[backgroundColorClass]\" :style=\"{backgroundColor: backgroundColorStyle, marginTop: $tn.string.getLengthUnitValue(top, 'px')}\">\r\n    \r\n    <!-- _tgetRect()对组件根节点无效，因为写了.in(this)，故这里获取内层接点尺寸 -->\r\n    <view :id=\"id\">\r\n      <scroll-view scroll-x class=\"tn-tabs__scroll-view\" :scroll-left=\"scrollLeft\" scroll-with-animation>\r\n        <view class=\"tn-tabs__scroll-view__box\" :class=\"{'tn-tabs__scroll-view--flex': !isScroll}\">\r\n          <!-- item -->\r\n          <view\r\n            v-for=\"(item, index) in list\"\r\n            :key=\"index\"\r\n            :id=\"'tn-tabs__scroll-view__item-' + index\"\r\n            class=\"tn-tabs__scroll-view__item tn-text-ellipsis\"\r\n            :style=\"[tabItemStyle(index)]\"\r\n            @tap=\"clickTab(index)\"\r\n          >\r\n            <tn-badge v-if=\"item[count] || item['count']\" backgroundColor=\"tn-bg-red\" fontColor=\"#FFFFFF\" :absolute=\"true\" :top=\"badgeOffset[0] || 0\" :right=\"badgeOffset[1] || 0\">{{ item[count] || item['count']}}</tn-badge>\r\n            {{ item[name] || item['name'] }}\r\n          </view>\r\n          \r\n          <!-- 底部滑块 -->\r\n          <view v-if=\"showBar\" class=\"tn-tabs__bar tn-flex tn-flex-direction-column tn-flex-col-center\" :style=\"[tabBarStyle]\">\r\n\t\t\t  <view class=\"selBar1\"></view>\r\n\t\t\t  <view class=\"selBar2\"></view>\r\n\t\t  </view>\r\n        </view>\r\n      </scroll-view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import componentsColor from '../../libs/mixin/components_color.js'\r\n  export default {\r\n    mixins: [componentsColor],\r\n    name: 'tn-tabs',\r\n    props: {\r\n      // 标签列表\r\n      list: {\r\n        type: Array,\r\n        default() {\r\n          return []\r\n        }\r\n      },\r\n      // 列表数据tab名称的属性\r\n      name: {\r\n        type: String,\r\n        default: 'name'\r\n      },\r\n      // 列表数据微标数量的属性\r\n      count: {\r\n        type: String,\r\n        default: 'count'\r\n      },\r\n      // 当前活动的tab索引\r\n      current: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // 菜单是否可以滑动\r\n      isScroll: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 高度\r\n      height: {\r\n        type: Number,\r\n        default: 80\r\n      },\r\n      // 距离顶部的距离(px)\r\n      top: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // item的宽度\r\n      itemWidth: {\r\n        type: [String, Number],\r\n        default: 'auto'\r\n      },\r\n      // 过渡动画时长\r\n      duration: {\r\n        type: Number,\r\n        default: 0.3\r\n      },\r\n      // 选中时的颜色\r\n      activeColor: {\r\n        type: String,\r\n        default: '#01BEFF'\r\n      },\r\n      // 未被选中时的颜色\r\n      inactiveColor: {\r\n        type: String,\r\n        default: '#080808'\r\n      },\r\n      // 未选中的item样式\r\n      inactiveItemStyle: {\r\n        type: Object,\r\n        default() {\r\n          return {}\r\n        }\r\n      },\r\n      // 选中的item样式\r\n      activeItemStyle: {\r\n        type: Object,\r\n        default() {\r\n          return {}\r\n        }\r\n      },\r\n      // 是否显示底部滑块\r\n      showBar: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      // 底部滑块的宽度\r\n      barWidth: {\r\n        type: Number,\r\n        default: 40\r\n      },\r\n      // 底部滑块的高度\r\n      barHeight: {\r\n        type: Number,\r\n        default: 6\r\n      },\r\n      // 自定义底部滑块的样式\r\n      barStyle: {\r\n        type: Object,\r\n        default() {\r\n          return {}\r\n        }\r\n      },\r\n      // 单个tab的左右内边距\r\n      gutter: {\r\n        type: Number,\r\n        default: 30\r\n      },\r\n      // 微标的偏移数[top, right]\r\n      badgeOffset: {\r\n        type: Array,\r\n        default() {\r\n          return [20, 22]\r\n        }\r\n      },\r\n      // 是否加粗字体\r\n      bold: {\r\n        type: Boolean,\r\n        default: false\r\n      }\r\n    },\r\n    computed: {\r\n      // 底部滑块样式\r\n      tabBarStyle() {\r\n        let style = {\r\n          width: this.$tn.string.getLengthUnitValue(this.barWidth),\r\n          height: this.$tn.string.getLengthUnitValue(12),\r\n          borderRadius: `${this.barHeight / 2}rpx`,\r\n          // backgroundColor: this.activeColor,\r\n          opacity: this.barMoveFirst ? 0 : 1,\r\n          transform: `translate(${this.scrollBarLeft}px, -100%)`,\r\n          transitionDuration: this.barMoveFirst ? '0s' : `${this.duration}s`\r\n        }\r\n        Object.assign(style, this.barStyle)\r\n        return style\r\n      },\r\n      // tabItem样式\r\n      tabItemStyle() {\r\n        return index => {\r\n          let style = {\r\n            width: this.$tn.string.getLengthUnitValue(this.itemWidth),\r\n            height: this.$tn.string.getLengthUnitValue(this.height),\r\n            lineHeight: this.$tn.string.getLengthUnitValue(this.height),\r\n            fontSize: this.fontSizeStyle || '28rpx',\r\n            padding: this.isScroll ? `0 ${this.gutter}rpx` : '',\r\n            flex: this.isScroll ? 'auto' : '1',\r\n            transitionDuration: `${this.duration}s`\r\n          }\r\n          if (index === this.currentIndex) {\r\n            if (this.bold) {\r\n              style.fontWeight = 'bold'\r\n            }\r\n            style.color = this.activeColor\r\n            Object.assign(style, this.activeItemStyle)\r\n          } else {\r\n            style.color = this.inactiveColor\r\n            Object.assign(style, this.inactiveItemStyle)\r\n          }\r\n          return style\r\n        }\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        // id值\r\n        id: this.$tn.uuid(),\r\n        // 滚动scroll-view的左边距离\r\n        scrollLeft: 0,\r\n        // 存放查询后tab菜单的节点信息\r\n        tabQueryInfo: [],\r\n        // 组件宽度\r\n        componentWidth: 0,\r\n        // 底部滑块的移动距离\r\n        scrollBarLeft: 0,\r\n        // 组件到屏幕左边的巨鹿\r\n        componentLeft: 0,\r\n        // 当前选中的itemIndex\r\n        currentIndex: this.current,\r\n        // 标记底部滑块是否第一次移动，第一次移动的时候不触发动画\r\n        barMoveFirst: true\r\n      }\r\n    },\r\n    watch: {\r\n      // 监听tab的变化，重新计算tab菜单信息\r\n      list(newValue, oldValue) {\r\n        // list变化时，重置内部索引，防止出现超过数据边界的问题\r\n        if (newValue.length !== oldValue.length) this.currentIndex = 0\r\n        this.$nextTick(() => {\r\n          this.init()\r\n        })\r\n      },\r\n      current: {\r\n        handler(val) {\r\n          this.$nextTick(() => {\r\n            this.currentIndex = val\r\n            this.scrollByIndex()\r\n          })\r\n        },\r\n        immediate: true\r\n      }\r\n    },\r\n    mounted() {\r\n      this.init()\r\n    },\r\n    methods: {\r\n      // 初始化变量\r\n      async init() {\r\n        // 获取tabs组件的信息\r\n        let tabRect = await this._tGetRect('#' + this.id)\r\n        // 计算组件的宽度\r\n        this.componentLeft = tabRect.left\r\n        this.componentWidth = tabRect.width\r\n        this.getTabRect()\r\n      },\r\n      // 点击tab菜单\r\n      clickTab(index) {\r\n        if (index === this.currentIndex) return\r\n        this.$emit('change', index)\r\n      },\r\n      // 查询tab的布局信息\r\n      getTabRect() {\r\n        let query = uni.createSelectorQuery().in(this)\r\n        // 遍历所有的tab\r\n        for (let i = 0; i < this.list.length; i++) {\r\n          query.select(`#tn-tabs__scroll-view__item-${i}`).fields({\r\n            size: true,\r\n            rect: true\r\n          })\r\n        }\r\n        query.exec((res) => {\r\n          this.tabQueryInfo = res\r\n          // 初始滚动条和底部滑块的位置\r\n          this.scrollByIndex()\r\n        })\r\n      },\r\n      // 滚动scrollView，让活动的tab处于屏幕中间\r\n      scrollByIndex() {\r\n        // 当前获取tab的布局信息\r\n        let tabInfo = this.tabQueryInfo[this.currentIndex]\r\n        if (!tabInfo) return\r\n        \r\n        // 活动tab的宽度\r\n        let tabWidth = tabInfo.width\r\n        // 活动item的左边到组件左边的距离\r\n        let offsetLeft = tabInfo.left - this.componentLeft\r\n        // 计算scroll-view移动的距离\r\n        let scrollLeft = offsetLeft - (this.componentWidth - tabWidth) / 2\r\n        this.scrollLeft = scrollLeft < 0 ? 0 : scrollLeft\r\n        \r\n        // 计算当前滑块需要移动的距离，当前活动item的中点到左边的距离减去滑块宽度的一半\r\n        let left = tabInfo.left + tabInfo.width / 2 - this.componentLeft\r\n        \r\n        // 计算当前活跃item到组件左边的距离\r\n        this.scrollBarLeft = left - uni.upx2px(this.barWidth) / 2\r\n        \r\n        // 防止在计算时出错，所以延迟执行标记不是第一次移动\r\n        if (this.barMoveFirst) {\r\n          setTimeout(() => {\r\n            this.barMoveFirst = false\r\n          }, 100)\r\n        }\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.selBar1{\r\n\t\twidth: 26rpx;\r\n\t\theight: 6rpx;\r\n\t\tborder-radius: 2rpx;\r\n\t\tbackground-color: #5552FF;\r\n\t}\r\n\t.selBar2{\r\n\t\twidth: 26rpx;\r\n\t\theight: 6rpx;\r\n\t\tborder-radius: 2rpx;\r\n\t\tbackground-color: #FF000A;\r\n\t}\r\n  \r\n  /* #ifndef APP-NVUE */\r\n  ::-webkit-scrollbar {\r\n    display: none;\r\n    width: 0 !important;\r\n    height: 0 !important;\r\n    -webkit-appearance: none;\r\n    background: transparent;\r\n  }\r\n  /* #endif */\r\n  \r\n  /* #ifdef H5 */\r\n  // 通过样式穿透，隐藏H5下，scroll-view下的滚动条\r\n  scroll-view ::v-deep ::-webkit-scrollbar {\r\n  \tdisplay: none;\r\n  \twidth: 0 !important;\r\n  \theight: 0 !important;\r\n  \t-webkit-appearance: none;\r\n  \tbackground: transparent;\r\n  }\r\n  /* #endif */\r\n  \r\n  .tn-tabs {\r\n    &__scroll-view {\r\n      position: relative;\r\n      width: 100%;\r\n      white-space: nowrap;\r\n      \r\n      &__box {\r\n        position: relative;\r\n        /* #ifdef MP-TOUTIAO */\r\n        white-space: nowrap;\r\n        /* #endif */\r\n      }\r\n      \r\n      &__item {\r\n        position: relative;\r\n        /* #ifndef APP-NVUE */\r\n        display: inline-block;\r\n        /* #endif */\r\n        text-align: center;\r\n        transition-property: background-color, color;\r\n      }\r\n      \r\n      &--flex {\r\n        display: flex;\r\n        flex-direction: row;\r\n        justify-content: space-between;\r\n      }\r\n    }\r\n    \r\n    &__bar {\r\n      position: absolute;\r\n      bottom: 0;\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-tabs.vue?vue&type=style&index=0&id=77999140&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tn-tabs.vue?vue&type=style&index=0&id=77999140&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980404819\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}