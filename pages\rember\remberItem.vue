<template>
	<view>
		<tn-tabs :list="temList" :isScroll="false" activeColor="#222222" inactiveColor="#666666" :current="current"
			name="template_name" @change="change"></tn-tabs>
		<view class="body">
			<view v-for="(item,index) in reciteChapterList" :key="index" class="body_item">
				<rember-dot :item="item" :index="index" :selIndex="selIndex" @showFold="showFold"
					@noShowFold="noShowFold" @toRember="toRember"></rember-dot>
			</view>
		</view>
	</view>
</template>

<script>
	import remberDot from "@/components/rember_dot.vue"
	import shareMixin from "@/mixins/shareMixin.js"
	export default {
		mixins: [shareMixin],
		components: {
			remberDot
		},
		data() {
			return {
				selIndex: -1,
				temList: [],
				current: 0,
				book_id: null,
				reciteChapterList: [],
			}
		},
		onLoad(options) {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			if (options.book_id) {
				this.book_id = options.book_id
				this.getTem()
			}
		},

		methods: {
			refData() {
				this.getTem()
			},
			showFold(item) {
				this.selIndex = item.id
			},
			noShowFold(item) {
				this.selIndex = -1
			},
			getTem() {
				this.$http.post(this.$api.reciteTemList, {
					book_id: this.book_id
				}).then(res => {
					if (res.code == 200) {
						this.temList = res.data
						if (this.temList.length > 0) {
							this.getDataList()
						} else {
							this.getDataList()
						}
					}
				})
			},
			getDataList() {
				let tid = ""
				if (this.temList.length > 0) {
					tid = this.temList[this.current].template_id || 0
				} else {
					tid = 0
				}
				this.$http.post(this.$api.reciteChapterList, {
					book_id: this.book_id,
					template_id: tid
				}).then(res => {
					if (res.code == 200) {
						this.reciteChapterList = res.data
					}
				})
			},
			change(index) {
				if (this.current != index) {
					this.current = index;
					this.selIndex = -1
					this.getDataList()
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.body_item {
		margin-bottom: 20rpx;
	}

	.body {
		width: 100%;
		padding: 20rpx 30rpx;
		box-sizing: border-box;
	}
</style>