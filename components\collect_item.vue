<template>
	<view class="exercise_item tn-flex tn-flex-direction-column tn-flex-row-between" @click.stop="toQuest()">
		<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between">
			<view class="tn-text-ellipsis tn-text-bold" style="width: 520rpx;font-size: 30rpx;">
				{{item.chapter_name}}
			</view>
		</view>
		<view class="tn-width-full tn-flex tn-flex-col-center tn-flex-row-between"
			style="font-size: 28rpx;color: #666666;">
			<view class="tn-text-ellipsis">
				收藏{{item.topic_num||0}}题
			</view>
			<view class="tn-icon-right"></view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "exercise_item",
		props: {
			item: {
				type: Object,
				default: () => {}
			},
		},
		data() {
			return {

			};
		},
		methods: {
			toQuest() {
				this.$emit("toQuest", this.item)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.his_btn {
		margin-left: 32rpx;
		width: 142rpx;
		height: 35rpx;
		border-radius: 8rpx;
		background-color: #FFEEEF;
		color: #FF000A;
		font-size: 20rpx;
	}

	.exercise_item {
		width: 690rpx;
		height: 200rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		padding: 30rpx;
		box-sizing: border-box;
	}
</style>