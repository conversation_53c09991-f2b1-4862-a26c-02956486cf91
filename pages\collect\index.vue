<template>
	<view>
		<view class="top_model tn-width-full">
			<view class="tn-width-full tn-flex tn-flex-row-between tn-flex-col-center">
				<view class="exercise_info tn-height-full tn-flex tn-flex-direction-column tn-flex-row-between">
					<view class="name tn-text-ellipsis-2" style="width: 380rpx;">
						{{name}}
					</view>
				</view>
				<view class="subsect">
					<tn-subsection :list="list" :height="72" inactiveColor="#5552FF" buttonColor="#5552FF"
						:current="current" @change="changeSub"></tn-subsection>
				</view>
			</view>
			<view class="tn-width-full" style="margin-top: 30rpx;">
				<scroll-view scroll-x="true" class="scroll-view-x">
					<view v-for="(item,index) in list_type" :key="index" class="scroll-view-item"
						:class="item.template_id==templateId?'sel_tem':''" @click.stop="changeTem(item)">
						{{item.template_name}}
					</view>
				</scroll-view>
			</view>
		</view>
		<view class="body">
			<view v-for="(item,index) in collectList" :key="index" class="tn-width-full" style="margin-bottom: 20rpx;">
				<collect-item :item="item" @toQuest="toQuest"></collect-item>
			</view>
		</view>
	</view>
</template>

<script>
	import collectItem from "@/components/collect_item.vue"
	import shareMixin from "@/mixins/shareMixin.js"
	export default {
		mixins: [shareMixin],
		components: {
			collectItem
		},
		data() {
			return {
				list: ['刷题模式', '背诵模式'],
				bookId: null,
				templateId: 0,
				name: null,
				list_type: [],
				collectList: [],
				current: 0
			};
		},
		onLoad(options) {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
			if (options.book_id) {
				this.bookId = options.book_id
				this.name = options.name
				this.getBookTem()
			}
		},

		methods: {
			changeSub(e) {
				this.current = e.index
			},
			toQuest(item) {
				// if (this.current == 0) {
				// 	uni.navigateTo({
				// 		url: "/pages/collect/quest?id=" + item.chapter_id
				// 	})
				// } else {
					uni.navigateTo({
						url: "/pages/collect/quest_rem?id=" + item.chapter_id
					})
				// }
			},
			changeTem(item) {
				if (this.templateId != item.template_id) {
					this.templateId = item.template_id
					this.collectList = []
					this.getCollectList()
				}
			},
			getBookTem() { //获取书籍模板
				this.$http.post(this.$api.bookTem, {
					book_id: this.bookId
				}).then(res => {
					if (res.code == 200) {
						this.list_type = res.data
						if (res.data.length > 0) {
							this.templateId = res.data[0].template_id
						}
						this.getCollectList()
					}
				})
			},
			getCollectList() {
				this.$http.post(this.$api.bookCollect, {
					book_id: this.bookId,
					template_id: this.templateId
				}).then(res => {
					if (res.code == 200) {
						this.collectList = res.data
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.sel_tem {
		background-color: #E3E3FF !important;
		color: #5552FF !important;
	}

	.body {
		width: 100%;
		padding: 20rpx 30rpx;
		box-sizing: border-box;
	}

	.scroll-view-item {
		display: inline-block;
		min-width: 128rpx;
		height: 60rpx;
		border-radius: 30rpx;
		background-color: #F7F7F7;
		padding: 0rpx 20rpx;
		box-sizing: border-box;
		text-align: center;
		line-height: 60rpx;
		margin-right: 10rpx;
		color: #666666;
		font-size: 28rpx;
	}

	.scroll-view-x {
		white-space: nowrap;
		width: 100%;
	}

	.name {
		font-size: 30rpx;
		font-weight: bold;
		color: #222222;
	}

	.time {
		font-size: 20rpx;
		color: #666666;
	}

	/deep/ .tn-subsection__item--text {
		font-size: 24rpx !important;
	}

	.subsect {
		width: 272rpx;
	}

	.top_model {
		width: 750rpx;
		background-color: #FFFFFF;
		padding: 30rpx;
		box-sizing: border-box;
		border-radius: 0rpx 0rpx 20rpx 20rpx;
	}
</style>