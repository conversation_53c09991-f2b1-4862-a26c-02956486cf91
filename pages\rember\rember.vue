<template>
	<view class="content">
		<view class="vip_warp tn-flex tn-flex-col-center tn-flex-row-between" @click.stop="toWeidian()"
			v-if="userInfo.recite_status != 1">
			<view class="vw_left tn-height-full tn-flex tn-flex-direction-column tn-flex-row-between">
				<view class="vw_top tn-flex tn-flex-col-center">
					<view class="vwt_word">
						背诵会员
					</view>
					<image src="../../static/icon/vip.png" mode="widthFix" style="width: 32rpx;height: 32rpx;"></image>
					<view class="vwt_tip">
						认证会员
					</view>
				</view>
				<view class="vw_bottom">
					终极冲刺“速记＋检测”，时间越紧越有用
				</view>
			</view>
			<view class="vw_right">
				认证会员
			</view>
		</view>
		<view class="list_body">
			<view v-for="(item,index) in reciteList" :key="index" style="margin-bottom: 20rpx;">
				<rember-item :item="item" :index="index" :selIndex="selIndex" @showFold="selIndex=index"
					@noShowFold="selIndex=-1" @toRember="toRember"></rember-item>
			</view>
		</view>
		<image src="../../static/icon/zixun.png" mode="widthFix" class="fab_btn" @click.stop="showKefu=true"></image>

		<tn-popup v-model="showKefu" mode="bottom" :borderRadius="40">
			<view class="tn-width-full tn-flex tn-flex-col-center tn-text-bold"
				style="width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;">
				客服帮助</view>
			<view class="tn-width-full" style="">
				<view class="tn-flex tn-flex-row-center"
					style="padding: 30rpx 46rpx 66rpx 46rpx;box-sizing: border-box;">
					<view style="width: 400rpx;height: 400rpx;">
						<image show-menu-by-longpress style="width: 400rpx;height: 400rpx;" :src="baseUrl+service_img"
							mode=""></image>
					</view>
				</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	import remberItem from "@/components/rember_item.vue"
	import shareMixin from "@/mixins/shareMixin.js"
	export default {
		mixins: [shareMixin],
		components: {
			remberItem
		},
		data() {
			return {
				baseUrl: this.$config.baseUrl,
				selIndex: -1,
				showKefu: false,
				reciteList: [],
				service_img: "",
				userInfo: {}
			}
		},
		onLoad() {
			// #ifdef MP-WEIXIN
			wx.showShareMenu({ // 微信小程序分享
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			// #endif
		},

		onShow() {
			this.getReciteList()
			this.getsysconfig()
			this.userInfo = uni.getStorageSync('userinfo');
		},
		methods: {
			toWeidian() {
				let url = uni.getStorageSync('systemData').config.user_jump_link
				wx.navigateToMiniProgram({
					shortLink: url,
					fail: (err) => {
						uni.showToast({
							title: err,
							icon: "none"
						})
					}
				})
			},
			getsysconfig() { // 获取系统配置
				this.$http.post(this.$api.systemData, {}).then(res => {
					if (res.code == 200) {
						let datas = res.data;
						this.service_img = datas.config.service_img
						uni.setStorageSync('systemData', datas)
					}
				})
			},
			toRember(it) {
				this.$publicjs.toUrl('/pages/rember/remberItem?book_id=' + it.id)
			},
			getReciteList() {
				this.$http.post(this.$api.reciteList).then(res => {
					if (res.code == 200) {
						this.reciteList = res.data
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.fab_btn {
		width: 154rpx;
		position: fixed;
		right: 0rpx;
		bottom: 150rpx;
	}

	.list_body {
		margin-top: 20rpx;
	}

	.vw_right {
		width: 150rpx;
		height: 56rpx;
		border-radius: 50rpx;
		background-color: #FFF6F0;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #A45505;
		font-size: 24rpx;
	}

	.vw_bottom {
		font-size: 20rpx;
		color: #C4690D;
	}

	.vwt_tip {
		width: 110rpx;
		height: 34rpx;
		border-radius: 8rpx;
		background: linear-gradient(180deg, #FF9D4B 0%, #FFB200 100%);
		font-size: 24rpx;
		color: #FFFFFF;
		text-align: center;
		line-height: 36rpx;
		margin-left: 12rpx;
	}

	.vwt_word {
		font-size: 28rpx;
		font-weight: bold;
		color: #A45505;
		margin-right: 4rpx;
	}

	.vip_warp {
		width: 690rpx;
		height: 130rpx;
		background: url("../../static/rember_vip_bg.png") no-repeat;
		background-size: 100% 100%;
		padding: 20rpx 48rpx 28rpx 34rpx;
		box-sizing: border-box;
	}

	.content {
		width: 100%;
		padding: 20rpx 30rpx 30rpx 30rpx;
		box-sizing: border-box;
	}
</style>