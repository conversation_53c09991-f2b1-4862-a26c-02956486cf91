@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.tn-swiper__wrap.data-v-4ba19b58 {
  position: relative;
  overflow: hidden;
  -webkit-transform: translateY(0);
          transform: translateY(0);
}
.tn-swiper__item.data-v-4ba19b58 {
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;
}
.tn-swiper__item__image.data-v-4ba19b58 {
  width: 100%;
  height: 100%;
  will-change: transform;
  display: block;
}
.tn-swiper__item__image__wrap.data-v-4ba19b58 {
  width: 100%;
  height: 100%;
  flex: 1;
  transition: all 0.5s;
  overflow: hidden;
  box-sizing: content-box;
  position: relative;
}
.tn-swiper__item__image--scale.data-v-4ba19b58 {
  -webkit-transform-origin: center center;
          transform-origin: center center;
}
.tn-swiper__item__title.data-v-4ba19b58 {
  width: 100%;
  position: absolute;
  background-color: rgba(0, 0, 0, 0.3);
  bottom: 0;
  left: 0;
  font-size: 28rpx;
  padding: 12rpx 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.tn-swiper__indicator.data-v-4ba19b58 {
  padding: 0 24rpx;
  position: absolute;
  display: flex;
  flex-direction: row;
  width: 100%;
  z-index: 1;
}
.tn-swiper__indicator__rect.data-v-4ba19b58 {
  width: 26rpx;
  height: 8rpx;
  background-color: rgba(0, 0, 0, 0.3);
  transition: all 0.5s;
}
.tn-swiper__indicator__rect--active.data-v-4ba19b58 {
  background-color: rgba(255, 255, 255, 0.8);
}
.tn-swiper__indicator__dot.data-v-4ba19b58 {
  width: 14rpx;
  height: 14rpx;
  margin: 0 6rpx;
  border-radius: 20rpx;
  background-color: rgba(0, 0, 0, 0.3);
  transition: all 0.5s;
}
.tn-swiper__indicator__dot--active.data-v-4ba19b58 {
  background-color: rgba(255, 255, 255, 0.8);
}
.tn-swiper__indicator__round.data-v-4ba19b58 {
  width: 14rpx;
  height: 14rpx;
  margin: 0 6rpx;
  border-radius: 20rpx;
  background-color: rgba(0, 0, 0, 0.3);
  transition: all 0.5s;
}
.tn-swiper__indicator__round--active.data-v-4ba19b58 {
  width: 34rpx;
  background-color: rgba(255, 255, 255, 0.8);
}
.tn-swiper__indicator__number.data-v-4ba19b58 {
  padding: 6rpx 16rpx;
  line-height: 1;
  background-color: rgba(0, 0, 0, 0.3);
  color: rgba(255, 255, 255, 0.8);
  border-radius: 100rpx;
  font-size: 26rpx;
}

