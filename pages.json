{
	"easycom": {
		"^tn-(.*)": "@/tuniao-ui/components/tn-$1/tn-$1.vue"
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/quest/quest",
			"style": {
				"navigationBarTitleText": "首页",
				"enablePullDownRefresh": true
			}
		}, {
			"path": "pages/fullscreen/fullscreen",
			"style": {
				"navigationStyle": "custom", // 取消本页面的导航栏
				"app-plus": {
					"animationType": "fade-in", // 设置fade-in淡入动画，为最合理的动画类型
					"background": "transparent", // 背景透明
					"backgroundColor": "rgba(0,0,0,0)", // 背景透明
					"popGesture": "none" // 关闭IOS屏幕左边滑动关闭当前页面的功能
				}
			}
		},
		{
			"path": "pages/check/check",
			"style": {
				"navigationBarTitleText": "发现",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/rember/rember",
			"style": {
				"navigationBarTitleText": "背诵",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/mine/mine",
			"style": {
				"navigationBarTitleText": "我的"
			}
		},
		{
			"path": "pages/mine/code",
			"style": {
				"navigationBarTitleText": "刷题",
				"navigationBarBackgroundColor": "#ffffff",
				"backgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/rember/remberItem",
			"style": {
				"navigationBarTitleText": "考点列表",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/rember/recitation",
			"style": {
				"navigationBarTitleText": "背诵",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/error_quest/index",
			"style": {
				"navigationBarTitleText": "刷题",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/error_quest/quest",
			"style": {
				"navigationBarTitleText": "题目",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/exercise/index",
			"style": {
				"navigationBarTitleText": "刷题",
				"navigationBarBackgroundColor": "#ffffff",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/exercise/quest",
			"style": {
				"navigationBarTitleText": "试卷",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/exercise/exercise_end",
			"style": {
				"navigationBarTitleText": "做题成绩",
				"navigationBarBackgroundColor": "#FFE8E4"
			}
		},
		{
			"path": "pages/exercise/check_index",
			"style": {
				"navigationBarTitleText": "答案速查",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/note/index",
			"style": {
				"navigationBarTitleText": "错题笔记",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/exercise/quest_rem",
			"style": {
				"navigationBarTitleText": "试卷",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/note/inner",
			"style": {
				"navigationBarTitleText": "笔记",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/note/quest",
			"style": {
				"navigationBarTitleText": "笔记",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/collect/index",
			"style": {
				"navigationBarTitleText": "收藏",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/collect/quest",
			"style": {
				"navigationBarTitleText": "试卷",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/collect/quest_rem",
			"style": {
				"navigationBarTitleText": "试卷",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/error_quest/quest_o",
			"style": {
				"navigationBarTitleText": "刷题",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/error_quest/quest_rem",
			"style": {
				"navigationBarTitleText": "刷题",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/exercise/exercise_res",
			"style": {
				"navigationBarTitleText": "答题记录",
				"navigationBarBackgroundColor": "#FFE8E4"
			}
		},
		{
			"path": "pages/analysis/index",
			"style": {
				"navigationBarTitleText": "题目解析",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/analysis/error",
			"style": {
				"navigationBarTitleText": "错题解析",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/rember/norecitation",
			"style": {
				"navigationBarTitleText": "背诵",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/quest/addQuest",
			"style": {
				"navigationBarTitleText": "刷题",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/mine/share",
			"style": {
				"navigationBarTitleText": "解锁",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/error_quest/quest_end",
			"style": {
				"navigationBarTitleText": "做题成绩",
				"navigationBarBackgroundColor": "#FFE8E4"
			}
		},
		{
			"path": "pages/error_quest/quest_rem_end",
			"style": {
				"navigationBarTitleText": "做题成绩",
				"navigationBarBackgroundColor": "#FFE8E4"
			}
		},
		{
			"path": "pages/error_quest/analysis/index",
			"style": {
				"navigationBarTitleText": "题目解析",
				"navigationBarBackgroundColor": "#ffffff"
			}
		},
		{
			"path": "pages/error_quest/analysis/error",
			"style": {
				"navigationBarTitleText": "错题解析",
				"navigationBarBackgroundColor": "#ffffff"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#f0f0ff",
		"backgroundColor": "#F7F7F7"
	},
	"tabBar": {
		"borderStyle": "white",
		"selectedColor": "#5552FF",
		"backgroundColor": "#FFFFFF",
		"color": "#979797",
		"list": [{
			"pagePath": "pages/quest/quest",
			"iconPath": "static/tabbar/quest.png",
			"selectedIconPath": "static/tabbar/quest_sel.png",
			"text": "刷题"
		}, {
			"pagePath": "pages/rember/rember",
			"iconPath": "static/tabbar/rember.png",
			"selectedIconPath": "static/tabbar/rember_sel.png",
			"text": "背诵"
		}, {
			"pagePath": "pages/check/check",
			"iconPath": "static/tabbar/check.png",
			"selectedIconPath": "static/tabbar/check_sel.png",
			"text": "发现"
		}, {
			"pagePath": "pages/mine/mine",
			"iconPath": "static/tabbar/mine.png",
			"selectedIconPath": "static/tabbar/mine_sel.png",
			"text": "我的"
		}]
	},
	"uniIdRouter": {}
}