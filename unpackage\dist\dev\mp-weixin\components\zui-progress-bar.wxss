@charset "UTF-8";
/* 让图片更清晰，仅限image标签 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.zui-progress-bar.data-v-beacc0e6 {
  position: relative;
  width: 100%;
  margin: 4px 0;
}
.zui-progress-bar-wrapper.data-v-beacc0e6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.zui-progress-bar-bar.data-v-beacc0e6 {
  width: 100%;
  background-color: #ffffff;
  padding: 3rpx 2rpx 2rpx 2rpx;
  box-sizing: border-box;
}
.zui-progress-bar-inside.data-v-beacc0e6 {
  height: 100%;
  width: var(--zui-progress-bar-value, 0);
  background: var(--zui-progress-bar-fg, --zui-progress-bar-fg-def);
}
.zui-progress-bar-bar.data-v-beacc0e6,
.zui-progress-bar-inside.data-v-beacc0e6,
.zui-progress-bar-value.data-v-beacc0e6 {
  transition: all 0.1s ease-in-out;
  background-size: auto 100%;
}
.zui-progress-bar-value.data-v-beacc0e6 {
  color: var(--zui-progress-bar-color);
  mix-blend-mode: var(--zui-progress-bar-invert, none);
}
.rounded .zui-progress-bar-bar.data-v-beacc0e6,
.rounded .zui-progress-bar-inside.data-v-beacc0e6 {
  border-radius: var(--zui-progress-bar-radius, 0);
  overflow: hidden;
}
.left .zui-progress-bar-value.data-v-beacc0e6,
.inside-left .zui-progress-bar-value.data-v-beacc0e6 {
  position: absolute;
  padding: 0 4px;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.center .zui-progress-bar-value.data-v-beacc0e6,
.inside-center .zui-progress-bar-value.data-v-beacc0e6 {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.right .zui-progress-bar-value.data-v-beacc0e6,
.inside-right .zui-progress-bar-value.data-v-beacc0e6 {
  position: absolute;
  padding: 0 4px;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}
.outside-left.data-v-beacc0e6 {
  flex-direction: row-reverse;
}
.outside-left .zui-progress-bar-value.data-v-beacc0e6 {
  margin-right: 4px;
}
.outside-right .zui-progress-bar-value.data-v-beacc0e6 {
  margin-left: 4px;
}
.follow-left .zui-progress-bar-value.data-v-beacc0e6 {
  position: absolute;
  top: 50%;
  left: var(--zui-progress-bar-value-fixed);
  -webkit-transform: translate(-100%, -50%);
          transform: translate(-100%, -50%);
  padding: 0 4px;
}
.follow-right .zui-progress-bar-value.data-v-beacc0e6 {
  position: absolute;
  top: 50%;
  left: var(--zui-progress-bar-value-fixed);
  -webkit-transform: translate(0, -50%);
          transform: translate(0, -50%);
  padding: 0 4px;
}

