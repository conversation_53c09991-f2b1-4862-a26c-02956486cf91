{"version": 3, "sources": ["webpack:///D:/project/shuati_new/components/quest/quest_btn.vue?7302", "webpack:///D:/project/shuati_new/components/quest/quest_btn.vue?17cc", "webpack:///D:/project/shuati_new/components/quest/quest_btn.vue?c793", "webpack:///D:/project/shuati_new/components/quest/quest_btn.vue?0f02", "uni-app:///components/quest/quest_btn.vue"], "names": ["name", "props", "type", "default", "data", "methods", "toColl", "toNote", "to<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;;;AAGxD;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAsmB,CAAgB,mnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4B1nB;EACAA;EACAC;IACAC;MACAA;MACAC;IACA;EACA;EACAC;IACA,QAEA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B", "file": "components/quest/quest_btn.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./quest_btn.vue?vue&type=template&id=aa0509b2&\"\nvar renderjs\nimport script from \"./quest_btn.vue?vue&type=script&lang=js&\"\nexport * from \"./quest_btn.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/quest/quest_btn.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quest_btn.vue?vue&type=template&id=aa0509b2&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quest_btn.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./quest_btn.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"tn-height-full tn-width-full tn-flex tn-flex-row-between tn-flex-col-center\"\r\n\t\tstyle=\"padding: 0rpx 50rpx;\">\r\n\t\t<view class=\"tn-flex tn-flex-direction-column tn-flex-col-center\" style=\"min-width: 100rpx;\"\r\n\t\t\************=\"toNote\">\r\n\t\t\t<image src=\"../../static/icon/note.png\" mode=\"widthFix\" style=\"width: 62rpx;\"></image>\r\n\t\t\t<view style=\"color: #333333;font-size: 28rpx;\">\r\n\t\t\t\t笔记\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"tn-flex tn-flex-direction-column tn-flex-col-center\" style=\"min-width: 100rpx;\"\r\n\t\t\************=\"toColl()\">\r\n\t\t\t<image src=\"../../static/icon/collect.png\" mode=\"widthFix\" style=\"width: 62rpx;\"></image>\r\n\t\t\t<view style=\"color: #333333;font-size: 28rpx;\">\r\n\t\t\t\t我的收藏\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"tn-flex tn-flex-direction-column tn-flex-col-center\" style=\"min-width: 100rpx;\" v-if=\"type!=3\"\r\n\t\t\************=\"toCheck()\">\r\n\t\t\t<image src=\"../../static/icon/answer.png\" mode=\"widthFix\" style=\"width: 62rpx;\"></image>\r\n\t\t\t<view style=\"color: #333333;font-size: 28rpx;\">\r\n\t\t\t\t答案速查\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: \"quest_btn\",\r\n\t\tprops: {\r\n\t\t\ttype: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 1\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttoColl() {\r\n\t\t\t\tthis.$emit(\"toColl\")\r\n\t\t\t},\r\n\t\t\ttoNote() {\r\n\t\t\t\tthis.$emit(\"toNote\")\r\n\t\t\t},\r\n\t\t\ttoCheck() {\r\n\t\t\t\tthis.$emit(\"toCheck\")\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\r\n</style>"], "sourceRoot": ""}