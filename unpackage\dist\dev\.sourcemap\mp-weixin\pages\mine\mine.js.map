{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/project/shuati_new/pages/mine/mine.vue?a51e", "webpack:///D:/project/shuati_new/pages/mine/mine.vue?6d8d", "webpack:///D:/project/shuati_new/pages/mine/mine.vue?f46d", "webpack:///D:/project/shuati_new/pages/mine/mine.vue?b713", "uni-app:///pages/mine/mine.vue", "webpack:///D:/project/shuati_new/pages/mine/mine.vue?f7f3", "webpack:///D:/project/shuati_new/pages/mine/mine.vue?49a0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "data", "baseUrl", "showKefu", "showUser", "nick", "avatar", "userForm", "service_img", "share_back", "userInfo", "onShow", "onLoad", "withShareTicket", "menus", "methods", "toShare", "uni", "url", "toCode", "<PERSON><PERSON><PERSON><PERSON>", "submitUser", "onChooseAvatar", "filePath", "name", "header", "success", "fail", "console", "getUserInfo", "getsysconfig", "updateManagerfun", "updateManager", "content", "showCancel", "confirmText"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,kRAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvDA;AAAA;AAAA;AAAA;AAAimB,CAAgB,8mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkIrnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAF;QACAC;MACA;MACAE;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IAEAjB;MAAA;MACAkB;MACAC;IACA;EAEA;EAEAC;IACAC;MACA;QACAC;UACAC;QACA;MACA;QACAD;UACAC;QACA;MACA;IACA;IACAC;MACA;QACAF;UACAC;QACA;MACA;QACAD;UACAC;QACA;MACA;IACA;IACAE;MACA;IACA;IACAC;MAAA;MACA;QACAf;QACAD;MACA;QACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAiB;MAAA;MACAL;QACAC;QACAK;QACAC;QACAC;UACA;QACA;QACAC;UACA;QACA;QACAC;UACAC;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA;UACA;YACA;UACA;UACAZ;QACA;MACA;IACA;IACAa;MAAA;MAAA;MACA;QACA;UACA;UACA;UACA;UACAb;QACA;MACA;IACA;IACAc;MAAA;MAAA;MACA;MACAC;QACA;UACAf;YACAgB;YACAC;YACAC;YACAT;cACA;gBACAM;kBACAA;gBACA;gBACAA;kBAAA;kBACA;kBACAf;oBACAgB;oBACAC;oBACAC;kBACA;gBACA;cACA;YACA;UACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7QA;AAAA;AAAA;AAAA;AAAoqC,CAAgB,qnCAAG,EAAC,C;;;;;;;;;;;ACAxrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mine/mine.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mine/mine.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mine.vue?vue&type=template&id=dcbcfe34&scoped=true&\"\nvar renderjs\nimport script from \"./mine.vue?vue&type=script&lang=js&\"\nexport * from \"./mine.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mine.vue?vue&type=style&index=0&id=dcbcfe34&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"dcbcfe34\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mine/mine.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=template&id=dcbcfe34&scoped=true&\"", "var components\ntry {\n  components = {\n    tnAvatar: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-avatar/tn-avatar\" */ \"@/tuniao-ui/components/tn-avatar/tn-avatar.vue\"\n      )\n    },\n    tnPopup: function () {\n      return import(\n        /* webpackChunkName: \"tuniao-ui/components/tn-popup/tn-popup\" */ \"@/tuniao-ui/components/tn-popup/tn-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      _vm.showUser = true\n    }\n    _vm.e1 = function ($event) {\n      return _vm.$publicjs.goMinifun()\n    }\n    _vm.e2 = function ($event) {\n      $event.stopPropagation()\n      _vm.showKefu = true\n    }\n    _vm.e3 = function ($event) {\n      $event.stopPropagation()\n      _vm.showUser = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"\">\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-flex-row-between\">\r\n\t\t\t\t<view class=\"tn-flex tn-flex-col-center\">\r\n\t\t\t\t\t<tn-avatar size=\"xl\" shape=\"circle\" :src=\"userForm.avatar\"></tn-avatar>\r\n\t\t\t\t\t<view class=\"name_warp\">\r\n\t\t\t\t\t\t<view class=\"name tn-text-bold tn-text-ellipsis\">\r\n\t\t\t\t\t\t\t{{ userForm.nick }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"tn-flex tn-flex-col-center\" style=\"margin-top: 10rpx;\">\r\n\t\t\t\t\t\t\t<view class=\"star_l tn-flex tn-flex-col-center\" v-if=\"userInfo.exam_status==1\">\r\n\t\t\t\t\t\t\t\t<image src=\"../../static/icon/st_vip.png\" mode=\"widthFix\" style=\"width: 130rpx;\">\r\n\t\t\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view style=\"width: 10rpx;\"></view>\r\n\t\t\t\t\t\t\t<view class=\"star_r tn-flex tn-flex-col-center\" v-if=\"userInfo.recite_status==1\">\r\n\t\t\t\t\t\t\t\t<image src=\"../../static/icon/bs_vip.png\" mode=\"widthFix\" style=\"width: 130rpx;\">\r\n\t\t\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"set_btn tn-flex tn-flex-col-center tn-flex-row-center\" @click.stop=\"showUser=true\">\r\n\t\t\t\t\t设置个性化头像昵称\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"vip_warp tn-flex tn-flex-row-between\">\r\n\t\t\t\t<view class=\"vip_inner vi_l\" @click.stop=\"toShare()\">\r\n\t\t\t\t\t<view class=\"vi_t tn-flex tn-flex-col-center\">\r\n\t\t\t\t\t\t刷题会员<view class=\"tips\">限时免费</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"vi_time\">\r\n\t\t\t\t\t\t<text v-if=\"userInfo.exam_status != 0\">有效期至{{ userInfo.exam_valid_time }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"vi_btn vib_l\">\r\n\t\t\t\t\t\t免费解锁\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"vip_inner vi_r\" @click.stop=\"toCode()\">\r\n\t\t\t\t\t<view class=\"vi_t\">\r\n\t\t\t\t\t\t背诵＋刷题会员\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"vi_time\">\r\n\t\t\t\t\t\t<text v-if=\"userInfo.recite_status != 0\">有效期至{{ userInfo.recite_valid_time }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"vi_btn vib_r\">\r\n\t\t\t\t\t\t立即认证\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"body\">\r\n\t\t\t<view class=\"poster tn-flex tn-flex-col-center tn-flex-row-between\" @click=\"$publicjs.goMinifun()\">\r\n\t\t\t\t<image class=\"posterbg\" :src=\"baseUrl+share_back\" mode=\"\"></image>\r\n\t\t\t\t<!-- <view class=\"\" style=\"position: relative;z-index: 10;\">\r\n\t\t\t\t\t<view class=\"tn-text-lg tn-text-bold\">考研政治 刷题＋背诵</view>\r\n\t\t\t\t\t<view class=\"tn-text-sm\">名师串串香--串烧名师，真香！</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"poster_btn\" @click=\"\" style=\"position: relative;z-index: 10;\">免费刷题</view> -->\r\n\t\t\t</view>\r\n\t\t\t<view class=\"meau tn-flex tn-flex-direction-column tn-flex-row-between\">\r\n\t\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-flex-row-between\" @click.stop=\"showKefu=true\">\r\n\t\t\t\t\t<view class=\"tn-flex tn-flex-col-center\">\r\n\t\t\t\t\t\t<image src=\"../../static/icon/kefu.png\" mode=\"widthFix\" style=\"width: 50rpx;height: 50rpx;\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t<view class=\"tn-text-md tn-text-bold\" style=\"color: #222222;margin-left: 20rpx;\">\r\n\t\t\t\t\t\t\t客服帮助\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tn-icon tn-icon-right\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-flex-row-between\" @click=\"updateManagerfun\">\r\n\t\t\t\t\t<view class=\"tn-flex tn-flex-col-center\">\r\n\t\t\t\t\t\t<image src=\"../../static/icon/version.png\" mode=\"widthFix\" style=\"width: 50rpx;height: 50rpx;\">\r\n\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t<view class=\"tn-text-md tn-text-bold\" style=\"color: #222222;margin-left: 20rpx;\">\r\n\t\t\t\t\t\t\t小程序更新\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tn-icon tn-icon-right\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<tn-popup v-model=\"showKefu\" mode=\"bottom\" :borderRadius=\"40\">\r\n\t\t\t<view class=\"tn-width-full tn-flex tn-flex-col-center tn-text-bold\"\r\n\t\t\t\tstyle=\"width: 750rpx;height: 120rpx;padding: 30rpx;box-sizing: border-box; background: url('../../static/pop_head.png') no-repeat;background-size: 100% 100%;\">\r\n\t\t\t\t客服帮助</view>\r\n\t\t\t<view class=\"tn-width-full\" style=\"\">\r\n\t\t\t\t<view class=\"tn-flex tn-flex-row-center\"\r\n\t\t\t\t\tstyle=\"padding: 30rpx 46rpx 66rpx 46rpx;box-sizing: border-box;\">\r\n\t\t\t\t\t<view style=\"width: 400rpx;height: 400rpx;\">\r\n\t\t\t\t\t\t<image show-menu-by-longpress style=\"width: 400rpx;height: 400rpx;\" :src=\"baseUrl+service_img\"\r\n\t\t\t\t\t\t\tmode=\"\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t\t<tn-popup v-model=\"showUser\" mode=\"center\" width=\"530\" class=\"userPop\">\r\n\t\t\t<view class=\"tn-flex tn-flex-direction-column tn-flex-col-center\" style=\"position: relative;\">\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"tn-width-full tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-between tn-bg-white user_set\">\r\n\t\t\t\t\t<button class=\"user_avatar\" open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\">\r\n\t\t\t\t\t\t<view style=\"width: 100%;height: 100%;border-radius: 50%;\" v-if=\"avatar\">\r\n\t\t\t\t\t\t\t<image :src=\"baseUrl+avatar\" mode=\"aspectFit\" style=\"width: 100%;height: 100%;\">\r\n\t\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"width: 100%;height: 100%;border-radius: 50%;\"\r\n\t\t\t\t\t\t\tclass=\"tn-flex tn-flex-col-center tn-flex-row-center\" v-else>\r\n\t\t\t\t\t\t\t<text class=\"tn-icon-add\" style=\"font-size: 38rpx;color: #666666;\"></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<view class=\"user_name_warp tn-flex tn-flex-col-center tn-flex-row-between\">\r\n\t\t\t\t\t\t<view class=\"tn-flex tn-flex-direction-column tn-flex-col-center\" style=\"margin-right: 50rpx;\">\r\n\t\t\t\t\t\t\t<image src=\"../../static/icon/user.png\" mode=\"widthFix\" style=\"width: 52rpx;height: 52rpx;\">\r\n\t\t\t\t\t\t\t</image>\r\n\t\t\t\t\t\t\t<text style=\"font-size: 22rpx;\">昵称</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<input type=\"nickname\" v-model=\"nick\" placeholder=\"请输入昵称\" @blur=\"bindblur\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"submit\" @click.stop=\"submitUser()\">立即授权</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<image src=\"../../static/icon/close.png\" mode=\"widthFix\"\r\n\t\t\t\t\tstyle=\"width: 100rpx;height: 100rpx;margin-top: 40rpx;\" @click.stop=\"showUser=false\"></image>\r\n\t\t\t</view>\r\n\t\t</tn-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport shareMixin from \"@/mixins/shareMixin.js\"\r\n\texport default {\r\n\t\tmixins: [shareMixin],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbaseUrl: this.$config.baseUrl,\r\n\t\t\t\tshowKefu: false,\r\n\t\t\t\tshowUser: false,\r\n\t\t\t\tnick: '',\r\n\t\t\t\tavatar: '',\r\n\t\t\t\tuserForm: {\r\n\t\t\t\t\tnick: \"\",\r\n\t\t\t\t\tavatar: ''\r\n\t\t\t\t},\r\n\t\t\t\tservice_img: '',\r\n\t\t\t\tshare_back: '',\r\n\t\t\t\tuserInfo: {}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.getsysconfig()\r\n\t\t\tthis.getUserInfo()\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\twx.showShareMenu({ // 微信小程序分享\r\n\t\t\t\twithShareTicket: true,\r\n\t\t\t\tmenus: ['shareAppMessage', 'shareTimeline']\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\ttoShare() {\r\n\t\t\t\tif (this.userInfo.exam_status == 1) {\r\n\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\turl: \"/pages/quest/quest\"\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: \"./share\"\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttoCode() {\r\n\t\t\t\tif (this.userInfo.recite_status == 1) {\r\n\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\turl: \"/pages/rember/rember\"\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: \"./code\"\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tbindblur(event) {\r\n\t\t\t\tthis.nick = event.target.value;\r\n\t\t\t},\r\n\t\t\tsubmitUser() {\r\n\t\t\t\tthis.$http.post(this.$api.setUser, {\r\n\t\t\t\t\tavatar: this.avatar,\r\n\t\t\t\t\tnick: this.nick\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.getUserInfo();\r\n\t\t\t\t\t\tthis.showUser = false\r\n\t\t\t\t\t\tthis.nick = \"\"\r\n\t\t\t\t\t\tthis.avatar = \"\"\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonChooseAvatar(res) {\r\n\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\turl: this.$config.baseUrl + '/api/upload/upload',\r\n\t\t\t\t\tfilePath: res.detail.avatarUrl,\r\n\t\t\t\t\tname: 'file',\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\t'Authorization': uni.getStorageSync('TOKEN'),\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: (uploadFileRes) => {\r\n\t\t\t\t\t\tthis.avatar = JSON.parse(uploadFileRes.data).data.src\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetUserInfo() {\r\n\t\t\t\tthis.$http.post(this.$api.getUserInfo, {}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tthis.userInfo = res.data\r\n\t\t\t\t\t\tthis.userForm.nick = res.data.nick\r\n\t\t\t\t\t\tif (res.data.avatar) {\r\n\t\t\t\t\t\t\tthis.userForm.avatar = this.baseUrl + res.data.avatar\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.setStorageSync('userinfo', res.data)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetsysconfig() { // 获取系统配置\r\n\t\t\t\tthis.$http.post(this.$api.systemData, {}).then(res => {\r\n\t\t\t\t\tif (res.code == 200) {\r\n\t\t\t\t\t\tlet datas = res.data;\r\n\t\t\t\t\t\tthis.service_img = datas.config.service_img\r\n\t\t\t\t\t\tthis.share_back = datas.config.share_back;\r\n\t\t\t\t\t\tuni.setStorageSync('systemData', datas)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tupdateManagerfun() { // 更新\r\n\t\t\t\tconst updateManager = uni.getUpdateManager();\r\n\t\t\t\tupdateManager.onCheckForUpdate(res => {\r\n\t\t\t\t\tif (res.hasUpdate) {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\tcontent: '新版本已经准备好，是否重启应用？',\r\n\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\tconfirmText: '确定',\r\n\t\t\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\tupdateManager.onUpdateReady(function(res) {\r\n\t\t\t\t\t\t\t\t\t\tupdateManager.applyUpdate();\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tupdateManager.onUpdateFailed(res => { // 新版本下载失败的回调\r\n\t\t\t\t\t\t\t\t\t\t// 新版本下载失败，提示用户删除后通过冷启动重新打开\r\n\t\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\t\tcontent: '下载失败，请删除当前小程序后重新打开',\r\n\t\t\t\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\t\t\t\tconfirmText: '知道了'\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$tools.toast('当前已经是最新版本')\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.star_l {\r\n\t\twidth: 120rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder-radius: 24rpx;\r\n\t\tbackground-color: #DAE9FF;\r\n\t}\r\n\r\n\t.star_r {\r\n\t\twidth: 120rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder-radius: 24rpx;\r\n\t\tbackground-color: #DDFFF5;\r\n\t}\r\n\r\n\t.submit {\r\n\t\twidth: 422rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 60rpx;\r\n\t\tbackground: #3775F6;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 32rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.user_name_warp {\r\n\t\twidth: 422rpx;\r\n\t\theight: 120rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder: 1px solid #7270FF;\r\n\t\tpadding: 0rpx 36rpx;\r\n\t}\r\n\r\n\t.user_avatar {\r\n\t\twidth: 140rpx;\r\n\t\theight: 140rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tpadding: 0rpx;\r\n\t\toverflow: hidden;\r\n\t\tbackground-color: #FFFFFF;\r\n\t}\r\n\r\n\t.user_set {\r\n\t\tpadding: 50rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\theight: 552rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tbackground: url('../../static/pop_c_bg.png') no-repeat;\r\n\t\tbackground-size: 100% auto;\r\n\t}\r\n\r\n\t.userPop /deep/ .tn-popup__content__center_box {\r\n\t\tbackground-color: transparent !important;\r\n\t}\r\n\r\n\t.meau {\r\n\t\twidth: 690rpx;\r\n\t\theight: 236rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tmargin-top: 20rpx;\r\n\t\tpadding: 40rpx 30rpx;\r\n\t}\r\n\r\n\t.poster_btn {\r\n\t\twidth: 152rpx;\r\n\t\theight: 56rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbackground: linear-gradient(180deg, #EBC877 0%, #FCAF92 100%);\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.poster {\r\n\t\twidth: 690rpx;\r\n\t\theight: 160rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 36rpx 36rpx 36rpx 120rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.posterbg {\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tz-index: 0;\r\n\t}\r\n\r\n\t.body {\r\n\t\twidth: 100%;\r\n\t\tpadding: 0rpx 30rpx 30rpx 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.tips {\r\n\t\twidth: 120rpx;\r\n\t\theight: 36rpx;\r\n\t\tline-height: 38rpx;\r\n\t\tborder-radius: 20rpx 20rpx 20rpx 0rpx;\r\n\t\tbackground: linear-gradient(180deg, #FF9C4C 0%, #FFB200 100%);\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\ttext-align: center;\r\n\t\tmargin-left: 10rpx;\r\n\t\tfont-weight: normal;\r\n\t}\r\n\r\n\t.vib_l {\r\n\t\tbackground: linear-gradient(180deg, #E7BE6C 0%, #C8A956 100%);\r\n\t}\r\n\r\n\t.vib_r {\r\n\t\tbackground: linear-gradient(180deg, #FCAF92 0%, #FF7076 100%);\r\n\t}\r\n\r\n\t.vi_l {\r\n\t\tbackground: url(\"../../static/vip_l.png\") no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t}\r\n\r\n\t.vi_r {\r\n\t\tbackground: url(\"../../static/vip_r.png\") no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t}\r\n\r\n\t.vip_warp {\r\n\t\twidth: 690rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-radius: 20rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tmargin-top: 50rpx;\r\n\r\n\t\t.vip_inner {\r\n\t\t\twidth: 306rpx;\r\n\t\t\theight: 230rpx;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tpadding: 24rpx 24rpx 36rpx 24rpx;\r\n\r\n\t\t\t.vi_t {\r\n\t\t\t\tcolor: #222222;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\r\n\t\t\t.vi_time {\r\n\t\t\t\tcolor: #222222;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\theight: 30rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.vi_btn {\r\n\t\t\t\twidth: 152rpx;\r\n\t\t\t\theight: 56rpx;\r\n\t\t\t\tborder-radius: 50rpx;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tmargin-top: 28rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.name {\r\n\t\tfont-size: 18px;\r\n\t}\r\n\r\n\t.name_warp {\r\n\t\twidth: 250rpx;\r\n\t\tmargin-left: 15rpx;\r\n\t}\r\n\r\n\t.set_btn {\r\n\t\twidth: 282rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 40rpx;\r\n\t\tbackground-color: #5552FF;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\r\n\t.header {\r\n\t\twidth: 100%;\r\n\t\tbackground: linear-gradient(180deg, #F1F0FF 71%, rgba(255, 255, 255, 0) 99%) no-repeat;\r\n\t\tbackground-size: 100% 304rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 30rpx 30rpx 20rpx 30rpx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=style&index=0&id=dcbcfe34&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mine.vue?vue&type=style&index=0&id=dcbcfe34&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753980402646\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}